<template>
  <div class="screen-container" >
    <div class="bg-layer bg-layer-1"></div>
    <div class="bg-layer bg-layer-2"></div>
    <div class="bg-layer bg-layer-3"></div>
    <!-- 地图容器，修改ID避免重复 -->
    <div id="player" class="map-container" ></div>
    <!-- 拆分为三个独立的容器 -->
    <!-- 左侧区域容器 -->
    <div class="left-container">
      <div class="left-section">
        <div class="time-weather">
          <div class="time-date">
            <div class="time">{{ currentTime }}</div>
            <div class="date">{{ currentDate }}</div>
          </div>
          <div class="divider"></div>
          <div class="weather">
            <img class="weather-icon" :src="getAssetsFile('sunny.png')" alt="天气" />
            <img class="weather-icon1" :src="getAssetsFile('wendu.png')" alt="温度" />
            <span class="temperature">17℃</span>
          </div>
        </div>

        <!-- 左右布局容器 -->
        <div class="content-layout">
          <!-- 上 -->
          <div class="content-layout-header">
            <div class="weather-info-bar">
              <div class="current-weather">
                <span class="weather-label">当前天气</span>
                <div class="city-info">
                  <img class="location-icon" src="@/assets/images/home/<USER>" alt="">
                  <span class="city-name">太原市</span>
                </div>
              </div>

              <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线">

              <div class="weather-detail">
                <div class="weather-icon-large">
                  <img src="@/assets/images/home/<USER>" alt="大雨" />
                </div>
                <div class="weather-data">
                  <div class="weather-type">
                    <span class="weather-name">大雨</span>
                    <span class="weather-temp">19优</span>
                  </div>
                  <div class="wind-info">
                    <span>东风 </span>
                    <span> 2级</span>
                  </div>
                </div>
              </div>

              <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线">

              <div class="weather-forecast">
                <div class="forecast-item">
                  <div class="forecast-title">24h累计降雨</div>
                  <div class="forecast-value">
                    <span class="forecast-value-num">50</span>
                    <span class="forecast-value-unit">mm</span>
                  </div>
                </div>
                <div class="forecast-item">
                  <div class="forecast-title">未来2h降雨</div>
                  <div class="forecast-value1">
                    <span class="forecast-value-num">20</span>
                    <span class="forecast-value-unit">mm</span>
                  </div>
                </div>
              </div>

              <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线">

              <div class="publish-info">
                <span class="publish-label">预报发布</span>
                <div class="publish-times">
                  <div class="time-item">开始时间: 20:50:00</div>
                  <div class="time-item">结束时间: 18:00:00</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 中 -->
          <div class="content-layout-middle">
            <div class="content-layout-middle-header">
              <div class="title">易涝点分布</div>
            </div>
            <div class="content-layout-middle-bottom">
              <div class="bottom-item-left">
                <div class="left-top">
                  <span class="stats-title">所有类别统计</span>
                  <span class="stats-number">68</span>
                  <span class="stats-unit">个</span>
                </div>

                <div class="stats-icons">
                  <div class="icon-item">
                    <div class="icon-circle">25</div>
                    <div class="icon-text">重点积水点</div>
                  </div>
                  <div class="icon-item">
                    <div class="icon-circle1">25</div>
                    <div class="icon-text">地下通道</div>
                  </div>
                  <div class="icon-item">
                    <div class="icon-circle2">25</div>
                    <div class="icon-text">关键节点</div>
                  </div>
                </div>
              </div>
              <div class="bottom-item-middle">
                <div class="left-top">
                  <span class="stats-title">报警总数统计</span>
                  <span class="stats-number">28</span>
                  <span class="stats-unit">个</span>
                </div>

                <div class="stats-icons">
                  <div class="icon-item">
                    <div class="icon-circle">25</div>
                    <div class="icon-text">重点积水点</div>
                  </div>
                  <div class="icon-item">
                    <div class="icon-circle1">25</div>
                    <div class="icon-text">地下通道</div>
                  </div>
                  <div class="icon-item">
                    <div class="icon-circle2">25</div>
                    <div class="icon-text">关键节点</div>
                  </div>
                </div>
              </div>
              <div class="bottom-item-right">
                <div class="problem-title">今日问题数量 (个)</div>
                <div class="problem-number">
                  <div>168</div>
                  <img class="arrow-up-with-line" src="@/assets/images/home/<USER>" alt="箭头"></img>
                </div>
              </div>
            </div>
          </div>
          <!-- 下 -->
          <div class="content-layout-bottom">
            <div class="bottom-item-left">
              <div class="left-h">
                <div class="h-header">
                  <div class="title">潜在受灾对象分布</div>
                </div>
                <div ref="horizontalBarChart" class="h-bottom"></div>
              </div>
              <div class="left-b">
                <div class="h-header">
                  <div class="title">潜在受灾风险分布</div>
                </div>
                <div ref="riskPieChart" class="h-bottom">

                </div>
              </div>
            </div>
            <div class="bottom-item-right">
              <div class="right-h">
                <div class="h-header">
                  <div class="title">潜在受灾对象分布</div>
                </div>
                <div class="right-h-content">
                  <div class="data-indicators">
                    <div class="indicator-group left-indicators">
                      <div class="indicator-item">
                        <div class="indicator-icon">
                          <img src="@/assets/images/home/<USER>" alt="地下通道" />
                        </div>
                        <div class="indicator-data">

                          <div class="indicator-label">隧道涵洞</div>
                          <div class="indicator-value">16<span>处</span></div>
                        </div>
                      </div>
                      <div class="indicator-item">
                        <div class="indicator-icon">
                          <img src="@/assets/images/home/<USER>" alt="地下通道" />
                        </div>
                        <div class="indicator-data">
                          <div class="indicator-label">影响户数</div>
                          <div class="indicator-value">119<span>户</span></div>
                        </div>
                      </div>
                    </div>
                    <div class="indicator-group right-indicators">

                      <div class="indicator-item">
                        <div class="indicator-icon">
                          <img src="@/assets/images/home/<USER>" alt="地下通道" />
                        </div>
                        <div class="indicator-data">
                          <div class="indicator-label">建筑小区</div>
                          <div class="indicator-value">68.3<span>km</span></div>
                        </div>
                      </div>
                      <div class="indicator-item">
                        <div class="indicator-icon">
                          <img src="@/assets/images/home/<USER>" alt="地下通道" />
                        </div>
                        <div class="indicator-data">
                          <div class="indicator-label">影响人口</div>
                          <div class="indicator-value">326<span>万人</span></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="right-b">
                <div class="h-header">
                  <div class="title">应急能力情况</div>
                </div>
                <div class="right-b-content">
                  <div class="emergency-stats">
                    <div class="stat-row">
                      <div class="stat-box">
                        <div class="stat-title">巡查人员总数 (位)</div>
                        <div class="stat-content">
                          <div class="stat-number">220</div>
                          <div class="stat-details">
                            <div class="stat-item">
                              <span class="stat-label">男</span>
                              <span class="stat-value male">182</span>
                            </div>
                            <div class="stat-item">
                              <span class="stat-label">女</span>
                              <span class="stat-value female">38</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="stat-box">
                        <div class="stat-title">在线人员数量 (位)</div>
                        <div class="stat-content">
                          <div class="stat-number">220</div>
                          <div class="stat-details">
                            <div class="stat-progress">
                              <div class="progress-label">在线率</div>
                              <div class="progress-value">58.6%</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="stat-row">
                      <div class="stat-box1">
                        <div class="stat-title">应急队伍数量 (人)</div>
                        <div class="stat-content">
                          <div class="stat-number">100</div>
                          <div class="stat-details">
                            <div class="stat-item">
                              <span class="stat-label">抢险队伍</span>
                              <span class="stat-value male">110</span>
                            </div>
                            <div class="stat-item">
                              <span class="stat-label">其他队伍</span>
                              <span class="stat-value other">100</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="stat-box1">
                        <div class="stat-title">应急队伍数量 (人)</div>
                        <div class="stat-content">
                          <div class="stat-number">100</div>
                          <div class="stat-details">
                            <div class="stat-item">
                              <span class="stat-label">抢险队伍</span>
                              <span class="stat-value male">110</span>
                            </div>
                            <div class="stat-item">
                              <span class="stat-label">其他队伍</span>
                              <span class="stat-value other">100</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间区域容器 -->
    <div class="middle-container">
      <div class="middle-section">
        <!-- 标题 -->
        <div class="section-title">内涝安全预警监测综合驾驶舱系统</div>
        <!-- 导航按钮 -->
        <div class="nav-buttons">
          <div
            v-for="(btn, index) in navButtons"
            :key="index"
            :class="['nav-button', { active: activeNavButton === index }]"
            @click="activeNavButton = index"
          >
           <div class="nav-button-text"> {{ btn.text }}</div>
          </div>
        </div>
        <!-- 中间内容 -->
         <div ></div>
      </div>
    </div>

    <!-- 右侧区域容器 -->
    <div class="right-container">
      <div class="right-section">
        <!-- 用户信息和返回门户 -->
        <div class="user-portal-container">
          <div class="user-info">
            <img class="user-icon" src="https://picsum.photos/200/300" alt="用户" />
            <span class="username">管理员</span>
          </div>
          <div class="portal-back">
            <img class="portal-icon" src="https://picsum.photos/200/300" alt="门户" />
            <span class="portal-text">返回门户</span>
          </div>

        </div>
        <!-- 右侧其他内容 -->
        <div class="full-width-container">
          <div class="left-half">
            <div class="left-top">
              <div class="h-header">
                <div class="title">排水系统概况</div>
              </div>
              <div class="content">
                <div class="drainage-system-overview">
                   <div class="overview-cards">
                    <div v-for="(item,index) in cardData"  :key="index" class="overview-card">
                      <div class="card-icon">
                        <img :src=getAssetsFile(item.icon) alt="雨污水管道总量" />
                      </div>
                      <div class="card-data">
                        <div class="card-label">{{ item.label }}</div>
                        <div class="card-value">{{ item.value }}<span class="card-unit">{{ item.unit }}</span></div>
                      </div>

                    </div>

                  </div>
                </div>
              </div>
            </div>

            <div class="left-bottom">
              <div class="h-header">
                <div class="title">潜在受灾风险分布</div>
              </div>
              <div class="monitoring-header-box">
                <div class="monitoring-header">
                  <div class="monitoring-left">河道今日水情</div>
                  <div class="monitoring-right">
                  <div>
                    <img :src="getAssetsFile('jingjie.png')" alt="警戒水位">
                    <div>警戒水位(m)</div>
                  </div>
                  <div>
                    <img :src="getAssetsFile('shishi.png')" alt="实时水位">
                    <div>实时水位</div>
                  </div>
                </div>
              </div>

              </div>
              <div class="content">
                <div ref="riverLevelChart" class="river-chart-container"></div>
              </div>
            </div>
          </div>

          <div class="right-half">
            <div class="right-top">
              <div class="h-header">
                <div class="title">排水系统运行态势</div>
              </div>
              <div class="monitoring-header-box">
                <div class="monitoring-header">今日监测报警情况汇总</div>
              </div>
              <div class="content">
                <div class="monitoring-stations">
                  <div v-for="(item,index) in stationData"  :key="index" class="station-item">
                    <div class="station-name">{{ item.name }}</div>
                    <div class="station-circle " :style="{ backgroundImage: `url(${getAssetsFile(item.icon)})` }">
                      <span class="station-number">{{ item.value }}</span>
                    </div>
                  </div>

                </div>
              </div>
            </div>

            <div class="right-bottom">
              <div class="h-header">
                <div class="title">排水系统运行态势</div>
              </div>
              <div class="monitoring-header-box">
                <div class="monitoring-header">今日监测设备在线情况汇总</div>
              </div>
              <div class="content">
                <!-- 内容区域 -->
                <div class="chart-rows-container">
                  <div class="chart-row">

                    <div ref="chart3DBar1" class="chart-container1"></div>
                  </div>
                  <div class="chart-row">

                    <div ref="chart3DBar2" class="chart-container2"></div>
                  </div>
                  <div class="chart-row">
                    <div ref="chart3DBar3" class="chart-container3"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
// 引入数字孪生js
import * as acapi from '../../assets/ac.min.js'
// 图表引用
const chart3DBar1 = ref(null)
const chart3DBar2 = ref(null)
const chart3DBar3 = ref(null)
let bar3DChart1 = null
let bar3DChart2 = null
let bar3DChart3 = null

const stationData = ref([
  {
    name: '河道水质水量站(处)',
    value: 21,
    icon: 'r-bg7.png',
  },
  {
    name: '管道水位站(处)',
    value: 9,
    icon: 'r-bg8.png',
  },
  {
    name: '泵站(处)',
    value: 16,
    icon: 'r-bg9.png',
  },
  {
    name: '易涝点(处)',
    value: 13,
    icon: 'r-bg10.png',
  },
  {
    name: '排水口水质(站)',
    value: 28,
    icon: 'r-bg11.png',
  },
  {
    name: '污水厂(处)',
    value: 30,
    icon: 'r-bg12.png',
  },
])
const cardData = ref([
  {
    label: '雨污水管数量',
    value: 2000,
    unit: '(km)',
    icon: 'r-bg1.png',
  },
  {
    label: '泵站数量',
    value: 500,
    unit: '(个)',
    icon: 'r-bg2.png',
  },
  {
    label: '排放口数量',
    value: 3000,
    unit: '(个)',
    icon: 'r-bg3.png',
  },
  {
    label: '污水厂数量',
    value: 50,
    unit: '(家)',
    icon: 'r-bg4.png',
  },
  {
    label: '城市内河数量',
    value: 30,
    unit: '(条)',
    icon: 'r-bg5.png',
  },
])
// 获取assets静态资源
const getAssetsFile = (url) => {
  return new URL(`../../assets/images/home/<USER>
}

// 时间和日期
const currentTime = ref('')
const currentDate = ref('')

// 图表引用
const horizontalBarChart = ref(null)
const riskPieChart = ref(null)
const riverLevelChart = ref(null)
let barChart = null
let pieChart = null
let riverChart = null

// 导航按钮
const navButtons = [
  { text: '综合展示' },
  { text: '智能感知' },
  { text: '报警决策' },
  { text: '辅助决策' },
]
const activeNavButton = ref(0)

// 标签页相关
const tabs = [
  { name: '实时', value: 'realtime' },
  { name: '3日', value: '3days' },
  { name: '7日', value: '7days' },
]
const currentTab = ref('realtime')

let timer = null

// 更新时间函数
const updateTime = () => {
  const now = new Date()

  // 格式化时分秒
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  const seconds = now.getSeconds().toString().padStart(2, '0')
  currentTime.value = `${hours}:${minutes}:${seconds}`

  // 格式化年月日
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  currentDate.value = `${year} ${month} ${day}`
}

// 初始化3D柱状图1
const init3DBarChart1 = () => {
  if (chart3DBar1.value) {
    // 如果已存在图表实例，先销毁
    if (bar3DChart1) {
      bar3DChart1.dispose()
    }

    bar3DChart1 = echarts.init(chart3DBar1.value)

    const barWidth = 50
    const colors = []

    // 创建4个渐变色
    for (let i = 0; i < 6; i++) {
      colors.push({
        type: 'linear',
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [
          {
            offset: 0,
            color: '#027095' // 最左边
          }, {
            offset: 0.5,
            color: '#0081A8' // 左边的右边 颜色
          }, {
            offset: 0.5,
            color: '#027095' // 右边的左边 颜色
          }, {
            offset: 1,
            color: '#027095'
          }
        ]
      })
    }

    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}%',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        top: '15%',
        left: '5%',
        right: '5%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['一区', '二区', '三区', '四区', '五区', '六区'],
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          fontSize: 22,
          margin: 20
        }
      },
      yAxis: {
        type: 'value',
        name: '在线率(%)',
        show: false,
        nameTextStyle: {
          color: '#fff',
          fontSize: 14
        },
        max: 100,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          fontSize: 22
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      series: [
        {
          type: 'bar',
          barWidth: barWidth,
          data: [88, 92, 87, 95, 89, 91],
          itemStyle: {
            normal: {
              color: function(params) {
                return colors[params.dataIndex % 6]
              }
            }
          },
          label: {
            show: true,
            position: 'inside',
            color: '#fff',
            fontSize: 14,
            formatter: '{c}'
          }
        },
        {
          z: 2,
          type: 'pictorialBar',
          data: [88, 92, 87, 95, 89, 91],
          symbol: 'diamond',
          symbolOffset: [0, '50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              color: function(params) {
                return colors[params.dataIndex % 6]
              }
            }
          }
        },
        {
          z: 3,
          type: 'pictorialBar',
          symbolPosition: 'end',
          data: [88, 92, 87, 95, 89, 91],
          symbol: 'diamond',
          symbolOffset: [0, '-50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              borderWidth: 0,
              color: function(params) {
                return colors[params.dataIndex % 6].colorStops[0].color
              }
            }
          }
        }
      ]
    }

    bar3DChart1.setOption(option)

    // 立即调整图表大小以适应容器
    bar3DChart1.resize()

    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', () => {
      if (bar3DChart1) {
        bar3DChart1.resize()
      }
    })
  }
}

// 初始化3D柱状图2
const init3DBarChart2 = () => {
  if (chart3DBar2.value) {
    // 如果已存在图表实例，先销毁
    if (bar3DChart2) {
      bar3DChart2.dispose()
    }

    bar3DChart2 = echarts.init(chart3DBar2.value)

    const barWidth = 50
    const colors = []

    // 创建绿色渐变
    for (let i = 0; i < 6; i++) {
      colors.push({
        type: 'linear',
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [
          {
            offset: 0,
            color: '#575757' // 最左边
          }, {
            offset: 0.5,
            color: '#575757' // 左边的右边 颜色
          }, {
            offset: 0.5,
            color: '#464E53' // 右边的左边 颜色
          }, {
            offset: 1,
            color: '#575757'
          }
        ]
      })
    }

    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}%',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        top: '15%',
        left: '5%',
        right: '5%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['一区', '二区', '三区', '四区', '五区', '六区'],
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          fontSize: 22,
          margin: 20
        }
      },
      yAxis: {
        type: 'value',
        name: '在线率(%)',
        show: false,
        nameTextStyle: {
          color: '#fff',
          fontSize: 14
        },
        max: 100,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          fontSize: 22
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      series: [
        {
          type: 'bar',
          barWidth: barWidth,
          data: [88, 92, 87, 95, 89, 91],
          itemStyle: {
            normal: {
              color: function(params) {
                return colors[params.dataIndex % 6]
              }
            }
          },
          label: {
            show: true,
            position: 'inside',
            color: '#fff',
            fontSize: 14,
            formatter: '{c}'
          }
        },
        {
          z: 2,
          type: 'pictorialBar',
          data: [88, 92, 87, 95, 89, 91],
          symbol: 'diamond',
          symbolOffset: [0, '50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              color: function(params) {
                return colors[params.dataIndex % 6]
              }
            }
          }
        },
        {
          z: 3,
          type: 'pictorialBar',
          symbolPosition: 'end',
          data: [88, 92, 87, 95, 89, 91],
          symbol: 'diamond',
          symbolOffset: [0, '-50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              borderWidth: 0,
              color: function(params) {
                return colors[params.dataIndex % 6].colorStops[0].color
              }
            }
          }
        }
      ]
    }

    bar3DChart2.setOption(option)

    // 立即调整图表大小以适应容器
    bar3DChart2.resize()

    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', () => {
      if (bar3DChart2) {
        bar3DChart2.resize()
      }
    })
  }
}

// 初始化3D柱状图3
const init3DBarChart3 = () => {
  if (chart3DBar3.value) {
    // 如果已存在图表实例，先销毁
    if (bar3DChart3) {
      bar3DChart3.dispose()
    }

    bar3DChart3 = echarts.init(chart3DBar3.value)

    const barWidth = 50
    const colors = []

    // 创建金色/橙色渐变
    for (let i = 0; i < 6; i++) {
      colors.push({
        type: 'linear',
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [
          {
            offset: 0,
            color: '#EB3D30' // 最左边
          }, {
            offset: 0.5,
            color: '#EB3D30' // 左边的右边 颜色
          }, {
            offset: 0.5,
            color: '#BC3933' // 右边的左边 颜色
          }, {
            offset: 1,
            color: '#EB3D30'
          }
        ]
      })
    }

    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}%',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        top: '15%',
        left: '5%',
        right: '5%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['一区', '二区', '三区', '四区', '五区', '六区'],
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          fontSize: 22,
          margin: 20
        }
      },
      yAxis: {
        type: 'value',
        name: '在线率(%)',
        show: false,
        nameTextStyle: {
          color: '#fff',
          fontSize: 14
        },
        max: 100,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          fontSize: 14
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      series: [
        {
          type: 'bar',
          barWidth: barWidth,
          data: [88, 92, 87, 95, 89, 91],
          itemStyle: {
            normal: {
              color: function(params) {
                return colors[params.dataIndex % 6]
              }
            }
          },
          label: {
            show: true,
            position: 'inside',
            color: '#fff',
            fontSize: 14,
            formatter: '{c}'
          }
        },
        {
          z: 2,
          type: 'pictorialBar',
          data: [88, 92, 87, 95, 89, 91],
          symbol: 'diamond',
          symbolOffset: [0, '50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              color: function(params) {
                return colors[params.dataIndex % 6]
              }
            }
          }
        },
        {
          z: 3,
          type: 'pictorialBar',
          symbolPosition: 'end',
          data: [88, 92, 87, 95, 89, 91],
          symbol: 'diamond',
          symbolOffset: [0, '-50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              borderWidth: 0,
              color: function(params) {
                return colors[params.dataIndex % 6].colorStops[0].color
              }
            }
          }
        }
      ]
    }

    bar3DChart3.setOption(option)

    // 立即调整图表大小以适应容器
    bar3DChart3.resize()

    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', () => {
      if (bar3DChart3) {
        bar3DChart3.resize()
      }
    })
  }
}

// 初始化河道水情柱状折线图
const initRiverLevelChart = () => {
  if (riverLevelChart.value) {
    // 如果已存在图表实例，先销毁
    if (riverChart) {
      riverChart.dispose()
    }

    riverChart = echarts.init(riverLevelChart.value)

    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      legend: {
        data: ['降雨量', '警戒水位', '实时水位'],
        textStyle: {
          color: '#fff',
          fontSize: 14
        },
        right: '10%',
        top: '0'
      },
      xAxis: [
        {
          type: 'category',
          data: ['一号闸', '二号闸', '三号闸', '四号闸', '五号闸', '六号闸'],
          axisPointer: {
            type: 'shadow'
          },
          axisLine: {
            lineStyle: {
              color: '#0a5c94'
            }
          },
          axisLabel: {
            color: '#fff',
            fontSize: 22,
            margin: 20
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '降雨量',
          min: 0,
          max: 100,
          interval: 20,
          axisLabel: {
            color: '#fff',
            fontSize: 22,
            formatter: '{value} mm',

          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#0a5c94'
            }
          },
          nameTextStyle: {
            color: '#fff',
            fontSize: 22,
            padding: [0, 0, 10, -75] // 添加左侧padding为负值，使标签向左移动
          }
        },
        {
          type: 'value',
          name: '水位',
          min: 0,
          max: 5,
          interval: 1,
          axisLabel: {
            color: '#fff',
            fontSize: 22,
            formatter: '{value} m'
          },
          splitLine: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#0a5c94'
            }
          },
          nameTextStyle: {
            color: '#fff',
            fontSize: 22,
            padding: [0, 0, 10, 50] // 添加左侧padding为负值，使标签向左移动
          }
        }
      ],
      series: [
        {
          name: '降雨量',
          type: 'bar',
          barWidth: 40,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(0, 136, 212, 1)' },
              { offset: 1, color: 'rgba(0, 136, 212, 0.3)' }
            ])
          },
          data: [8, 12, 45, 30, 15, 5]
        },
        {
          name: '警戒水位',
          type: 'line',
          yAxisIndex: 1,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#ff4646'
          },
          lineStyle: {
            width: 2,
            type: 'dashed'
          },
          data: [2.8, 2.8, 2.8, 2.8, 2.8, 2.8]
        },
        {
          name: '实时水位',
          type: 'line',
          yAxisIndex: 1,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#00d0ff'
          },
          lineStyle: {
            width: 2
          },
          data: [1.5, 1.8, 2.3, 2.5, 2.2, 2.0]
        }
      ]
    }

    riverChart.setOption(option)

    // 立即调整图表大小以适应容器
    riverChart.resize()

    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', () => {
      if (riverChart) {
        riverChart.resize()
      }
    })
  }
}

// 连接数字孪生配置
const options = ref({
  domId: 'player', // 修改为新的ID
  apiOptions: {
    onReady: function () {
      console.info('此时可以调API了')
      // 设置鼠标拾取掩码，允许鼠标点击、移动和悬停事件
    }
  },
})

const api = ref(null)
// 太原数字孪生内网地址
const host = ref('*************:8008/dts')
// const host = ref("************:8080");
// 组件挂载时启动定时器和初始化屏幕适配
onMounted(() => {
  updateTime() // 立即执行一次
  timer = setInterval(updateTime, 1000) // 每秒更新一次

  // 延时初始化图表，确保DOM已完全渲染
  setTimeout(() => {
    initHorizontalBarChart()
    initRiskPieChart()
    init3DBarChart1()
    init3DBarChart2()
    init3DBarChart3()
    initRiverLevelChart() // 初始化河道水情图表
  }, 300)

  // 修改这里，接入地图配置
  try {
    // 确保先引入了ac.min.js
    if (typeof acapi !== 'undefined') {
      // 创建数字孪生平台实例
      console.log('加载飞渡')
      api.value = new DigitalTwinPlayer(host.value, options.value)
      console.log('数字孪生平台初始化成功')

      // 不在这里设置鼠标交互，而是在onReady回调中设置
    } else {
      console.error('ac.min.js未正确加载，请检查引入路径')
    }
  } catch (error) {
    console.error('数字孪生平台初始化失败:', error)
  }
})

// 初始化横向柱状图
const initHorizontalBarChart = () => {
  if (horizontalBarChart.value) {
    // 如果已存在图表实例，先销毁
    if (barChart) {
      barChart.dispose()
    }

    barChart = echarts.init(horizontalBarChart.value)

    const option = {
      backgroundColor: 'transparent',
      grid: {
        top: '0',
        left: '0',
        right: '0',
        bottom: '2%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        max: 250,
        min: 0,
        interval: 25,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed',
          },
        },
        axisLabel: {
          show: true,
          color: '#fff',
          fontSize: 12,
        },
      },
      yAxis: {
        type: 'category',
        data: [
          '小店区',
          '迎泽区',
          '万柏林区',
          '尖草坪区',
          '杏花岭区',
          '万柏林区',
          '清徐县',
          '阳曲县',
          '娄烦县',
          '古交市',
        ],
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#fff',
          fontSize: 20,
          margin: 20,
        },
      },
      series: [
        {
          // 背景柱状图
          type: 'bar',
          barWidth: 12,
          barGap: '-100%',
          z: 1,
          data: [250, 250, 250, 250, 250, 250, 250, 250, 250, 250],
          itemStyle: {
            color: 'rgba(32, 78, 132, 0.3)',
            borderRadius: [0, 6, 6, 0],
          },
          silent: true,
        },
        {
          type: 'bar',
          data: [250, 200, 180, 170, 160, 150, 140, 130, 120, 100],
          barWidth: 12,
          z: 2,
          itemStyle: {
            color: function (params) {
              // 创建渐变色 - 使用蓝色渐变
              return new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 1, color: 'rgba(48, 198, 220, 1)' },
              ])
            },
            borderRadius: [0, 6, 6, 0],
          },
          label: {
            show: false,
          },
        },
      ],
    }

    barChart.setOption(option)

    // 立即调整图表大小以适应容器
    barChart.resize()

    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', () => {
      if (barChart) {
        barChart.resize()
      }
    })
  }
}

// 初始化风险饼图
const initRiskPieChart = () => {
  if (riskPieChart.value) {
    // 如果已存在图表实例，先销毁
    if (pieChart) {
      pieChart.dispose()
    }

    pieChart = echarts.init(riskPieChart.value)

    const option = {
      backgroundColor: 'transparent',
      color: ['#ff3838', '#ff9d00', '#ffde00', '#00b0ff'],
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}个 ({d}%)',
      },
      legend: {
        orient: 'vertical',
        right: '10%',
        top: 'center',
        itemGap: 20,
        // 修改图例的宽高
        itemWidth: 20,
        itemHeight: 20,
        icon: 'rect',
        textStyle: {
          color: '#fff',
          fontSize: 24,
          padding: [0, 0, 0, 40],
        },
        formatter: function (name) {
          let total = 0
          let targetValue
          option.series[0].data.forEach(function (value) {
            total += value.value
            if (value.name === name) {
              targetValue = value.value
            }
          })
          return name + '            ' + targetValue + ' 个'
        },
        data: ['一级风险', '二级风险', '三级风险', '四级风险'],
      },
      series: [
        {
          type: 'pie',
          radius: ['50%', '75%'],
          center: ['25%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderColor: 'rgba(0, 0, 0, 0.1)',
            borderWidth: 2,
          },
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: 16, name: '一级风险' },
            { value: 22, name: '二级风险' },
            { value: 32, name: '三级风险' },
            { value: 33, name: '四级风险' },
          ],
        },
        {
          type: 'pie',
          radius: ['30%', '35%'],
          center: ['25%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'center',
            formatter: function () {
              return ['{text|风险点个数}', '{number|103}'].join('\n\n')
            },
            rich: {
              text: {
                color: '#fff',
                fontSize: 16,
                fontWeight: 'normal',
                lineHeight: 20,
              },
              number: {
                color: '#fff',
                fontSize: 28,
                fontWeight: 'bold',
                lineHeight: 30,
              },
            },
          },
          labelLine: {
            show: false,
          },
          data: [{ value: 103, name: '风险总个数' }],
          itemStyle: {
            color: 'transparent',
          },
        },
      ],
    }

    pieChart.setOption(option)

    // 立即调整图表大小以适应容器
    pieChart.resize()

    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', () => {
      if (pieChart) {
        pieChart.resize()
      }
    })
  }
}

  // 组件卸载时清除定时器和屏幕适配事件监听
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
  // 移除窗口大小变化监听器
  window.removeEventListener('resize', () => {})

  // 销毁图表实例
  if (barChart) {
    barChart.dispose()
    barChart = null
  }
  if (pieChart) {
    pieChart.dispose()
    pieChart = null
  }
  if (riverChart) {
    riverChart.dispose()
    riverChart = null
  }
  if (bar3DChart1) {
    bar3DChart1.dispose()
    bar3DChart1 = null
  }
  if (bar3DChart2) {
    bar3DChart2.dispose()
    bar3DChart2 = null
  }
  if (bar3DChart3) {
    bar3DChart3.dispose()
    bar3DChart3 = null
  }

  // 销毁DTS实例
  if (api.value) {
    api.value = null
  }
})
</script>

<style lang="scss" scoped>
// 基础变量和混合

// 屏幕容器
.screen-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;

  // 全适配模式下的居中处理
  .full-scale & {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 背景层
.bg-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  pointer-events: none; // 背景层不接收鼠标事件
  z-index: 1; // 确保背景层在地图之上
}

.bg-layer-1 {
  background-image: url("@/assets/images/home/<USER>");
  z-index: 1;
}

.bg-layer-2 {
  background-image: url("@/assets/images/home/<USER>");
  z-index: 2;
}

.bg-layer-3 {
  background-image: url("@/assets/images/home/<USER>");
  z-index: 3;
}

// 地图容器样式
.map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0; // 确保地图在最底层
  pointer-events: auto; // 确保鼠标事件能够到达地图
}

// 拆分为三个独立的容器
.left-container,
.middle-container,
.right-container {
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 10; // 确保UI在最上层
  pointer-events: none; // 容器本身不接收鼠标事件，允许事件穿透到地图
}

.left-container {
  left: 0;
  width: 25%;
}

.middle-container {
  left: 45%;
  // width: 40%;
  height: auto;
  // top: 2%;
}

.right-container {
  left: 75%;
  width: 25%;
}

/* 三栏布局 */
.left-section{
  width: 100%;
  height: 100%;
  padding: vh(40) vw(120) vh(50) vw(100);
  pointer-events: auto; // 恢复鼠标事件，使UI元素可交互
}
.right-section {
  width: 100%;
  height: 100%;
  padding: vh(40) vw(100) vh(50) vw(150);
  pointer-events: auto; // 恢复鼠标事件，使UI元素可交互
}

.middle-section {
  // width: 100%;
  height: 250px;
  padding: vh(5);
  pointer-events: auto; // 恢复鼠标事件，使UI元素可交互
}

/* 时间和天气样式 */
.time-weather {
  /**1920 */
  display: flex;
  align-items: center;
  color: #fff;
  margin-left: vw(40);
}

.time-date {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.time {
  font-size: vh(24);
}

.date {
  font-size: vh(14);
  margin-right: vw(2);
}

.divider {
  margin: 0 vw(20);
  width: vw(1);
  height: vh(30);
  background-color: #3a607c;
}

.weather {
  display: flex;
  align-items: center;
}

.weather-icon {
  width: vw(24);
  height: vh(24);
  margin-right: vw(10);
}

.weather-icon1 {
  width: vw(24);
  height: vh(22);
}

.temperature {
  font-size: vh(24);
}

/* 中间区域标题 */
.section-title {
  font-family: YouSheBiaoTiHei;
  font-size: vh(80);
  color: #ffffff;
  text-align: center;
  font-style: normal;
}

/* 导航按钮 */
.nav-buttons {
  display: flex;
  justify-content: center;
  gap: vw(20);
  margin-top: vh(60);
}

.nav-button {
  width: vw(200);
  height: vh(80);
  background-image: url("@/assets/images/home/<USER>");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  padding-top: vh(14);
  font-family: JiangChengXieHei;
  color: #fff;
  font-size: vh(24);
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-button.active {
  background-image: url("@/assets/images/home/<USER>");
  color: #fff;
  font-weight: bold;
}

/* 用户信息和返回门户样式 */
.user-portal-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: #fff;
  margin-right: vw(40);
  margin-top: vh(10);
  gap: vw(20);
}

.user-info,
.portal-back {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: vw(5);
}

.user-icon,
.portal-icon {
  width: vw(24);
  height: vh(24);
  margin-right: vw(5);
}

.username,
.portal-text {
  font-size: vh(14);
}

/* 左右布局容器样式 */
.content-layout {
  width: 100%;
  height: calc(100% - 50px);
  /* 减去顶部时间天气区域的高度 */
  display: flex;
  flex-direction: column;
  margin-top: vh(10);
  gap: vw(1);
}

.content-layout-header {
  width: 100%;
  height: vh(140);
  background-image: url("@/assets/images/home/<USER>");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 vw(95);
}

/* 天气信息栏样式 */
.weather-info-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  color: #fff;
  padding: 0 vw(15);
}

.current-weather {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  gap: vw(48);
}

.weather-label {
  font-weight: normal;
  font-size: vh(24);
  color: #ffffff;
  line-height: vh(33);
}

.city-info {
  display: flex;
  align-items: center;
}

.location-icon {
  display: inline-block;
  width: vw(22);
  height: vh(24);
  margin-right: vw(20);
}

.city-name {
  font-weight: normal;
  font-size: vh(32);
  color: #ffffff;
  margin-top: vh(-8);
}

.weather-detail {
  display: flex;
  align-items: center;
  height: 100%;
  gap: vw(15);
}

.weather-icon-large {
  width: vw(58);
  height: vh(61);
  margin-right: vw(10);
}

.weather-icon-large img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.weather-data {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.weather-type {
  display: flex;
  align-items: center;
  gap: vw(10);
}

.weather-name {
  font-weight: normal;
  font-size: vh(30);
  color: #ffffff;
  line-height: vh(42);
  text-align: center;
  font-style: normal;
}

.weather-temp {
  font-weight: normal;
  font-size: vh(22);
  color: #ffffff;
  line-height: vh(30);
  font-style: normal;
  text-align: center;
  width: vw(72);
  height: vh(31);
  background: #1ecfa5;
  border-radius: vw(16);
}

.wind-info {
  display: flex;
  align-items: center;
  gap: vw(20);
  font-weight: normal;
  font-size: vh(27);
  color: #ffffff;
  text-align: left;
  font-style: normal;
  margin-top: vh(3);
}

.weather-forecast {
  display: flex;
  height: 100%;
  align-items: center;
}

.forecast-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 vw(30);
}

.forecast-title {
  font-weight: normal;
  font-size: vh(28);
  color: #ffffff;
  text-align: left;
  font-style: normal;
}

.forecast-value {
  .forecast-value-num {
    font-weight: bold;
    font-size: vh(33);
    color: #24ceb8;
    font-style: normal;
  }

  .forecast-value-unit {
    font-weight: normal;
    font-size: vh(24);
    color: #ffffff;
    font-style: normal;
  }
}

.forecast-value1 {
  .forecast-value-num {
    font-weight: bold;
    font-size: vh(33);
    color: #92d3ec;
    font-style: normal;
  }

  .forecast-value-unit {
    font-weight: normal;
    font-size: vh(24);
    color: #ffffff;
    font-style: normal;
  }
}

.publish-info {
  width: vw(395);
  height: vh(77);
  background: rgba(110, 204, 255, 0.14);
  border-radius: vw(1);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: vw(20);
}

.publish-label {
  font-weight: normal;
  font-size: vh(25);
  color: #ffffff;
  font-style: normal;
}

.publish-times {
  font-weight: normal;
  font-size: vh(22);
  color: #ffffff;
  font-style: normal;
}

.time-item {
  font-weight: normal;
  font-size: vh(22);
  color: #ffffff;
  font-style: normal;
}

// 添加分隔线样式
.divider-line {
  width: vw(1);
  height: vh(70);
}

.content-layout-middle {
  width: 100%;
  height: vh(270);
  //  background: #1ECFA5;
  background: linear-gradient(
    90deg,
    rgba(6, 72, 146, 0.24) 0%,
    rgba(6, 79, 156, 0.06) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .content-layout-middle-header {
    width: 100%;
    height: vh(45);
    background-image: url("@/assets/images/home/<USER>");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-left: vw(55);

    .title {
      font-family: JiangChengXieHei;
      height: vh(26);
      font-weight: bold;
      font-size: vh(26);
      color: #d8f1ff;
      font-style: normal;
    }
  }

  .content-layout-middle-bottom {
    width: 100%;
    height: vh(220);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 vw(16);

    .bottom-item-left {
      width: 36%;
      height: 100%;
      background-image: url("@/assets/images/home/<USER>");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      .left-top {
        margin-left: vw(55);
        margin-bottom: vh(14);

        .stats-title {
          font-size: vh(25);
          color: #fff;
        }

        .stats-number {
          font-size: vh(40);
          font-weight: bold;
          color: #2ada4c;
          line-height: 1;
          padding: 0 vw(30);
        }

        .stats-unit {
          font-size: vh(25);
          color: #d8f1ff;
          margin-bottom: vh(20);
        }
      }

      .stats-icons {
        display: flex;
        justify-content: space-around;
        width: 100%;
        padding: 0 vw(10);

        .icon-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          .icon-circle {
            width: vh(100);
            height: vh(100);
            background-image: url("@/assets/images/home/<USER>");
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: center;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: vh(22);
            font-weight: bold;
            margin-bottom: vh(8);
          }

          .icon-circle1 {
            width: vh(100);
            height: vh(100);
            background-image: url("@/assets/images/home/<USER>");
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: center;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: vh(22);
            font-weight: bold;
            margin-bottom: vh(8);
          }

          .icon-circle2 {
            width: vh(100);
            height: vh(100);
            background-image: url("@/assets/images/home/<USER>");
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: center;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: vh(22);
            font-weight: bold;
            margin-bottom: vh(8);
          }

          .icon-text {
            font-size: vh(23);
            text-align: center;
            font-family: Alibaba-PuHuiTi;
            font-weight: normal;
            color: #ffffff;
            font-style: normal;
          }
        }
      }
    }

    .bottom-item-middle {
      width: 36%;
      height: 100%;
      background-image: url("@/assets/images/home/<USER>");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      .left-top {
        margin-left: vw(65);
        margin-bottom: vh(14);

        .stats-title {
          font-size: vh(25);
          color: #fff;
        }

        .stats-number {
          font-size: vh(40);
          font-weight: bold;
          color: #2ada4c;
          line-height: 1;
          padding: 0 vw(30);
        }

        .stats-unit {
          font-size: vh(25);
          color: #d8f1ff;
          margin-bottom: vh(20);
        }
      }

      .stats-icons {
        display: flex;
        justify-content: space-around;
        width: 100%;
        padding: 0 vw(10);

        .icon-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          .icon-circle {
            width: vh(100);
            height: vh(100);
            background-image: url("@/assets/images/home/<USER>");
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: center;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: vh(22);
            font-weight: bold;
            margin-bottom: vh(8);
          }

          .icon-circle1 {
            width: vh(100);
            height: vh(100);
            background-image: url("@/assets/images/home/<USER>");
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: center;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: vh(22);
            font-weight: bold;
            margin-bottom: vh(8);
          }

          .icon-circle2 {
            width: vh(100);
            height: vh(100);
            background-image: url("@/assets/images/home/<USER>");
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: center;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: vh(22);
            font-weight: bold;
            margin-bottom: vh(8);
          }

          .icon-text {
            font-size: vh(23);
            text-align: center;
            font-family: Alibaba-PuHuiTi;
            font-weight: normal;
            color: #ffffff;
            font-style: normal;
          }
        }
      }
    }

    .bottom-item-right {
      width: 22%;
      height: 100%;
      background-image: url("@/assets/images/home/<USER>");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .problem-title {
        font-size: vh(22);
        color: #ffffff;
        margin-bottom: vh(15);
      }

      .problem-number {
        font-size: vh(60);
        font-weight: bold;
        color: #ffc800;
        font-family: DIN-Bold;
        display: flex;
        align-items: center;
        gap: vw(10);

        .arrow-up-with-line {
          width: vw(25);
          height: vh(40);
        }
      }
    }
  }
}

.content-layout-bottom {
  width: 100%;
  height: 99%;
  margin-top: 1%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .h-bottom {
        width: 100%;
        height: calc(100% - vh(45));
        padding: vh(20);
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: vh(200);
      }
  .bottom-item-left {
    width: 45%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .left-h {
      width: 100%;
      height: 56%;
      background: linear-gradient(
        90deg,
        rgba(6, 72, 146, 0.24) 0%,
        rgba(6, 79, 156, 0.06) 100%
      );

      .h-header {
        width: 100%;
        height: vh(45);
        background-image: url("@/assets/images/home/<USER>");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding-left: vw(55);

        .title {
          font-family: JiangChengXieHei;
          height: vh(26);
          font-weight: bold;
          font-size: vh(26);
          color: #d8f1ff;
          font-style: normal;
          line-height: vh(45);
        }
      }

      .content {
        width: 100%;
        height: calc(100% - vh(45));
        padding: vh(10);
      }
    }

    .left-b {
      width: 100%;
      height: 40%;
      background: linear-gradient(
        90deg,
        rgba(6, 72, 146, 0.24) 0%,
        rgba(6, 79, 156, 0.06) 100%
      );

      .h-header {
        width: 100%;
        height: vh(45);
        background-image: url("@/assets/images/home/<USER>");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding-left: vw(55);

        .title {
          font-family: JiangChengXieHei;
          height: vh(26);
          font-weight: bold;
          font-size: vh(26);
          color: #d8f1ff;
          font-style: normal;
          line-height: vh(45);
        }
      }

      .content {
        width: 100%;
        height: calc(100% - vh(45));
        padding: vh(10);
      }
    }
  }

  .bottom-item-right {
    width: 52%;
    height: 100%;
    background: linear-gradient(
      90deg,
      rgba(6, 72, 146, 0.24) 0%,
      rgba(6, 79, 156, 0.06) 100%
    );
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .right-h {
      width: 100%;
      height: 44%;

      .h-header {
        width: 100%;
        height: vh(45);
        background-image: url("@/assets/images/home/<USER>");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding-left: vw(55);

        .title {
          font-family: JiangChengXieHei;
          height: vh(26);
          font-weight: bold;
          font-size: vh(26);
          color: #d8f1ff;
          font-style: normal;
          line-height: vh(45);
        }
      }

      .right-h-content {
        width: 100%;
        height: calc(100% - vh(45));
        padding: vh(10);

        .data-indicators {
          display: flex;
          justify-content: space-between;
          height: 100%;

          .indicator-group {
            width: 48%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .indicator-item {
              height: 45%;
              padding: vh(8) vw(15);
              display: flex;
              align-items: center;
              justify-content: flex-start;

              .indicator-icon {
                width: vh(143);
                height: vh(166);
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: vw(15);

                img {
                  width: 100%;
                  height: 100%;
                }
              }

              .indicator-data {
                .indicator-value {
                  font-size: vh(40);
                  font-weight: bold;
                  color: #00a2ff;
                  font-family: DIN-Bold;

                  span {
                    font-size: vh(18);
                    margin-left: vw(5);
                  }
                }

                .indicator-label {
                  font-family: Alibaba-PuHuiTi;
                  font-size: vh(26);
                  color: #ffffff;
                  margin-top: vh(40);
                }
              }
            }
          }
        }
      }
    }

    .right-b {
      width: 100%;
      height: 54%;

      .h-header {
        width: 100%;
        height: vh(45);
        background-image: url("@/assets/images/home/<USER>");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding-left: vw(55);

        .title {
          font-family: JiangChengXieHei;
          height: vh(26);
          font-weight: bold;
          font-size: vh(26);
          color: #d8f1ff;
          font-style: normal;
          line-height: vh(45);
        }
      }

      .right-b-content {
        width: 100%;
        height: calc(100% - vh(45));
        padding: vh(10);

        .emergency-stats {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: 100%;

          .stat-row {
            display: flex;
            justify-content: space-between;
            height: 48%;

            .stat-box {
              width: 48%;
              height: 100%;

              padding-top: vh(25);
              padding-left: vw(50);
              display: flex;
              flex-direction: column;

              .stat-title {
                font-size: vh(26);
                color: #ffffff;
                margin-bottom: vh(20);
              }

              .stat-content {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                gap: vw(5);
                .stat-number {
                  width: vw(180);
                  height: vh(80);
                  font-size: vh(36);
                  font-weight: bold;
                  color: #fff;
                  font-family: DIN-Bold;
                  background-image: url("@/assets/images/home/<USER>");
                  background-size: 100% 100%;
                  background-repeat: no-repeat;
                  text-align: center;
                }

                .stat-details {
                  .stat-item {
                    display: flex;
                    align-items: center;
                    margin-right: vw(20);

                    .stat-label {
                      font-size: vh(26);
                      color: #ffffff;
                      margin-right: vw(8);
                    }

                    .stat-value {
                      font-size: vh(26);
                      font-weight: bold;

                      &.male {
                        color: #00fdbf;
                      }

                      &.female {
                        color: #ff6e9c;
                      }
                    }
                  }

                  .stat-progress {
                    display: flex;
                    align-items: center;
                    width: 100%;
                    gap: vw(7);
                    .progress-label {
                      font-size: vh(26);
                      color: #ffffff;
                    }

                    .progress-value {
                      font-size: vh(26);
                      font-weight: bold;
                      color: #fff;
                      margin-left: auto;
                    }
                  }
                }

                .stat-baseline {
                  font-size: vh(16);
                  color: #ffffff;
                  margin-bottom: vh(6);
                  align-self: flex-start;
                }

                .stat-bars {
                  width: 100%;
                  .bar-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: vh(8);

                    .bar-label {
                      font-size: vh(16);
                      color: #ffffff;
                      width: vw(80);
                    }

                    .bar-container {
                      flex: 1;
                      height: vh(8);
                      background-color: rgba(255, 255, 255, 0.2);
                      border-radius: vh(4);
                      overflow: hidden;
                      position: relative;
                      margin-right: vw(10);

                      .bar-fill {
                        height: 100%;
                        background: linear-gradient(90deg, #00a2ff, #00ffb4);
                        border-radius: vh(4);
                      }

                      .bar-baseline {
                        position: absolute;
                        top: 0;
                        left: 100%;
                        height: 100%;
                        width: 2px;
                        background-color: #ffde00;
                        transform: translateX(-50%);
                      }
                    }

                    .bar-value {
                      font-size: vh(16);
                      font-weight: bold;
                      color: #00ffb4;
                      width: vw(40);
                      text-align: right;
                    }
                  }
                }
              }
            }
            .stat-box1 {
              width: 48%;
              height: 100%;
              padding-top: vh(25);
              padding-left: vw(50);
              display: flex;
              flex-direction: column;

              .stat-title {
                font-size: vh(26);
                color: #ffffff;
                margin-bottom: vh(20);
              }

              .stat-content {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                gap: vw(5);
                .stat-number {
                  width: vw(180);
                  height: vh(80);
                  font-size: vh(36);
                  font-weight: bold;
                  color: #fff;
                  font-family: DIN-Bold;
                  background-image: url("@/assets/images/home/<USER>");
                  background-size: 100% 100%;
                  background-repeat: no-repeat;
                  text-align: center;
                }

                .stat-details {
                  .stat-item {
                    display: flex;
                    align-items: center;
                    margin-right: vw(20);

                    .stat-label {
                      font-size: vh(26);
                      color: #ffffff;
                      margin-right: vw(16);
                    }

                    .stat-value {
                      font-size: vh(26);
                      font-weight: bold;

                      &.male {
                        color: #00fdbf;
                      }

                      &.other {
                        color: #a8950f;
                      }
                    }
                  }

                  .stat-progress {
                    display: flex;
                    align-items: center;
                    width: 100%;
                    gap: vw(7);
                    .progress-label {
                      font-size: vh(26);
                      color: #ffffff;
                    }

                    .progress-value {
                      font-size: vh(26);
                      font-weight: bold;
                      color: #fff;
                      margin-left: auto;
                    }
                  }
                }

                .stat-baseline {
                  font-size: vh(16);
                  color: #ffffff;
                  margin-bottom: vh(6);
                  align-self: flex-start;
                }

                .stat-bars {
                  width: 100%;
                  .bar-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: vh(8);

                    .bar-label {
                      font-size: vh(16);
                      color: #ffffff;
                      width: vw(80);
                    }

                    .bar-container {
                      flex: 1;
                      height: vh(8);
                      background-color: rgba(255, 255, 255, 0.2);
                      border-radius: vh(4);
                      overflow: hidden;
                      position: relative;
                      margin-right: vw(10);

                      .bar-fill {
                        height: 100%;
                        background: linear-gradient(90deg, #00a2ff, #00ffb4);
                        border-radius: vh(4);
                      }

                      .bar-baseline {
                        position: absolute;
                        top: 0;
                        left: 100%;
                        height: 100%;
                        width: 2px;
                        background-color: #ffde00;
                        transform: translateX(-50%);
                      }
                    }

                    .bar-value {
                      font-size: vh(16);
                      font-weight: bold;
                      color: #00ffb4;
                      width: vw(40);
                      text-align: right;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
.full-width-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  padding: 2% 0 3% 0;
  .left-half {
    width: 49%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .left-top {
      width: 100%;
      height: 35%;
      background: linear-gradient(
        90deg,
        rgba(6, 72, 146, 0.24) 0%,
        rgba(6, 79, 156, 0.06) 100%
      );

      .h-header {
        width: 100%;
        height: vh(45);
        background-image: url("@/assets/images/home/<USER>");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding-left: vw(55);

        .title {
          font-family: JiangChengXieHei;
          height: vh(26);
          font-weight: bold;
          font-size: vh(26);
          color: #d8f1ff;
          font-style: normal;
          line-height: vh(45);
        }
      }

      .content {
        width: 100%;
        height: calc(100% - vh(45));
        padding: vh(10);

        .drainage-system-overview {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;

          .overview-cards {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-items: center;
            height: 100%;
            gap: 2%;
            .overview-card {
              width: 32%;
              height: 46%;
              // background: rgba(0, 42, 95, 0.3);
              // border: 1px solid rgba(0, 162, 255, 0.3);
              border-radius: vh(4);
              display: flex;
              align-items: center;
              justify-content: center;
              padding: vh(5) vw(5);

              .card-icon {
                width: vh(200);
                height: 100%;
                margin-bottom: vh(8);

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: contain;
                  filter: drop-shadow(0 0 vh(5) rgba(0, 162, 255, 0.5));
                }
              }

              .card-data {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: center;
                gap: vh(10);
                width: 100%;
                height: 100%;
                .card-value {
                  font-family: DIN-Bold;
                  font-size: vh(30);
                  font-weight: bold;
                  color: #fff;
                }

                .card-unit {
                  font-size: vh(16);
                  color: #ffffff;
                  margin-left: vw(8);
                  font-family: Alibaba-PuHuiTi;
                }
              }

              .card-label {
                font-size: vh(20);
                color: #ffffff;
                text-align: center;
              }
            }
          }
        }
      }
    }

    .left-bottom {
      width: 100%;
      height: 63%;
      background: linear-gradient(
        90deg,
        rgba(6, 72, 146, 0.24) 0%,
        rgba(6, 79, 156, 0.06) 100%
      );

      .h-header {
        width: 100%;
        height: vh(45);
        background-image: url("@/assets/images/home/<USER>");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding-left: vw(55);

        .title {
          font-family: JiangChengXieHei;
          height: vh(26);
          font-weight: bold;
          font-size: vh(26);
          color: #d8f1ff;
          font-style: normal;
          line-height: vh(45);
        }
      }
      .monitoring-header-box {
        width: 100%;
        height: vh(50);
        padding: 0 vw(20);
                  .monitoring-header {
          width: 100%;
          height: 100%;
          font-family: JiangChengXieHei;
          font-size: vh(24);
          color: #ffffff;
          text-align: left;
          padding-left: vw(45);
          padding-right: vw(45);
          background-image: url("@/assets/images/home/<USER>");
          background-size: 100% 100%;
          background-repeat: no-repeat;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .monitoring-left {
            width: 50%;
            height: 100%;
            display: flex;
            align-items: center;
          }
          .monitoring-right {
            width: 50%;
            height: 100%;
            font-size: vh(24);
            display: flex;
            justify-content: center;
            gap: vw(70);
            align-items: center;
            font-family: Alibaba-PuHuiTi;
            div {
              display: flex;
              justify-content: center;
              align-items: center;
            gap: vw(8);

              img {
                width: vh(18);
                height: vh(25);
                margin-bottom: vh(5);
              }

              div {
                font-size: vh(14);
                color: #ffffff;
              }
            }
          }
        }
      }
      .content {
        width: 100%;
        height: calc(100% - vh(95));
        padding: vh(10);
        /* 添加水情折线柱状图 */
        .river-chart-container {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }

  .right-half {
    width: 49%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .right-top {
      width: 100%;
      height: 40%;
      background: linear-gradient(
        90deg,
        rgba(6, 72, 146, 0.24) 0%,
        rgba(6, 79, 156, 0.06) 100%
      );

      .h-header {
        width: 100%;
        height: vh(45);
        background-image: url("@/assets/images/home/<USER>");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding-left: vw(55);

        .title {
          font-family: JiangChengXieHei;
          height: vh(26);
          font-weight: bold;
          font-size: vh(26);
          color: #d8f1ff;
          font-style: normal;
          line-height: vh(45);
        }
      }
      .monitoring-header-box {
        width: 100%;
        height: vh(50);
        padding: 0 vw(20);
        .monitoring-header {
          width: 100%;
          height: 100%;
          font-family: JiangChengXieHei;
          font-size: vh(24);
          color: #ffffff;
          text-align: left;
          padding-left: vw(45);
          padding-top: vh(10);
          background-image: url("@/assets/images/home/<USER>");
          background-size: 100% 100%;
          background-repeat: no-repeat;
        }
      }

      .content {
        width: 100%;
        height: calc(100% - vh(95)); /* Adjusted height to account for header and monitoring-header-box */
        padding: vh(10);

        .monitoring-stations {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-items: center;
          padding: vh(20) vw(10);
          gap: vw(20);

          .station-item {
            width: 30%;
            height: 50%;
            display: flex;
            flex-wrap: wrap;
            flex-direction: column;
            justify-items: space-between;
            align-items: center;

            .station-circle {
              width: vh(160);
              height: vh(130);
              background-image: url("@/assets/images/home/<USER>");
              background-size: 100% 100%;
              background-repeat: no-repeat;
              display: flex;
              justify-content: center;
              align-items: flex-start;
              margin-top: vh(-10);

              .station-number {
                font-size: vh(40);
                margin-top: vh(20);
                color: #ffffff;
                font-family: DIN-Bold;
              }
            }

            .station-name {
              font-size: vh(26);
              color: #ffffff;
              text-align: center;
            }
          }
        }
      }
    }

    .right-bottom {
      width: 100%;
      height: 58%;
      background: linear-gradient(
        90deg,
        rgba(6, 72, 146, 0.24) 0%,
        rgba(6, 79, 156, 0.06) 100%
      );

      .h-header {
        width: 100%;
        height: vh(45);
        background-image: url("@/assets/images/home/<USER>");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding-left: vw(55);

        .title {
          font-family: JiangChengXieHei;
          height: vh(26);
          font-weight: bold;
          font-size: vh(26);
          color: #d8f1ff;
          font-style: normal;
          line-height: vh(45);
        }

      }
    .monitoring-header-box {
        width: 100%;
        height: vh(50);
        padding: 0 vw(20);
        .monitoring-header {
          width: 100%;
          height: 100%;
          font-family: JiangChengXieHei;
          font-size: vh(24);
          color: #ffffff;
          text-align: left;
          padding-left: vw(45);
          padding-top: vh(10);
          background-image: url("@/assets/images/home/<USER>");
          background-size: 100% 100%;
          background-repeat: no-repeat;
        }
      }
      .content {
        width: 100%;
        height: calc(100% - vh(95));
        padding: vh(10);

      }
      .chart-rows-container {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .chart-row {
            width: 100%;
            height: 32%;
            position: relative;

            .chart-container1 {
              width: 100%;
              height: 100%;
              background-image: url("@/assets/images/home/<USER>");
              background-size: 100% 100%;
              background-repeat: no-repeat;
              border-radius: vh(4);
            }
            .chart-container2 {
              width: 100%;
              height: 100%;
              background-image: url("@/assets/images/home/<USER>");
              background-size: 100% 100%;
              background-repeat: no-repeat;
              border-radius: vh(4);
            }
            .chart-container3 {
              width: 100%;
              height: 100%;
              background-image: url("@/assets/images/home/<USER>");
              background-size: 100% 100%;
              background-repeat: no-repeat;
              border-radius: vh(4);
            }
          }
        }
    }
  }
}
</style>

