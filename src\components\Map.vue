<template>
  <div id="player"></div>
</template>

<script>
import * as acapi from '../assets/ac.min'
export default {
  name: 'Player',
  data() {
    return {
      api: null
    }
  },
  mounted() {
    //初始化API实例并启动渲染，DigitalTwinPlayer对象的构造参数请参考API开发文档
    // this.api = new acapi.DigitalTwinPlayer("************:8080", { "domId": "player",'apiOptions': {...} }).getAPI();
    this.init()
  },
  destroyed() {
    //关闭云渲染并释放资源
    this.api.destroy()
  },
  methods: {
    init() {
      function _onReady() {
        console.info('此时可以调API了')
      }

      function _onLog(s, nnl) {
        console.info('输出接口调用日志：')
        var logStr = s + (nnl ? '' : '\n')
        console.info(logStr)
      }

      function _onEvent(event) {
        console.info('监听各类交互事件')
        if (e.eventtype == 'LeftMouseButtonClick' && e.Type == 'TileLayer') {
          console.info('监听图层点击事件')
        }
      }
      //Cloud云渲染服务器地址和端口
      var host = '************:8080'
      //构造DigitalTwinPlayer对象所需的参数选项，更多参数详情请参考API开发手册里DigitalTwinPlayer对象
      var options = {
        //必选参数，网页显示视频流的domId
        domId: 'player',

        //必选参数，二次开发时必须指定，否则无法进行二次开发
        apiOptions: {
          //必选参数，与云渲染主机通信成功后的回调函数
          //注意：只有在onReady之后才可以调用DigitalTwinAPI接口
          onReady: _onReady, //api调用入口，必选回调函数
          onLog: _onLog, //可选参数，日志输出回调函数
          onEvent: _onEvent //可选参数，三维场景交互事件回调函数
        },

        ui: {
          startupInfo: true, //可选参数，是否显示页面加载详细信息，默认值false
          statusButton: true //可选参数，是否显示状态按钮，默认false
        },

        events: {
          //   onVideoLoaded: _onLoaded, //可选参数，视频流加载成功回调函数
          //   onConnClose: _onClose, //可选参数，连接断开回调函数
        },

        //可选参数，设置三维交互的键盘事件接收者
        //注意：接收类型有视频标签(video)，网页文档(document)，空(none)
        keyEventTarget: 'none'
      }
      //构造player对象
      var demoPlayer = new DigitalTwinPlayer(host, options)
      //构造DigitalTwinAPI对象并初始化
      var airCityApi = demoPlayer.getAPI()
    }
  }
}
</script>

<style scoped>
#player {
  height: 100vh;
  z-index: 9999;
}
</style>
