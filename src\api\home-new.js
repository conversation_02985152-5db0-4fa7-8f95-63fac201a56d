import request from '../utils/request'

export function getYltj(name = '2025-07-25') {
  return request({
    url: `/taiyuan/zhzs-yltj/stat?name=${name}`,
    method: 'get'
  })
}

export function getSjtj() {
  return request({
    url: '/taiyuan/zhzs-sjtj/stat',
    method: 'get'
  })
}

export function getBzyx() {
  return request({
    url: '/taiyuan/zhzs-psssnlbz/stat',
    method: 'get'
  })
}

export function getGw() {
  return request({
    url: '/taiyuan/zhzs-psssnlgw/stat',
    method: 'get'
  })
}

export function getRywz() {
  return request({
    url: '/taiyuan/zhzs-rywztj/stat',
    method: 'get'
  })
}

export function getDdzltj() {
  return request({
    url: '/taiyuan/zhzs-ddzltj/stat',
    method: 'get'
  })
}

export function getDdzl() {
  return request({
    url: '/taiyuan/zhzs-ddzl/stat',
    method: 'get'
  })
}

export function getYldbj() {
  return request({
    url: '/taiyuan/zhzs-yldbj/stat',
    method: 'get'
  })
}

export function getYldbjtb() {
  return request({
    url: '/taiyuan/statistics/list?type=低洼路段',
    method: 'get'
  })
}

export function getPsss() {
  return request({
    url: '/taiyuan/zhzs-psssbj/stat',
    method: 'get'
  })
}

export function getPssstb() {
  return request({
    url: '/taiyuan/statistics/list?type=雨水管网',
    method: 'get'
  })
}

export function gettbRight(params) {
  let url = `/taiyuan/statistics/list?pageNum=${params.pageNum}&pageSize=${params.pageSize}`

  // 添加查询条件
  if (params.area) {
    url += `&area=${encodeURIComponent(params.area)}`
  }
  if (params.type) {
    url += `&type=${encodeURIComponent(params.type)}`
  }

  return request({
    url,
    method: 'get'
  })
}
export function getddzltb() {
  return request({
    url: '/taiyuan/zhzs-ddzl/stat',
    method: 'get'
  })
}

export function getsghx(params) {
  return request({
    url: '/taiyuan/zhzs-qytj/stat',
    method: 'get'
  })
}

export function getbcLocation() {
  return request({
    url: '/taiyuan/fkclxxb/list?cllx=泵车',
    method: 'get'
  })
}
export function getwzLocation() {
  return request({
    url: '/taiyuan/wz/list?pageSize=10000',
    method: 'get'
  })
}

// 韦恩图联动
// echarts地图数据
export function getMapData(name) {
  return request({
    url: `/taiyuan/zhzs-qytj2/stat?name=${name}`,
    method: 'get'
  })
}

// 柱状图数据
export function getBarData(name) {
  return request({
    url: `/taiyuan/zhzs-sjtj2/stat?name=${name}`,
    method: 'get'
  })
}
