// 定义设计稿的宽高
const designWidth = 8612
const designHeight = 1382

/**
 * 初始化屏幕适配
 * 根据设计稿尺寸和当前窗口尺寸计算缩放比例
 */
export function initScreenAdapter() {
  const handleResize = () => {
    const app = document.querySelector('.app-container')
    if (!app) return

    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight

    // 计算宽高比例
    const widthRatio = windowWidth / designWidth
    const heightRatio = windowHeight / designHeight

    // 取最小比例进行等比缩放
    const scale = Math.min(widthRatio, heightRatio)

    // 应用缩放
    app.style.transform = `scale(${scale})`
    app.style.transformOrigin = 'top left'

    // 居中处理
    if (widthRatio > heightRatio) {
      const marginLeft = (windowWidth - designWidth * scale) / 2
      app.style.marginLeft = `${marginLeft}px`
      app.style.marginTop = '0px'
    } else {
      const marginTop = (windowHeight - designHeight * scale) / 2
      app.style.marginTop = `${marginTop}px`
      app.style.marginLeft = '0px'
    }
  }

  // 初始调用一次
  handleResize()

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}
