// Echarts图表字体、间距自适应
export const fitChartSize = (size, type = 'width', defaultWidth = 8612, defaultHeight = 1382) => {
  // 将固定大小转换为vw或vh单位
  if (type == 'width' || type == 'w') {
    // vw是视口宽度的1/100，所以将size转换为相对于defaultWidth的百分比，再乘以100得到vw值
    return Number(((size / defaultWidth) * 100).toFixed(3)) + 'vw';
  } else if (type == 'height' || type == 'h') {
    // vh是视口高度的1/100，所以将size转换为相对于defaultHeight的百分比，再乘以100得到vh值
    return Number(((size / defaultHeight) * 100).toFixed(3)) + 'vh';
  } else {
    // 默认返回vw单位
    return Number(((size / defaultWidth) * 100).toFixed(3)) + 'vw';
  }
}