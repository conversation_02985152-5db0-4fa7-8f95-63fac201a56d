import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import router from './router'
import pinia from './stores'
import { registerChartResizeDirective } from '@/utils/directive'
import zhLocale from 'element-plus/es/locale/lang/zh-cn'  

console.log('import.meta.env.VITE_APP_TITLE', import.meta.env.VITE_APP_TITLE)
console.log('import.meta.env.VITE_APP_BASE_URL', import.meta.env.VITE_APP_BASE_URL)
const app = createApp(App)

app.use(router)
app.use(pinia)
registerChartResizeDirective(app)


app.use(ElementPlus, { locale: zhLocale })
app.mount('#app')
