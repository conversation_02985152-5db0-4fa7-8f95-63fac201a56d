<!DOCTYPE html>
<meta charset="utf-8" />
<title>拨打电话</title>
<style>
    body {
        color: #fff;
    }

    .call-page {
        font-size: 25px;
        color: #fff;
        text-align: center;
        margin-top: 50px;
    }

    .chuzhiimg {
        width: 80px;
        height: 80px;
        margin-left: 240px;
    }

    .callBtn {
        /* position: absolute;
        bottom: 30px;
        left: 150px; */
        position: absolute;
        bottom: 50px;
    }

    .output {
        display: none;
        height: 408px;
        overflow-y: auto;
    }
</style>
<script language="javascript" type="text/javascript">
    //  var wsUri ="ws://*************:1818/"; 
    var output;//发送输出结果
    var socket;//连接
    var phoneNum = '';//电话
    var callPhoneNo = ''
    var CallWorkNo = '' //坐席号
    //格式化要拨打的电话号码123-1234-1234
    function GetQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg); //获取url中"?"符后的字符串并正则匹配
        var context = "";
        if (r != null)
            context = decodeURIComponent(r[2]);
        reg = null;
        r = null;
        return context == null || context == "" || context == "undefined" ? "" : context;
    }
    function hidePhone(str) {
        var hiddenStr = str.substring(0, 4) + str.substring(4).replace(/./g, '*');
        return hiddenStr;
    }
    function getCookie(name) {
        var cookieValue = null;
        if (document.cookie && document.cookie != '') {
            var cookies = document.cookie.split(';');
            for (var i = 0; i < cookies.length; i++) {
                var cookie = cookies[i].replace(/(^\s*)|(\s*$)/g, '');
                if (cookie.substring(0, name.length + 1) == (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    //加载显示要拨打的电话号码
    window.onload = function () {
        debugger
        phoneNum = GetQueryString("phoneNum")
        let formatterPhone = phoneNum.replace(/(\d{3})(\d{4})(\d{3})/, '$1-$2-$3')
        let strphone = hidePhone(formatterPhone)
        document.getElementById('Calphone').innerText = strphone
        // document.getElementById('Calphone').innerText = phoneNum.replace(/(\d{3})(\d{4})(\d{3})/, '$1-$2-$3')
        debugger
        let quicksoft_ticket = getCookie("bigscreenlogincode");
        //不同的登录用户名，使用不同的话机号（96420,tycgj-96418,tyszf-96419,typgzx-96417）
        console.log(quicksoft_ticket)
        if (quicksoft_ticket == 'tycgj') {
            callPhoneNo = '96418'
            CallWorkNo = '6003'
        } else if (quicksoft_ticket == 'tyszf') {
            callPhoneNo = '96419'
            CallWorkNo = '6004'
        } else if (quicksoft_ticket == 'typgzx') {
            callPhoneNo = '96417'
            CallWorkNo = '6005'
        } else {
            callPhoneNo = '96420'
            CallWorkNo = '6002'
        }
    }
    const messageQueue = [];//send队列
    let isSending = false;//判断是否在send中
    //点击拨号,拨号前先连接
    function callout() {
        debugger
        //socket.open();
        //执行WebSocket连接
        checkWebSocketConnection().then(result => {
            if (result === 'success') {
                // 连接成功后的操作
                writeToScreen("CONNECTED成功");
                // 调用函数发送多个消息
                sendMultipleMessages();
            } else {
                writeToScreen('<span style="color: red;">ERROR:websock连接失败</span> ');
            }

        }).catch(error => {
            writeToScreen('<span style="color: red;">CONNECTED连接ERROR:</span> ' + JSON.stringify(error));
        });
    }
    //websock连接
    function checkWebSocketConnection() {
        output = document.getElementById("output");
        socket = new WebSocket('ws://127.0.0.1:1818/');
        return new Promise((resolve, reject) => {
            socket.onopen = function () {
                resolve('success');
                console.log("wobsoct conn success")
            };
            socket.onerror = function (error) {
                reject(error);
            };
        });
    }
    //打印结果
    function writeToScreen(message) {
        var pre = document.createElement("p");
        pre.style.wordWrap = "break-word";
        pre.innerHTML = message;
        output.appendChild(pre);
    }
    //执行send
    function sendMessage(message) {
        debugger
        return new Promise((resolve, reject) => {
            socket.onmessage = function (event) {
                resolve(event.data);
            };
            socket.onerror = function (error) {
                reject(error);
            };
            socket.send(message);
        });
    }
    async function sendMultipleMessages() {
        debugger
        try {
            //嵌入
            writeToScreen('SENT: {"Cmd":"Login","WorkNo":6002,"PhoneNo":"' + callPhoneNo + '","Password":"6002","AgentType":2}');
            const response1 = await sendMessage('{"Cmd":"Login","WorkNo":'+CallWorkNo+',"PhoneNo":"' + callPhoneNo + '","Password":"'+CallWorkNo+'","AgentType":2}');
            console.log('{"Cmd":"Login","WorkNo":'+CallWorkNo+',"PhoneNo":"' + callPhoneNo + '","Password":"'+CallWorkNo+'","AgentType":2}')
            // const response1 = await sendMessage('{"Cmd":"Login","WorkNo":6002,"PhoneNo":"' + callPhoneNo + '","Password":"6002","AgentType":2}');
            writeToScreen('<span style="color: orange;">RESPONSE: ' + response1 + '</span>');
            //拨号
            setTimeout(() => {
                console.log('拨号SENT: {"Cmd":"CallOut","Caller":"8612319","Called":"' + phoneNum + '"}')
                writeToScreen('SENT: {"Cmd":"CallOut","Caller":"8612319","Called":"' + phoneNum + '"}');
                const response2 = sendMessage('{"Cmd":"CallOut","Caller":"8612319","Called":"' + phoneNum + '"}');
                console.log('拨号RESPONSE:', response2)
                writeToScreen('<span style="color: orange;">RESPONSE: ' + response2 + '</span>');
            }, 5000);
        } catch (error) {
            writeToScreen('<span style="color: red;">ERROR:</span> ' + JSON.stringify(error));
        }
    }
    //挂断
    async function wsexit() {
        //迁出
        writeToScreen('SENT: {"Cmd":"Logout"}');
        const response3 = await sendMessage('{"Cmd":"Logout"}');
        writeToScreen('<span style="color: orange;">RESPONSE: ' + response3 + '</span>');
        //websock关闭
        alert('websocket已迁出')
    }

</script>
<div id="callphone">
    <div class="call-page"><span>您确定拨打：</span> <span id="Calphone"></span> </div>
</div>
<div id="output" class="output"></div>
<div class="callBtn">
    <img class="chuzhiimg" onClick="callout()" src="images/拨打.png">
    <img class="chuzhiimg" onClick="wsexit()" src="images/关闭.png">
</div>

</html>