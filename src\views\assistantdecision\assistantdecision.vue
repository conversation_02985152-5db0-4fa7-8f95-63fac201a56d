<template>
  <div class="screen-container">
    <div class="bg-layer bg-layer-1"></div>
    <div class="bg-layer bg-layer-2"></div>
    <!-- <div class="bg-layer bg-layer-3"></div> -->
    <!-- 地图容器，修改ID避免重复 -->
    <div id="player" class="map-container"></div>

    <!-- 左侧区域容器 -->
    <div class="left-container">
      <div class="left-section">
        <div class="time-weather">
          <div class="time-date">
            <div class="time">{{ currentTime }}</div>
            <div class="date">{{ currentDate }}</div>
          </div>
          <div class="divider"></div>
          <div class="weather">
            <img class="weather-icon" :src="getAssetsFile('sunny.png')" alt="天气" />
            <img class="weather-icon1" :src="getAssetsFile('wendu.png')" alt="温度" />
            <span class="temperature">17℃</span>
          </div>
        </div>
        <!-- 第一部分 -->
        <div class="content-layout-first">
          <div class="content-layout-header">
            <div class="weather-info-bar">
              <div class="current-weather">
                <span class="weather-label">当前天气</span>
                <div class="city-info">
                  <img class="location-icon" src="@/assets/images/home/<USER>" alt="" />
                  <span class="city-name">太原市</span>
                </div>
              </div>
              <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />
              <div class="weather-detail">
                <div class="weather-icon-large">
                  <img src="@/assets/images/home/<USER>" alt="大雨" />
                </div>
                <div class="weather-data">
                  <div class="weather-type">
                    <span class="weather-name">大雨</span>
                    <span class="weather-temp">19优</span>
                  </div>
                  <div class="wind-info">
                    <span>东风 </span>
                    <span> 2级</span>
                  </div>
                </div>
              </div>
              <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />
              <div class="weather-forecast">
                <div class="forecast-item">
                  <div class="forecast-title">24h累计降雨</div>
                  <div class="forecast-value">
                    <span class="forecast-value-num">50</span>
                    <span class="forecast-value-unit">mm</span>
                  </div>
                </div>
                <div class="forecast-item">
                  <div class="forecast-title">未来2h降雨</div>
                  <div class="forecast-value1">
                    <span class="forecast-value-num">20</span>
                    <span class="forecast-value-unit">mm</span>
                  </div>
                </div>
              </div>
              <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />

              <div class="publish-info">
                <span class="publish-label">预报发布</span>
                <div class="publish-times">
                  <div class="time-item">开始时间: 20:50:00</div>
                  <div class="time-item">结束时间: 18:00:00</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 上下布局容器 -->
        <div class="left-content">
          <div class="content-layout-second">
            <div class="second-module-1">
              <div class="module-item-1">
                <div class="module-header">
                  <h3 class="module-title">排水防涝统计/排水预警统计</h3>
                  <div class="module-time">降雨日历</div>
                </div>
                <div class="module-content">
                  <div class="stats-row">
                    <div class="stats-col">
                      <div class="stats-label">雨量统计</div>
                      <div class="stats-card">
                        <div class="stats-icon">
                          <img src="https://picsum.photos/60/60" alt="雨量图标" />
                        </div>
                        <div class="stats-data">
                          <div class="stats-unit">平均降雨 (mm)</div>
                          <div class="stats-value">{{ rainfallRight?.average || '0' }}</div>
                        </div>
                      </div>
                      <div class="stats-card">
                        <div class="stats-icon">
                          <img src="https://picsum.photos/60/60" alt="雨量图标" />
                        </div>
                        <div class="stats-data">
                          <div class="stats-unit">最大降雨 (mm)</div>
                          <div class="stats-value">{{ rainfallRight?.max || '0' }}</div>
                        </div>
                      </div>
                    </div>
                    <div class="stats-col">
                      <div class="stats-label">
                        <div class="title">内涝积水点统计</div>
                        <div class="time">日/月/周</div>
                      </div>
                      <div class="stats-grid">
                        <div v-for="(item, index) in drainageDataList" :key="index" class="grid-item">
                          <div class="grid-value">{{ item.value }}</div>
                          <div class="grid-label">{{ item.name }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="module-item-2">
                <div class="module-header">
                  <h3 class="module-title">报警事件类型统计</h3>
                </div>
                <div class="chart-container">
                  <div id="audit-chart" class="chart"></div>
                </div>
              </div>
              <div class="module-item-3">
                <div class="module-header">
                  <h3 class="module-title">人员调配统计</h3>
                </div>
                <div class="personnel-table-container">
                  <table class="personnel-table">
                    <thead>
                      <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>负责人</th>
                        <th>队伍人数</th>
                        <th>联系电话</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(item, index) in personnelTableData" :key="index">
                        <td>{{ item.id }}</td>
                        <td>{{ item.name }}</td>
                        <td>{{ item.manager }}</td>
                        <td>{{ item.count }}</td>
                        <td>{{ item.phone }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <div class="second-module-2">
              <div class="module-2-top">
                <div class="top-title-area">
                  <h3 class="area-title">内涝影响分析</h3>
                </div>
                <div class="top-content-area">
                  <div v-for="(block, blockIndex) in impactofwaterloggingList" :key="blockIndex" class="content-block">
                    <div class="block-title">
                      <h4 class="title-text">{{ block.severity }}</h4>
                    </div>
                    <div class="block-content">
                      <div class="content-item">
                        <div class="content-item-value">
                          <div class="content-item-value-text" :class="getBackgroundClass(block.severity)">
                            {{ block.roads }}
                          </div>
                          <div class="content-item-value-text" :class="getBackgroundClass(block.severity)">
                            {{ block.water }}
                          </div>
                          <div class="content-item-value-text" :class="getBackgroundClass(block.severity)">
                            {{ block.people }}
                          </div>
                        </div>
                        <div class="content-item-title-box">
                          <div class="content-item-title">道路</div>
                          <div class="content-item-title">积水点</div>
                          <div class="content-item-title">人员</div>
                        </div>
                      </div>
                      <!-- <div class="content-item" v-for="(item, itemIndex) in block.items" :key="itemIndex">
                        <div class="content-item-value" :class="getBackgroundClass(block.title)">
                          <span class="content-item-value-text">10</span>
                        </div>
                        <div class="content-item-title">{{ item.content }}</div>
                      </div> -->
                    </div>
                  </div>
                </div>
              </div>
              <div class="module-2-middle">
                <div class="drainage-stats">
                  <div class="drainage-title">积涝统计</div>
                  <div class="risk-overview">
                    <div class="risk-item">
                      <span class="risk-label">低风险</span>
                      <span class="risk-value">{{ riskOverview?.low_risk }}</span>
                      <span class="risk-trend">↑</span>
                    </div>
                    <div class="risk-item">
                      <span class="risk-label">中风险</span>
                      <span class="risk-value">{{ riskOverview?.medium_risk }}</span>
                      <span class="risk-trend">↑</span>
                    </div>
                    <div class="risk-item">
                      <span class="risk-label">高风险</span>
                      <span class="risk-value">{{ riskOverview?.high_risk }}</span>
                      <span class="risk-trend">↑</span>
                    </div>
                  </div>
                  <div class="drainage-table-container">
                    <table class="drainage-table">
                      <thead>
                        <tr>
                          <th>序号</th>
                          <th>站名</th>
                          <th>时段</th>
                          <th>积水变化(mm)</th>
                          <th>面积扩展(s)</th>
                          <th>持续积水时间(min)</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(item, index) in accumulatedFloodList" :key="index">
                          <td>{{ item.seq }}</td>
                          <td>{{ item.station }}</td>
                          <td style="white-space: normal; word-break: break-all">{{ item.time_period }}</td>
                          <td>{{ item.water_change }}</td>
                          <td>{{ item.area_expansion }}</td>
                          <td>{{ item.water_storage_time }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <div class="module-2-bottom">
                <div class="bottom-title-area">
                  <h3 class="area-title">物资调配统计</h3>
                </div>
                <div class="bottom-content-area">
                  <div class="material-table-container">
                    <table class="material-table">
                      <thead>
                        <tr>
                          <th>序号</th>
                          <th>名称</th>
                          <th>数量</th>
                          <th>负责人</th>
                          <th>联系电话</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(item, index) in materialAllocationList" :key="index">
                          <td>{{ item.seq }}</td>
                          <td>{{ item.name }}</td>
                          <td>{{ item.responsible }}</td>
                          <td>{{ item.num }}</td>
                          <td>{{ item.phone }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
            <div class="second-module-3">
              <div class="module-3-top">
                <div class="top-title-area">
                  <h3 class="area-title">设备报警</h3>
                </div>
                <div class="top-content-area">
                  <div class="grid-container">
                    <div class="grid-item" v-for="(item, index) in equipmentAlarmList" :key="index">
                      <div class="item-icon">
                        <img :src="item.icon || 'https://picsum.photos/100/100'" :alt="item.name" />
                      </div>
                      <div class="item-text">
                        <div class="item-name">{{ item.type }}</div>
                        <div class="item-status">{{ item.online }}/{{ item.total }}/{{ item.warning }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="equipment-container">
                <div class="equipment-header">
                  <h3 class="equipment-title">设备报警情况</h3>
                </div>
                <div class="equipment-filter">
                  <div class="filter-item">
                    <span class="filter-label">设备类型</span>
                    <select class="filter-select">
                      <option value="">全部</option>
                      <option value="1">泵站</option>
                      <option value="2">排水口</option>
                      <option value="3">积水点</option>
                      <option value="4">雨水管网</option>
                    </select>
                  </div>
                </div>
                <div class="equipment-table-container">
                  <table class="equipment-table">
                    <thead>
                      <tr>
                        <th>序号</th>
                        <th>设备名称</th>
                        <th>位置</th>
                        <th>泵站状况</th>
                        <th>抽水量</th>
                        <th>抽排量</th>
                        <th>启停状况</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(item, index) in equipmentAlarmDetailList" :key="index">
                        <td>{{ item.seq }}</td>
                        <td>{{ item.device_name }}</td>
                        <td>{{ item.location }}</td>
                        <td>{{ item.status }}</td>
                        <td>{{ item.pumping_volume }}</td>
                        <td>{{ item.discharge_volume }}</td>
                        <td>{{ item.shutdown_status }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间区域容器 -->
    <div class="middle-container">
      <div class="middle-section">
        <!-- 标题 -->
        <div class="section-title">内涝安全预警监测综合驾驶舱系统</div>
        <!-- 导航按钮 -->
        <div class="nav-buttons">
          <div
            v-for="(btn, index) in navButtons"
            :key="index"
            :class="['nav-button', { active: activeNavButton === index }]"
            @click="activeNavButton = index"
          >
            <div class="nav-button-text">{{ btn.text }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧区域容器 -->
    <div class="right-container">
      <div class="right-section">
        <!-- 上部分：用户信息和返回门户 -->
        <div class="user-portal-container">
          <div class="user-info">
            <img class="user-icon" src="https://picsum.photos/200/300" alt="用户" />
            <span class="username">管理员</span>
          </div>
          <div class="portal-back">
            <img class="portal-icon" src="https://picsum.photos/200/300" alt="门户" />
            <span class="portal-text">返回门户</span>
          </div>
        </div>

        <!-- 下部分：新增的内容区域 -->
        <div class="right-content">
          <div class="content-layout-second">
            <div class="second-module-1">
              <div class="module-item-1">
                <div class="module-header">
                  <h3 class="module-title">排水防涝统计/排水预警统计</h3>
                  <div class="module-time">降雨日历</div>
                </div>
                <div class="module-content">
                  <div class="stats-row">
                    <div class="stats-col">
                      <div class="stats-label">雨量统计</div>
                      <div class="stats-card">
                        <div class="stats-icon">
                          <img src="https://picsum.photos/60/60" alt="雨量图标" />
                        </div>
                        <div class="stats-data">
                          <div class="stats-unit">平均降雨 (mm)</div>
                          <div class="stats-value">{{ rainfall?.average || '0' }}</div>
                        </div>
                      </div>
                      <div class="stats-card">
                        <div class="stats-icon">
                          <img src="https://picsum.photos/60/60" alt="雨量图标" />
                        </div>
                        <div class="stats-data">
                          <div class="stats-unit">最大降雨 (mm)</div>
                          <div class="stats-value">{{ rainfall?.max || '0' }}</div>
                        </div>
                      </div>
                    </div>
                    <div class="stats-col">
                      <div class="stats-label">
                        <div class="title">内涝积水点统计</div>
                        <div class="time">日/月/周</div>
                      </div>
                      <div class="stats-grid">
                        <div class="grid-item" v-for="(item, index) in drainageDataRightList" :key="index">
                          <div class="grid-value">{{ item.value }}</div>
                          <div class="grid-label">{{ item.area }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="module-item-2">
                <div class="module-header">
                  <h3 class="module-title">报警事件类型统计</h3>
                </div>
                <div class="chart-container">
                  <div id="audit-chart1" class="chart"></div>
                </div>
              </div>
              <div class="module-item-3">
                <div class="module-header">
                  <h3 class="module-title">人员调配统计</h3>
                </div>
                <div class="personnel-table-container">
                  <table class="personnel-table">
                    <thead>
                      <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>负责人</th>
                        <th>队伍人数</th>
                        <th>联系电话</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(item, index) in personnelDeploymentRightList" :key="index">
                        <td>{{ item.seq }}</td>
                        <td>{{ item.name }}</td>
                        <td>{{ item.responsible }}</td>
                        <td>{{ item.num }}</td>
                        <td>{{ item.phone }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <div class="second-module-2">
              <div class="module-2-top">
                <div class="top-title-area">
                  <h3 class="area-title">内涝影响分析</h3>
                </div>
                <div class="top-content-area">
                  <div
                    v-for="(block, blockIndex) in impactofwaterloggingRightList"
                    :key="blockIndex"
                    class="content-block"
                  >
                    <div class="block-title">
                      <h4 class="title-text">{{ block.severity }}</h4>
                    </div>
                    <div class="block-content">
                      <div class="content-item">
                        <div class="content-item-value">
                          <div class="content-item-value-text" :class="getBackgroundClass(block.severity)">
                            {{ block.roads }}
                          </div>
                          <div class="content-item-value-text" :class="getBackgroundClass(block.severity)">
                            {{ block.water }}
                          </div>
                          <div class="content-item-value-text" :class="getBackgroundClass(block.severity)">
                            {{ block.people }}
                          </div>
                        </div>
                        <div class="content-item-title-box">
                          <div class="content-item-title">道路</div>
                          <div class="content-item-title">积水点</div>
                          <div class="content-item-title">人员</div>
                        </div>
                      </div>
                      <!-- <div class="content-item" v-for="(item, itemIndex) in block.items" :key="itemIndex">
                        <div class="content-item-value" :class="getBackgroundClass(block.title)">
                          <span class="content-item-value-text">10</span>
                        </div>
                        <div class="content-item-title">{{ item.content }}</div>
                      </div> -->
                    </div>
                  </div>
                </div>
              </div>
              <div class="module-2-middle">
                <div class="drainage-stats">
                  <div class="drainage-title">积涝统计</div>
                  <div class="risk-overview">
                    <div class="risk-item">
                      <span class="risk-label">低风险</span>
                      <span class="risk-value">{{ riskOverviewRight?.low_risk }}</span>
                      <span class="risk-trend">↑</span>
                    </div>
                    <div class="risk-item">
                      <span class="risk-label">中风险</span>
                      <span class="risk-value">{{ riskOverviewRight?.medium_risk }}</span>
                      <span class="risk-trend">↑</span>
                    </div>
                    <div class="risk-item">
                      <span class="risk-label">高风险</span>
                      <span class="risk-value">{{ riskOverviewRight?.high_risk }}</span>
                      <span class="risk-trend">↑</span>
                    </div>
                  </div>
                  <div class="drainage-table-container">
                    <table class="drainage-table">
                      <thead>
                        <tr>
                          <th>序号</th>
                          <th>站名</th>
                          <th>时段</th>
                          <th>积水变化(mm)</th>
                          <th>面积扩展(s)</th>
                          <th>持续积水时间(min)</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(item, index) in accumulatedFloodRightList" :key="index">
                          <td>{{ item.seq }}</td>
                          <td>{{ item.station }}</td>
                          <td style="white-space: normal; word-break: break-all">{{ item.time_period }}</td>
                          <td>{{ item.water_change }}</td>
                          <td>{{ item.area_expansion }}</td>
                          <td>{{ item.water_storage_time }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <div class="module-2-bottom">
                <div class="bottom-title-area">
                  <h3 class="area-title">物资调配统计</h3>
                </div>
                <div class="bottom-content-area">
                  <div class="material-table-container">
                    <table class="material-table">
                      <thead>
                        <tr>
                          <th>序号</th>
                          <th>名称</th>
                          <th>数量</th>
                          <th>负责人</th>
                          <th>联系电话</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(item, index) in materialAllocationRightList" :key="index">
                          <td>{{ item.seq }}</td>
                          <td>{{ item.name }}</td>
                          <td>{{ item.responsible }}</td>
                          <td>{{ item.num }}</td>
                          <td>{{ item.phone }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
            <div class="second-module-3">
              <div class="module-3-top">
                <div class="top-title-area">
                  <h3 class="area-title">设备报警</h3>
                </div>
                <div class="top-content-area">
                  <div class="grid-container">
                    <div class="grid-item" v-for="(item, index) in equipmentAlarmRightList" :key="index">
                      <div class="item-icon">
                        <img :src="item.icon || 'https://picsum.photos/100/100'" :alt="item.name" />
                      </div>
                      <div class="item-text">
                        <div class="item-name">{{ item.type }}</div>
                        <div class="item-status">{{ item.online }}/{{ item.total }}/{{ item.warning }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="equipment-container">
                <div class="equipment-header">
                  <h3 class="equipment-title">设备报警情况</h3>
                </div>
                <div class="equipment-filter">
                  <div class="filter-item">
                    <span class="filter-label">设备类型</span>
                    <select class="filter-select">
                      <option value="">全部</option>
                      <option value="1">泵站</option>
                      <option value="2">排水口</option>
                      <option value="3">积水点</option>
                      <option value="4">雨水管网</option>
                    </select>
                  </div>
                </div>
                <div class="equipment-table-container">
                  <table class="equipment-table">
                    <thead>
                      <tr>
                        <th>序号</th>
                        <th>设备名称</th>
                        <th>位置</th>
                        <th>泵站状况</th>
                        <th>抽水量</th>
                        <th>抽排量</th>
                        <th>启停状况</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(item, index) in equipmentMaintenanceRightList" :key="index">
                        <td>{{ item.seq }}</td>
                        <td>{{ item.device_name }}</td>
                        <td>{{ item.location }}</td>
                        <td>{{ item.status }}</td>
                        <td>{{ item.pumping_volume }}</td>
                        <td>{{ item.discharge_volume }}</td>
                        <td>{{ item.shutdown_status }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as acapi from '../../assets/ac.min.js'
import * as echarts from 'echarts'
import {
  getDrainageData,
  getAlarmevent,
  getPersonnelDeployment,
  getImpactofwaterlogging,
  getMaterialAllocation,
  getAccumulatedFlood,
  getEquipmentAlarm,
  getEquipmentMaintenance,
  getDrainageDataRight,
  getAlarmeventRight,
  getPersonnelDeploymentRight,
  getImpactofwaterloggingRight,
  getMaterialAllocationRight,
  getAccumulatedFloodRight,
  getEquipmentAlarmRight,
  getEquipmentMaintenanceRight
} from '@/api/assistantdecision'
const navButtons = [{ text: '综合展示' }, { text: '智能感知' }, { text: '报警决策' }, { text: '辅助决策' }]
const activeNavButton = ref(3)

// 右边
// 设备报警情况
const equipmentMaintenanceRight = ref(null)
const equipmentMaintenanceRightList = ref(null)
const getEquipmentMaintenanceRightFc = async () => {
  try {
    const result = await getEquipmentMaintenanceRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        equipmentMaintenanceRight.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        equipmentMaintenanceRightList.value = equipmentMaintenanceRight.value[0].data
        // console.log('11111111111111112222222222:', equipmentAlarmDetail.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 设备报警
const equipmentAlarmRight = ref(null)
const equipmentAlarmRightList = ref(null)
const getEquipmentAlarmRightFc = async () => {
  try {
    const result = await getEquipmentAlarmRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        equipmentAlarmRight.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        equipmentAlarmRightList.value = equipmentAlarmRight.value[0].data

        console.log('11111111111111112222222222:', equipmentAlarmRight.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 积涝统计
const accumulatedFloodRight = ref(null)
const accumulatedFloodRightList = ref(null)
const riskOverviewRight = ref(null)
const getAccumulatedFloodRightFc = async () => {
  try {
    const result = await getAccumulatedFloodRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        accumulatedFloodRight.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        accumulatedFloodRightList.value = accumulatedFloodRight.value[0].data
        riskOverviewRight.value = accumulatedFloodRight.value[0]
        // console.log('11111111111111112222222222:', accumulatedFloodRight.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 物资调配统计
const materialAllocationRight = ref(null)
const materialAllocationRightList = ref(null)
const getMaterialAllocationRightFc = async () => {
  try {
    const result = await getMaterialAllocationRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        materialAllocationRight.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        materialAllocationRightList.value = materialAllocationRight.value[0].data
        // console.log('11111111111111112222222222:', materialAllocationRight.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 内涝影响分析
const impactofwaterloggingRight = ref(null)
const impactofwaterloggingRightList = ref(null)
const getImpactofwaterloggingRightFc = async () => {
  try {
    const result = await getImpactofwaterloggingRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        impactofwaterloggingRight.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        impactofwaterloggingRightList.value = impactofwaterloggingRight.value[0].data
        // console.log('11111111111111112222222222:', impactofwaterloggingRight.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 人员调配统计
const personnelDeploymentRight = ref(null)
const personnelDeploymentRightList = ref(null)
const getPersonnelDeploymentRightFc = async () => {
  try {
    const result = await getPersonnelDeploymentRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        personnelDeploymentRight.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        personnelDeploymentRightList.value = personnelDeploymentRight.value[0].data
        // console.log('11111111111111112222222222:', personnelDeploymentRight.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 报警事件类型统计
const alarmeventRight = ref(null)
const alarmeventRightList = ref(null)
const getAlarmeventRightFc = async () => {
  try {
    const result = await getAlarmeventRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        alarmeventRight.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        alarmeventRightList.value = alarmeventRight.value[0].data
        // console.log('11111111111111112222222222:', alarmeventRight.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 排水防涝统计/排水预警统计
const drainageDataRight = ref(null)
const drainageDataRightList = ref(null)
const rainfallRight = ref(null)
const getDrainageDataRightFc = async () => {
  try {
    const result = await getDrainageDataRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        drainageDataRight.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        rainfallRight.value = drainageDataRight.value[0].rainfall
        drainageDataRightList.value = drainageDataRight.value[0].flood_points
        // console.log('11111111111111112222222222:', drainageDataRight.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 左边
// 设备报警情况
const equipmentAlarmDetail = ref(null)
const equipmentAlarmDetailList = ref(null)
const getEquipmentAlarmDetailFc = async () => {
  try {
    const result = await getEquipmentMaintenance()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        equipmentAlarmDetail.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        equipmentAlarmDetailList.value = equipmentAlarmDetail.value[0].data
        // console.log('11111111111111112222222222:', equipmentAlarmDetail.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 设备报警
const equipmentAlarm = ref(null)
const equipmentAlarmList = ref(null)
const getEquipmentAlarmFc = async () => {
  try {
    const result = await getEquipmentAlarm()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        equipmentAlarm.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        equipmentAlarmList.value = equipmentAlarm.value[0].data
        // console.log('11111111111111112222222222:', equipmentAlarm.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 积涝统计
const accumulatedFlood = ref(null)
const accumulatedFloodList = ref(null)
const riskOverview = ref(null)
const getAccumulatedFloodFc = async () => {
  try {
    const result = await getAccumulatedFlood()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        accumulatedFlood.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        accumulatedFloodList.value = accumulatedFlood.value[0].data
        riskOverview.value = accumulatedFlood.value[0]
        // console.log('11111111111111112222222222:', accumulatedFlood.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 物资调配统计
const materialAllocation = ref(null)
const materialAllocationList = ref(null)
const getMaterialAllocationFc = async () => {
  try {
    const result = await getMaterialAllocation()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        materialAllocation.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        materialAllocationList.value = materialAllocation.value[0].data
        // console.log('11111111111111112222222222:', materialAllocation.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 内涝影响分析
const impactofwaterlogging = ref(null)
const impactofwaterloggingList = ref(null)
const getImpactofwaterloggingFc = async () => {
  try {
    const result = await getImpactofwaterlogging()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        impactofwaterlogging.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        impactofwaterloggingList.value = impactofwaterlogging.value[0].data
        // console.log('11111111111111112222222222:', impactofwaterlogging.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 人员调配统计
const personnelDeployment = ref(null)
const personnelDeploymentList = ref(null)
const getPersonnelDeploymentFc = async () => {
  try {
    const result = await getPersonnelDeployment()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        personnelDeployment.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean)
        // 过滤掉解析失败的数据
        personnelDeploymentList.value = personnelDeployment.value[0].data
        // console.log('111111111111111122222222222加载成功·实时:', personnelDeployment.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 报警事件类型统计
const alarmevent = ref(null)
const alarmeventList = ref(null)
const getAlarmeventFc = async () => {
  try {
    const result = await getAlarmevent()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        alarmevent.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        alarmeventList.value = alarmevent.value[0].data
        // console.log('111111111111111122222222222加载成功·实时:', alarmevent.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 排水防涝统计/排水预警统计
const drainageData = ref(null)
const drainageDataList = ref(null)
const rainfall = ref(null)
const getDrainageDataFc = async () => {
  try {
    const result = await getDrainageData()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        drainageData.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        drainageDataList.value = drainageData.value[0].flood_points
        rainfall.value = drainageData.value[0].rainfall
        // console.log('111111111111111122222222222加载成功·实时:', drainageData.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

onMounted(async () => {
  // 左边
  await getDrainageDataFc()
  await getAlarmeventFc()
  await getPersonnelDeploymentFc()
  await getImpactofwaterloggingFc()
  await getMaterialAllocationFc()
  await getAccumulatedFloodFc()
  await getEquipmentAlarmFc()
  await getEquipmentAlarmDetailFc()

  // 右边
  await getDrainageDataRightFc()
  await getAlarmeventRightFc()
  await getPersonnelDeploymentRightFc()
  await getImpactofwaterloggingRightFc()
  await getMaterialAllocationRightFc()
  await getAccumulatedFloodRightFc()
  await getEquipmentAlarmRightFc()
  await getEquipmentMaintenanceRightFc()

  // 初始化审计类型统计图表
  initAuditChart()
  initAuditChart1()
})

// 监听报警事件数据变化，更新图表
watch(
  alarmeventList,
  newVal => {
    if (newVal && auditChart) {
      auditChart.setOption({
        series: [
          {
            data: newVal
          }
        ]
      })
    }
  },
  { deep: true }
)

// 监听右侧报警事件数据变化，更新图表
watch(
  alarmeventRightList,
  newVal => {
    if (newVal && auditChart1) {
      auditChart1.setOption({
        series: [
          {
            data: newVal
          }
        ]
      })
    }
  },
  { deep: true }
)

// 获取assets静态资源
const getAssetsFile = url => {
  // 检查是否包含斜杠，如果包含则认为是带文件夹路径的
  if (url.includes('/')) {
    return new URL(`../../assets/images/${url}`, import.meta.url).href
  } else {
    // 不包含斜杠，默认从home文件夹获取
    return new URL(`../../assets/images/home/<USER>
  }
}

// 根据标题获取背景类名
const getBackgroundClass = title => {
  if (title === '轻度影响') {
    return 'bg-light'
  } else if (title === '较重影响') {
    return 'bg-medium'
  } else if (title === '重度影响') {
    return 'bg-heavy'
  }
  return 'bg-light' // 默认背景
}
// 时间和日期
const currentTime = ref('')
const currentDate = ref('')

// 添加内涝积水点统计数据
const floodingStats = ref([
  { area: '央宣坪区', value: 3 },
  { area: '晋源区', value: 0 },
  { area: '杏花岭区', value: 7 },
  { area: '万柏林区', value: 5 },
  { area: '迎泽区', value: 6 },
  { area: '小店区', value: 5 }
])

// 人员调配统计数据
const personnelTableData = ref([
  { id: '02', name: '消防一大队', manager: '张xx', count: 4, phone: '123*254' },
  { id: '01', name: '消防一大队', manager: '王xx', count: 3, phone: '123*254' },
  { id: '03', name: '消防一大队', manager: '刘xx', count: 2, phone: '123*254' },
  { id: '04', name: '消防一大队', manager: '王xx', count: 1, phone: '123*125' },
  { id: '05', name: '消防一大队', manager: '张xx', count: 4, phone: '123*254' },
  { id: '06', name: '消防一大队', manager: '刘xx', count: 2, phone: '123*254' },
  { id: '06', name: '消防一大队', manager: '刘xx', count: 2, phone: '123*254' },
  { id: '06', name: '消防一大队', manager: '刘xx', count: 2, phone: '123*254' },
  { id: '07', name: '消防一大队', manager: '王xx', count: 3, phone: '123*254' }
])

// 内涝影响分析模块数据
const contentBlocks = ref([
  {
    title: '轻度',
    items: [
      { content: '道路', title: '道路' },
      { content: '积水点', title: '积水点' },
      { content: '人员', title: '人员' }
    ]
  },
  {
    title: '中度',
    items: [
      { content: '道路', title: '道路' },
      { content: '积水点', title: '积水点' },
      { content: '人员', title: '人员' }
    ]
  },
  {
    title: '重度',
    items: [
      { content: '道路', title: '道路' },
      { content: '积水点', title: '积水点' },
      { content: '人员', title: '人员' }
    ]
  }
])

// 积劳统计表格数据
const drainageTableData = ref([
  {
    id: '01',
    route: '低速线路1',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '02',
    route: '低速线路2',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '03',
    route: '低速线路3',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '01',
    route: '低速线路1',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '02',
    route: '低速线路2',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '03',
    route: '低速线路3',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '01',
    route: '低速线路1',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '01',
    route: '低速线路1',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '01',
    route: '低速线路1',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  }
])

// 物资调配统计数据
const materialTableData = ref([
  { id: '01', name: '抽水泵', quantity: 2, manager: '王xx', phone: '123**254' },
  { id: '02', name: '抽水泵', quantity: 1, manager: '张xx', phone: '123**254' },
  { id: '03', name: '抽水泵', quantity: 3, manager: '林xx', phone: '123**254' },
  { id: '01', name: '抽水泵', quantity: 2, manager: '王xx', phone: '123**254' },
  { id: '02', name: '抽水泵', quantity: 1, manager: '张xx', phone: '123**254' },
  { id: '02', name: '抽水泵', quantity: 1, manager: '张xx', phone: '123**254' },
  { id: '02', name: '抽水泵', quantity: 1, manager: '张xx', phone: '123**254' },
  { id: '03', name: '抽水泵', quantity: 3, manager: '林xx', phone: '123**254' },
  { id: '03', name: '抽水泵', quantity: 3, manager: '林xx', phone: '123**254' }
])

// 设备报警数据
const equipmentAlarmData = ref([
  { name: '低洼地段', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '人行下穿', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '车辆下穿', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '棚户区', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '地下商超', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '密集区域', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '公共地下停车', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '涉河入口', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '雨水管网', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '污水处理厂', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '缓洪池', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '排水渠', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '雨污水泵站', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '泵车', icon: 'https://picsum.photos/100/100', status: '3/63/2' }
])

// 设备情况表格数据
const equipmentTableData = ref([
  {
    id: '01',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '01',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '01',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '01',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '01',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '01',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  }
])

// 审计类型统计数据 - 保留作为备用数据
const auditTypeData = [
  { value: 60, name: '设备故障' },
  { value: 40, name: '灾洪超标' },
  { value: 30, name: '官网拥堵' }
]

let timer = null
let auditChart = null
let auditChart1 = null

// 更新时间函数
const updateTime = () => {
  const now = new Date()

  // 格式化时分秒
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  const seconds = now.getSeconds().toString().padStart(2, '0')
  currentTime.value = `${hours}:${minutes}:${seconds}`

  // 格式化年月日
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  currentDate.value = `${year}/${month}/${day}`
}

// 初始化审计类型统计图表
const initAuditChart = () => {
  const chartDom = document.getElementById('audit-chart')
  if (!chartDom) return

  auditChart = echarts.init(chartDom)

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      show: true,
      orient: 'vertical',
      right: '5%',
      top: 'center',
      itemGap: 15,
      textStyle: {
        color: '#D8F1FF',
        fontSize: 18
      },
      itemWidth: 15,
      itemHeight: 15,
      itemStyle: {
        borderWidth: 0
      },
      formatter: name => {
        const item = alarmeventList.value ? alarmeventList.value.find(item => item.name === name) : null
        return `${name}  ${item ? item.value : ''}`
      }
    },
    color: ['#00A7FF', '#FFCC00', '#00CC99'],
    series: [
      {
        name: '报警事件类型',
        type: 'pie',
        right: '15%',
        radius: ['35%', '70%'],
        avoidLabelOverlap: false,
        // itemStyle: {
        // borderRadius: 0,
        //  borderColor: '#042B52',
        //  borderWidth: 2
        // },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{c}',
          color: '#D8F1FF',
          fontSize: 18,
          fontWeight: 'normal',
          lineHeight: 18
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          smooth: false,
          lineStyle: {
            color: '#D8F1FF'
          }
        },
        data: alarmeventList.value || auditTypeData
      }
    ]
  }

  auditChart.setOption(option)
}
// 初始化审计类型统计图表
const initAuditChart1 = () => {
  const chartDom = document.getElementById('audit-chart1')
  if (!chartDom) return

  auditChart1 = echarts.init(chartDom)

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      show: true,
      orient: 'vertical',
      right: '5%',
      top: 'center',
      itemGap: 15,
      textStyle: {
        color: '#D8F1FF',
        fontSize: 22
      },
      itemWidth: 15,
      itemHeight: 15,
      icon: 'stack',
      itemStyle: {
        borderWidth: 0,
        borderRadius: 0
      },
      formatter: name => {
        const item = alarmeventRightList.value ? alarmeventRightList.value.find(item => item.name === name) : null
        return `${name}  ${item ? item.value : ''}`
      }
    },
    color: ['#00A7FF', '#FFCC00', '#00CC99'],
    series: [
      {
        name: '报警事件类型',
        type: 'pie',
        right: '15%',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        // itemStyle: {
        //   borderRadius: 0,
        //   borderColor: '#042B52',
        //   borderWidth: 2
        // },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{c}',
          color: '#D8F1FF',
          fontSize: 22,
          fontWeight: 'normal',
          lineHeight: 18
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          smooth: false,
          lineStyle: {
            color: '#D8F1FF'
          }
        },
        data: alarmeventRightList.value || auditTypeData
      }
    ]
  }

  auditChart1.setOption(option)
}

// 连接数字孪生配置
const options = ref({
  domId: 'player', // 修改为新的ID
  apiOptions: {
    onReady: function () {
      console.info('此时可以调API了')
    }
  }
})

const api = ref(null)
// 太原数字孪生内网地址
const host = ref(import.meta.env.VITE_DTS_URL)
// 组件挂载时启动定时器和初始化屏幕适配
onMounted(async () => {
  updateTime() // 立即执行一次
  timer = setInterval(updateTime, 1000) // 每秒更新一次
  // 修改这里，接入地图配置
  try {
    // 确保先引入了ac.min.js
    if (typeof acapi !== 'undefined') {
      // 创建数字孪生平台实例
      console.log('加载飞渡')
      api.value = new DigitalTwinPlayer(host.value, options.value)
      console.log('数字孪生平台初始化成功')
    } else {
      console.error('ac.min.js未正确加载，请检查引入路径')
    }
  } catch (error) {
    console.error('数字孪生平台初始化失败:', error)
  }

  // 监听窗口大小变化，重绘图表
  window.addEventListener('resize', () => {
    if (auditChart) {
      auditChart.resize()
    }
    if (auditChart1) {
      auditChart1.resize()
    }
  })
})

// 组件卸载时清除定时器和屏幕适配事件监听
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }

  // 销毁DTS实例
  if (api.value) {
    api.value = null
  }

  // 销毁图表实例
  if (auditChart) {
    auditChart.dispose()
    auditChart = null
  }
  if (auditChart1) {
    auditChart1.dispose()
    auditChart1 = null
  }

  // 移除窗口大小变化监听
  window.removeEventListener('resize', () => {
    if (auditChart) {
      auditChart.resize()
    }
    if (auditChart1) {
      auditChart1.resize()
    }
  })
})
</script>

<style lang="scss" scoped>
// Global module header styles
.module-header,
.equipment-header,
.top-title-area,
.bottom-title-area,
.drainage-title {
  background-image: url('@/assets/images/home/<USER>') !important;
  background-size: 100% 100% !important;
  background-repeat: no-repeat !important;
  padding: 0 vh(12) 0 vw(45) !important;
  border-radius: 0 !important;
}

// Remove border-radius from all content containers
.module-item-1,
.module-item-2,
.module-item-3,
.second-module-1,
.second-module-2,
.second-module-3,
.module-2-top,
.module-2-middle,
.module-2-bottom,
.module-3-top,
.equipment-container,
.content-item,
.stats-card,
.grid-item,
.chart-container,
.personnel-table-container,
.drainage-table-container,
.material-table-container,
.equipment-table-container,
.equipment-filter,
.drainage-title,
.filter-input {
  border-radius: 0 !important;
}

.module-title,
.equipment-title,
.area-title,
.drainage-title,
.title-text {
  font-size: vh(26) !important;
  color: #d8f1ff !important;
  font-family: JiangChengXieHei !important;
  font-weight: normal !important;
}

.module-time {
  color: #d8f1ff !important;
}

// 屏幕容器
.screen-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

// 背景层
.bg-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  pointer-events: none;
  z-index: 1;
}

.bg-layer-1 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 1;
}

.bg-layer-2 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 2;
}

.bg-layer-3 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 3;
}

// 地图容器样式
.map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: auto;
}

// 拆分为三个独立的容器
.left-container,
.middle-container,
.right-container {
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.left-container {
  left: 0;
  width: 35%;
  height: 100%;
  padding: vh(40) vw(0) vh(40) vw(100);
  // background: rgb(150, 214, 153);
  // background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(19, 44, 57, 0.7745) 15%, rgba(19, 44, 57, 0.95) 100%);
  .left-section {
    width: 100%;
    height: 100%;
    pointer-events: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .time-weather {
    display: flex;
    align-items: center;
    color: #fff;
    padding-left: vw(90);
    // margin-bottom: vh(5);
    // background: blue;
    .time-date {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }

    .time {
      font-size: vh(24);
    }

    .date {
      font-size: vh(14);
      margin-right: vw(2);
    }

    .divider {
      margin: 0 vw(20);
      width: vw(1);
      height: vh(30);
      background-color: #3a607c;
    }

    .weather {
      display: flex;
      align-items: center;
    }

    .weather-icon {
      width: vw(24);
      height: vh(24);
      margin-right: vw(10);
    }

    .weather-icon1 {
      width: vw(24);
      height: vh(22);
    }

    .temperature {
      font-size: vh(24);
    }
  }
  .content-layout-first {
    flex: 1;
    width: 100%;
    // background: rgba(0, 0, 0, 0.2);
    .content-layout-header {
      width: 100%;
      height: vh(120);
      background-image: url('@/assets/images/weather-bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 vw(95);

      .weather-info-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 100%;
        color: #fff;
        padding: 0 vw(15);
      }

      .current-weather {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 100%;
        gap: vw(48);
      }

      .weather-label {
        font-weight: normal;
        font-size: vh(24);
        color: #ffffff;
        line-height: vh(33);
      }

      .city-info {
        display: flex;
        align-items: center;
      }

      .location-icon {
        display: inline-block;
        width: vw(22);
        height: vh(24);
        margin-right: vw(20);
      }

      .city-name {
        font-weight: normal;
        font-size: vh(32);
        color: #ffffff;
        margin-top: vh(-8);
      }

      .weather-detail {
        display: flex;
        align-items: center;
        height: 100%;
        gap: vw(15);
      }

      .weather-icon-large {
        width: vw(58);
        height: vh(61);
        margin-right: vw(10);
      }

      .weather-icon-large img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .weather-data {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .weather-type {
        display: flex;
        align-items: center;
        gap: vw(10);
      }

      .weather-name {
        font-weight: normal;
        font-size: vh(30);
        color: #ffffff;
        line-height: vh(42);
        text-align: center;
        font-style: normal;
      }

      .weather-temp {
        font-weight: normal;
        font-size: vh(22);
        color: #ffffff;
        line-height: vh(30);
        font-style: normal;
        text-align: center;
        width: vw(72);
        height: vh(31);
        background: #1ecfa5;
        border-radius: vw(16);
      }

      .wind-info {
        display: flex;
        align-items: center;
        gap: vw(20);
        font-weight: normal;
        font-size: vh(27);
        color: #ffffff;
        text-align: left;
        font-style: normal;
        margin-top: vh(3);
      }

      .weather-forecast {
        display: flex;
        height: 100%;
        align-items: center;
      }

      .forecast-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 vw(30);
      }

      .forecast-title {
        font-weight: normal;
        font-size: vh(28);
        color: #ffffff;
        text-align: left;
        font-style: normal;
      }

      .forecast-value {
        .forecast-value-num {
          font-weight: bold;
          font-size: vh(33);
          color: #24ceb8;
          font-style: normal;
        }

        .forecast-value-unit {
          font-weight: normal;
          font-size: vh(24);
          color: #ffffff;
          font-style: normal;
        }
      }

      .forecast-value1 {
        .forecast-value-num {
          font-weight: bold;
          font-size: vh(33);
          color: #92d3ec;
          font-style: normal;
        }

        .forecast-value-unit {
          font-weight: normal;
          font-size: vh(24);
          color: #ffffff;
          font-style: normal;
        }
      }

      .publish-info {
        width: vw(395);
        height: vh(77);
        background: rgba(110, 204, 255, 0.14);
        border-radius: vw(1);
        display: flex;
        justify-content: center;
        align-items: center;
        gap: vw(20);
      }

      .publish-label {
        font-weight: normal;
        font-size: vh(25);
        color: #ffffff;
        font-style: normal;
      }

      .publish-times {
        font-weight: normal;
        font-size: vh(22);
        color: #ffffff;
        font-style: normal;
      }

      .time-item {
        font-weight: normal;
        font-size: vh(22);
        color: #ffffff;
        font-style: normal;
      }

      .divider-line {
        width: vw(1);
        height: vh(70);
      }
    }
  }
  .left-content {
    // background: red;
    // background: rgba(255, 255, 255, 0.24);
    width: 100%;
    height: calc(100% - vh(180));

    .content-layout-second {
      width: 100%;
      height: 100%;
      display: flex;
      gap: vw(20);

      .second-module-1 {
        flex: 3;
        height: 100%;
        border-radius: vh(4);
        display: flex;
        flex-direction: column;
        gap: vh(20);

        .module-item-1 {
          flex: 3;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          display: flex;
          flex-direction: column;
        }

        .module-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: vh(10);
          // background: rgba(6, 72, 146, 0.5);
          padding: vh(8) vh(12);
          border-radius: vh(4);

          .module-title {
            font-weight: normal;
            font-size: vh(28);
            color: #ffffff;
            font-style: normal;
          }

          .module-time {
            font-weight: normal;
            font-size: vh(22);
            color: #ffffff;
            font-style: normal;
          }
        }

        .module-content {
          display: flex;
          flex-direction: column;
          gap: vh(10);
          padding: vh(0) vw(10);
          height: 100%;
          justify-content: center;

          .stats-row {
            display: flex;
            justify-content: space-between;
            gap: vw(10);
          }

          .stats-col {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: vh(10);
            background: rgba(27, 105, 190, 0.24);
          }

          .stats-label {
            font-weight: 400;
            font-size: vh(23);
            height: vh(30);
            margin-bottom: vh(10);
            color: #ffffff;
            font-style: normal;
            background-image: url('@/assets/images/home/<USER>');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: left;
            padding: 0 vw(35);
            display: flex;
            justify-content: space-between;
            align-items: center;
            .title {
              font-weight: normal;
              font-size: vh(23);
              color: #ffffff;
              font-style: normal;
            }
            .time {
              font-weight: normal;
              font-size: vh(20);
            }
          }

          .stats-card {
            display: flex;
            align-items: center;
            gap: vw(10);
            border-radius: vh(4);
            padding: vh(10);
            height: vh(95); // 添加固定高度
            box-sizing: border-box; // 确保padding不会增加元素总高度
          }

          .stats-icon {
            width: vw(40);
            height: vh(40);
            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }

          .stats-data {
            display: flex;
            flex-direction: column;
          }

          .stats-value {
            font-weight: 700;
            font-size: vh(24);
            color: #fff;
            font-style: normal;
          }

          .stats-unit {
            font-weight: normal;
            font-size: vh(22);
            font-weight: 350;
            color: #949fbc;
            font-style: normal;
          }

          .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: vw(10);
          }

          .grid-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: vh(4);
            height: vh(95); // 添加与stats-card相同的高度
            box-sizing: border-box; // 确保padding不会增加元素总高度
            transition: all 0.3s ease;
          }

          .grid-value {
            font-weight: 700;
            font-size: vh(36);
            color: #fff;
            font-style: normal;
            background-image: url('@/assets/images/assistantdecision/nlbg.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: bottom;
            height: vh(68);
            width: 100%;
            display: flex;
            align-items: flex-start;
            justify-content: center;
          }

          .grid-label {
            font-weight: 400;
            font-size: vh(20);
            color: #ffffff;
            font-style: normal;
          }
        }

        .module-item-2 {
          flex: 3;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          .module-header {
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            // background: rgba(6, 72, 146, 0.5);
            padding: vh(8) vh(12);
            border-radius: vh(4);

            .module-title {
              font-weight: normal;
              font-size: vh(28);
              color: #ffffff;
              font-style: normal;
            }
          }

          .chart-container {
            width: 100%;
            height: calc(100% - vh(60));
            display: flex;
            justify-content: center;
            align-items: center;
            padding: vh(10);
            background: rgba(255, 255, 255, 0.05);
            border-radius: vh(4);

            .chart {
              width: 100%;
              height: 100%;
              box-sizing: border-box;
            }
          }
        }

        .module-item-3 {
          flex: 4;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          display: flex;
          flex-direction: column;

          .module-header {
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            // background: rgba(6, 72, 146, 0.5);
            padding: vh(8) vh(12);
            border-radius: vh(4);
            margin-bottom: vh(10);

            .module-title {
              font-weight: normal;
              font-size: vh(28);
              color: #ffffff;
              font-style: normal;
            }
          }

          .personnel-table-container {
            flex: 1;
            width: 100%;
            border-radius: vh(4);
            padding: vh(10);
            display: flex;
            flex-direction: column;
          }

          .personnel-table {
            width: 100%;
            border-collapse: collapse;
            color: #ffffff;
            table-layout: fixed;

            th {
              background: #0d4873;
              padding: vh(10) vw(5);
              text-align: center;
              font-size: vh(22);
              font-weight: normal;
              border: none;
              border-bottom: 1px solid rgba(255, 255, 255, 0.2);
              position: sticky;
              top: 0;
              z-index: 1;
            }

            td {
              padding: vh(10) vw(5);
              text-align: center;
              font-size: vh(24);
              border: none;
              border-bottom: 1px solid rgba(255, 255, 255, 0.2);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: vw(100);
            }

            tbody {
              display: block;
              max-height: vh(320);
              overflow-y: auto;

              /* 隐藏滚动条 */
              &::-webkit-scrollbar {
                width: 0;
                display: none;
              }
              scrollbar-width: none; /* Firefox */
              -ms-overflow-style: none; /* IE and Edge */
            }

            thead,
            tbody tr {
              display: table;
              width: 100%;
              table-layout: fixed;
            }
          }
        }
      }

      .second-module-2 {
        flex: 4;
        height: 100%;
        border-radius: vh(4);
        display: flex;
        flex-direction: column;
        gap: vh(20);
        overflow: hidden; /* 添加overflow:hidden防止内容溢出 */

        .module-2-top {
          flex: 2;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          display: flex;
          flex-direction: column;
          gap: vh(10);
          overflow: hidden; /* 添加overflow:hidden防止内容溢出 */
          min-height: 0; /* 确保flex子项可以收缩 */

          .top-title-area {
            height: vh(45);
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            // background: rgba(6, 72, 146, 0.5);
            padding: vh(8) vh(12);
            border-radius: vh(4);

            .area-title {
              font-weight: normal;
              font-size: vh(24);
              color: #ffffff;
              font-style: normal;
            }
          }

          .top-content-area {
            width: 100%;
            height: calc(100% - vh(45));
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            gap: vh(10);

            .content-block {
              flex: 1;
              height: 100%;
              border-radius: vh(4);
              padding: vh(10);
              background: rgba(27, 105, 190, 0.24);
              display: flex;
              flex-direction: column;
              gap: vh(5);

              .block-title {
                font-weight: 400;
                font-size: vh(23);
                height: vh(30);
                margin-bottom: vh(10);
                color: #ffffff;
                font-style: normal;
                background-image: url('@/assets/images/home/<USER>');
                background-size: cover;
                background-repeat: no-repeat;
                background-position: left;
                padding: 0 vw(35);
                display: flex;
                justify-content: space-between;
                align-items: center;

                .title-text {
                  font-weight: normal;
                  font-size: vh(18);
                  color: #ffffff;
                  font-style: normal;
                }
              }

              .block-content {
                flex: 1;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                gap: vh(5);

                .content-item {
                  flex: 1;

                  border-radius: vh(4);
                  padding: vh(5);
                  font-weight: normal;
                  font-size: vh(16);
                  color: #ffffff;
                  font-style: normal;
                  text-align: center;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;

                  .content-item-value {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    height: 100%;

                    .content-item-value-text {
                      width: vw(82);
                      height: vh(82);
                      background-size: cover;
                      background-repeat: no-repeat;
                      background-position: center;
                      margin-bottom: vh(5);
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      font-size: vh(35);
                      font-weight: 700;
                      color: #ffffff;
                      &.bg-light {
                        background-image: url('@/assets/images/assistantdecision/qdbg.png');
                      }

                      &.bg-medium {
                        background-image: url('@/assets/images/assistantdecision/zdbg.png');
                      }

                      &.bg-heavy {
                        background-image: url('@/assets/images/assistantdecision/zhgbg.png');
                      }
                    }
                  }
                  .content-item-title-box {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    height: 100%;
                  }

                  .content-item-title {
                    font-size: vh(23);
                    color: #ffffff;
                    width: vw(82);
                  }
                }
              }
            }
          }
        }

        .module-2-middle {
          flex: 4;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          display: flex;
          flex-direction: column;
          overflow: hidden; /* 添加overflow:hidden防止内容溢出 */
          min-height: 0; /* 确保flex子项可以收缩 */

          .drainage-stats {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden; /* 添加overflow:hidden防止内容溢出 */

            .drainage-title {
              font-size: vh(28);
              color: #ffffff;
              height: vh(45);
              display: flex;
              align-items: center;
            }

            .risk-overview {
              display: flex;
              justify-content: space-around;
              // margin: vh(10) 0;
              padding: vh(17) vh(15);
              border-radius: vh(4);

              .risk-item {
                display: flex;
                align-items: center;

                .risk-label {
                  font-size: vh(22);
                  color: #ffffff;
                  font-weight: 700;
                  margin-right: vh(10);
                }

                .risk-value {
                  font-weight: 900;
                  font-size: vh(28);
                  background-image: -webkit-linear-gradient(bottom, #ffffff, #e2b500);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                  font-weight: bold;
                  margin-right: vh(5);
                }

                .risk-trend {
                  font-size: vh(26);
                  background-image: -webkit-linear-gradient(bottom, #ffffff, #e2b500);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
              }
            }

            .drainage-table-container {
              flex: 1;
              border-radius: vh(4);
              padding: vh(10);
              min-height: 0; /* 确保flex子项可以收缩 */

              .drainage-table {
                width: 100%;
                border-collapse: collapse;
                color: #ffffff;
                table-layout: fixed;

                th {
                  background: #0d4873;
                  padding: vh(15) vw(5);
                  text-align: center;
                  font-size: vh(20);
                  font-weight: normal;
                  border: 1px solid rgba(255, 255, 255, 0.2);
                  position: sticky;
                  top: 0;
                  z-index: 1;
                }

                td {
                  padding: vh(8) vw(5);
                  text-align: center;
                  font-size: vh(24);
                  border: none;
                  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  max-width: vw(100);
                }

                tbody {
                  display: block;
                  max-height: vh(240);
                  overflow-y: auto;

                  /* 隐藏滚动条 */
                  &::-webkit-scrollbar {
                    width: 0;
                    display: none;
                  }
                  scrollbar-width: none; /* Firefox */
                  -ms-overflow-style: none; /* IE and Edge */
                }

                thead,
                tbody tr {
                  display: table;
                  width: 100%;
                  table-layout: fixed;
                }
              }
            }
          }
        }

        .module-2-bottom {
          flex: 2.9;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          display: flex;
          flex-direction: column;
          padding: vh(10);
          gap: vh(10);
          overflow: hidden; /* 添加overflow:hidden防止内容溢出 */
          min-height: 0; /* 确保flex子项可以收缩 */

          .bottom-title-area {
            height: vh(45);
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            // background: rgba(6, 72, 146, 0.7);
            padding: vh(8) vh(12);

            .area-title {
              font-weight: normal;
              font-size: vh(24);
              color: #ffffff;
              font-style: normal;
            }
          }

          .bottom-content-area {
            flex: 1;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden; /* 添加overflow:hidden防止内容溢出 */

            .material-table-container {
              width: 100%;
              height: 100%;
              background: rgba(255, 255, 255, 0.05);
              border-radius: vh(4);
              padding: vh(10);
              display: flex;
              flex-direction: column;
              overflow: hidden; /* 添加overflow:hidden防止内容溢出 */

              .material-table {
                width: 100%;
                border-collapse: collapse;
                color: #ffffff;
                table-layout: fixed;
                border-spacing: 0;

                th {
                  background: #0d4873;
                  padding: vh(10) vw(5);
                  text-align: center;
                  font-size: vh(22);
                  font-weight: normal;
                  border: none;
                  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                  position: sticky;
                  top: 0;
                  z-index: 1;
                }

                td {
                  padding: vh(10) vw(5);
                  text-align: center;
                  font-size: vh(24);
                  border: none;
                  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  max-width: vw(100);
                }

                tbody {
                  display: block;
                  max-height: vh(280);
                  overflow-y: auto;

                  /* 隐藏滚动条 */
                  &::-webkit-scrollbar {
                    width: 0;
                    display: none;
                  }
                  scrollbar-width: none; /* Firefox */
                  -ms-overflow-style: none; /* IE and Edge */
                }

                thead,
                tbody tr {
                  display: table;
                  width: 100%;
                  table-layout: fixed;
                }

                tbody {
                  display: block;
                  max-height: vh(280);
                  overflow-y: auto;

                  /* 隐藏滚动条 */
                  &::-webkit-scrollbar {
                    width: 0;
                    display: none;
                  }
                  scrollbar-width: none; /* Firefox */
                  -ms-overflow-style: none; /* IE and Edge */
                }

                thead,
                tbody tr {
                  display: table;
                  width: 100%;
                  table-layout: fixed;
                }
              }
            }
          }
        }
      }

      .second-module-3 {
        flex: 3;
        height: 100%;
        gap: vh(20);
        border-radius: vh(4);
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        .module-3-top {
          width: 100%;
          height: 50%;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: center;
          background: linear-gradient(90deg, rgba(6, 72, 146, 0.24) 0%, rgba(6, 79, 156, 0.06) 100%);
          gap: vh(10);
          .top-title-area {
            height: vh(45);
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            // background: rgba(6, 72, 146, 0.7);
            padding: 0 0 0 vh(45);

            .area-title {
              font-weight: normal;
              font-size: vh(26);
              color: #ffffff;
              font-style: normal;
            }
          }

          .top-content-area {
            width: 100%;
            height: calc(100% - vh(45));
            // background: rgba(6, 72, 146, 0.5);
            border-radius: vh(4);
            padding: vh(10);
            overflow-y: auto;

            /* 隐藏滚动条 */
            &::-webkit-scrollbar {
              width: 0;
              display: none;
            }
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */

            .grid-container {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              grid-gap: vh(10);
              width: 100%;
              height: 100%;
            }

            .grid-item {
              border-radius: vh(4);
              display: flex;
              flex-direction: row;
              align-items: center;
              padding: vh(8) vh(10);
              transition: all 0.3s ease;

              .item-icon {
                width: vh(70);
                height: vh(70);
                min-width: vh(70);
                border-radius: vh(4);
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: vh(10);
                overflow: hidden;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
              }

              .item-text {
                display: flex;
                flex-direction: column;
                justify-content: center;
                flex: 1;
              }

              .item-name {
                font-size: vh(22);
                color: #949fbc;
                font-weight: 350;
                text-align: left;
              }

              .item-status {
                font-weight: 700;
                font-size: vh(24);
                color: #fff;
                margin-top: vh(5);
                text-align: left;
              }
            }
          }
        }

        .equipment-container {
          width: 100%;
          height: 53%;
          display: flex;
          flex-direction: column;
          background: linear-gradient(90deg, rgba(6, 72, 146, 0.24) 0%, rgba(6, 79, 156, 0.06) 100%);
          gap: vh(10);

          .equipment-header {
            height: vh(45);
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: vh(8) vh(12);
            border-radius: vh(4);

            .equipment-title {
              font-weight: normal;
              font-size: vh(26);
              color: #ffffff;
              font-style: normal;
            }
          }

          .equipment-filter {
            width: 100%;
            padding: vh(10);
            border-radius: vh(4);

            .filter-item {
              display: flex;
              align-items: center;
              gap: vw(10);

              .filter-label {
                font-size: vh(18);
                color: #ffffff;
                white-space: nowrap;
              }

              .filter-select {
                flex: 1;
                height: vh(40);
                border-radius: vh(4);
                color: #ffffff;
                padding: 0 vh(10);
                font-size: vh(18);
                background: rgba(13, 72, 115, 0.6);
                border: 1px solid #1f8ad4;
                position: relative;
                appearance: none;
                background-image: url("data:image/svg+xml;utf8,<svg fill='white' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/></svg>");
                background-repeat: no-repeat;
                background-position: right vh(10) center;

                &:focus {
                  outline: none;
                  border-color: rgba(255, 255, 255, 0.5);
                }

                option {
                  background-color: rgba(6, 72, 146, 0.9);
                  color: #ffffff;
                  padding: vh(5);
                  font-size: vh(18);
                }
              }
            }
          }

          .equipment-table-container {
            flex: 1;
            width: 100%;
            border-radius: vh(4);
            padding: vh(10);
            overflow: auto;

            /* 隐藏滚动条 */
            &::-webkit-scrollbar {
              width: 0;
              display: none;
            }
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */

            .equipment-table {
              width: 100%;
              border-collapse: collapse;
              color: #ffffff;
              table-layout: fixed;

              th {
                background: #0d4873;
                padding: vh(10) vw(5);
                text-align: center;
                font-size: vh(22);
                font-weight: normal;
                border: none;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                position: sticky;
                top: 0;
                z-index: 1;
              }

              td {
                padding: vh(10) vw(5);
                text-align: center;
                font-size: vh(24);
                border: none;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
              }

              tbody {
                display: block;
                max-height: vh(340);
                overflow-y: auto;

                /* 隐藏滚动条 */
                &::-webkit-scrollbar {
                  width: 0;
                  display: none;
                }
                scrollbar-width: none; /* Firefox */
                -ms-overflow-style: none; /* IE and Edge */
              }

              thead,
              tbody tr {
                display: table;
                width: 100%;
                table-layout: fixed;
              }
            }
          }
        }
      }
    }
  }
}

.middle-container {
  left: 46%;
  height: auto;
  .middle-section {
    /* 中间区域标题 */
    .section-title {
      font-family: YouSheBiaoTiHei;
      font-size: vh(80);
      color: #ffffff;
      text-align: center;
      font-style: normal;
    }

    /* 导航按钮 */
    .nav-buttons {
      display: flex;
      justify-content: center;
      gap: vw(20);
      margin-top: vh(60);
    }

    .nav-button {
      width: vw(200);
      height: vh(80);
      background-image: url('@/assets/images/home/<USER>');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      justify-content: center;
      padding-top: vh(14);
      font-family: JiangChengXieHei;
      color: #fff;
      font-size: vh(24);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .nav-button.active {
      background-image: url('@/assets/images/home/<USER>');
      color: #fff;
      font-weight: bold;
    }
  }
}

.right-container {
  left: 65%;
  width: 35%;
  padding: vh(40) vw(100) vh(40) vw(0);
  .right-section {
    width: 100%;
    height: 100%;

    pointer-events: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .user-portal-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: vh(10) vw(20);
    gap: vw(30);
    border-radius: vh(4);
    // background: rgba(6, 72, 146, 0.24); // 添加背景色

    .user-info,
    .portal-back {
      display: flex;
      align-items: center;
      gap: vw(10);

      img {
        width: vh(40);
        height: vh(40);
        border-radius: 50%;
        object-fit: cover;
      }

      span {
        color: #fff;
        font-size: vh(16);
      }
    }
  }

  .right-content {
    width: 100%;
    height: 96%;
    .content-layout-second {
      width: 100%;
      height: 100%;
      display: flex;
      gap: vw(20);

      .second-module-1 {
        flex: 3;
        height: 100%;

        border-radius: vh(4);
        display: flex;
        flex-direction: column;
        gap: vh(30);

        .module-item-1 {
          flex: 3;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          display: flex;
          flex-direction: column;
        }

        .module-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: vh(10);
          // background: rgba(6, 72, 146, 0.5);
          padding: vh(8) vh(12);
          border-radius: vh(4);

          .module-title {
            font-weight: normal;
            font-size: vh(28);
            color: #ffffff;
            font-style: normal;
          }

          .module-time {
            font-weight: normal;
            font-size: vh(22);
            color: #ffffff;
            font-style: normal;
          }
        }

        .module-content {
          display: flex;
          flex-direction: column;
          gap: vh(10);
          padding: vh(0) vw(10);
          height: 100%;
          justify-content: center;

          .stats-row {
            display: flex;
            justify-content: space-between;
            gap: vw(10);
          }

          .stats-col {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: vh(10);
            background: rgba(27, 105, 190, 0.24);
          }

          .stats-label {
            font-weight: 400;
            font-size: vh(23);
            height: vh(30);
            margin-bottom: vh(10);
            color: #ffffff;
            font-style: normal;
            background-image: url('@/assets/images/home/<USER>');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: left;
            padding: 0 vw(35);
            display: flex;
            justify-content: space-between;
            align-items: center;
            .title {
              font-weight: normal;
              font-size: vh(23);
              color: #ffffff;
              font-style: normal;
            }
            .time {
              font-weight: normal;
              font-size: vh(20);
            }
          }

          .stats-card {
            display: flex;
            align-items: center;
            gap: vw(10);
            border-radius: vh(4);
            padding: vh(10);
            height: vh(95); // 添加固定高度
            box-sizing: border-box; // 确保padding不会增加元素总高度
          }

          .stats-icon {
            width: vw(40);
            height: vh(40);
            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }

          .stats-data {
            display: flex;
            flex-direction: column;
          }

          .stats-value {
            font-weight: 700;
            font-size: vh(24);
            color: #fff;
            font-style: normal;
          }

          .stats-unit {
            font-weight: normal;
            font-size: vh(22);
            font-weight: 350;
            color: #949fbc;
            font-style: normal;
          }

          .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: vw(10);
          }

          .grid-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: vh(4);
            height: vh(95); // 添加与stats-card相同的高度
            box-sizing: border-box; // 确保padding不会增加元素总高度
            transition: all 0.3s ease;
          }

          .grid-value {
            font-weight: 700;
            font-size: vh(36);
            color: #fff;
            font-style: normal;
            background-image: url('@/assets/images/assistantdecision/nlbg.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: bottom;
            height: vh(68);
            width: 100%;
            display: flex;
            align-items: flex-start;
            justify-content: center;
          }

          .grid-label {
            font-weight: 400;
            font-size: vh(20);
            color: #ffffff;
            font-style: normal;
          }
        }

        .module-item-2 {
          flex: 3;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          .module-header {
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            // background: rgba(6, 72, 146, 0.5);
            padding: vh(8) vh(12);
            border-radius: vh(4);

            .module-title {
              font-weight: normal;
              font-size: vh(28);
              color: #ffffff;
              font-style: normal;
            }
          }

          .chart-container {
            width: 100%;
            height: calc(100% - vh(60));
            display: flex;
            justify-content: center;
            align-items: center;
            padding: vh(10);
            background: rgba(255, 255, 255, 0.05);
            border-radius: vh(4);

            .chart {
              width: 100%;
              height: 100%;
              box-sizing: border-box;
            }
          }
        }

        .module-item-3 {
          flex: 4;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          display: flex;
          flex-direction: column;

          .module-header {
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            // background: rgba(6, 72, 146, 0.5);
            padding: vh(8) vh(12);
            border-radius: vh(4);
            margin-bottom: vh(10);

            .module-title {
              font-weight: normal;
              font-size: vh(28);
              color: #ffffff;
              font-style: normal;
            }
          }

          .personnel-table-container {
            flex: 1;
            width: 100%;
            border-radius: vh(4);
            padding: vh(10);
            display: flex;
            flex-direction: column;
          }

          .personnel-table {
            width: 100%;
            border-collapse: collapse;
            color: #ffffff;
            table-layout: fixed;

            th {
              background: #0d4873;
              padding: vh(10) vw(5);
              text-align: center;
              font-size: vh(22);
              font-weight: normal;
              border: none;
              border-bottom: 1px solid rgba(255, 255, 255, 0.2);
              position: sticky;
              top: 0;
              z-index: 1;
            }

            td {
              padding: vh(10) vw(5);
              text-align: center;
              font-size: vh(24);
              border: none;
              border-bottom: 1px solid rgba(255, 255, 255, 0.2);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: vw(100);
            }

            tbody {
              display: block;
              max-height: vh(340);
              overflow-y: auto;

              /* 隐藏滚动条 */
              &::-webkit-scrollbar {
                width: 0;
                display: none;
              }
              scrollbar-width: none; /* Firefox */
              -ms-overflow-style: none; /* IE and Edge */
            }

            thead,
            tbody tr {
              display: table;
              width: 100%;
              table-layout: fixed;
            }
          }
        }
      }

      .second-module-2 {
        flex: 4;
        height: 100%;
        border-radius: vh(4);
        display: flex;
        flex-direction: column;
        gap: vh(30);

        .module-2-top {
          flex: 2;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          display: flex;
          flex-direction: column;
          gap: vh(10);

          .top-title-area {
            height: vh(45);
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            // background: rgba(6, 72, 146, 0.5);
            padding: vh(8) vh(12);
            border-radius: vh(4);

            .area-title {
              font-weight: normal;
              font-size: vh(24);
              color: #ffffff;
              font-style: normal;
            }
          }

          .top-content-area {
            width: 100%;
            height: calc(100% - vh(45));
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            gap: vh(10);

            .content-block {
              flex: 1;
              height: 100%;
              border-radius: vh(4);
              padding: vh(10);
              background: rgba(27, 105, 190, 0.24);
              display: flex;
              flex-direction: column;
              gap: vh(5);

              .block-title {
                font-weight: 400;
                font-size: vh(23);
                height: vh(30);
                margin-bottom: vh(10);
                color: #ffffff;
                font-style: normal;
                background-image: url('@/assets/images/home/<USER>');
                background-size: cover;
                background-repeat: no-repeat;
                background-position: left;
                padding: 0 vw(35);
                display: flex;
                justify-content: space-between;
                align-items: center;

                .title-text {
                  font-weight: normal;
                  font-size: vh(18);
                  color: #ffffff;
                  font-style: normal;
                }
              }

              .block-content {
                flex: 1;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                gap: vh(5);

                .content-item {
                  flex: 1;

                  border-radius: vh(4);
                  padding: vh(5);
                  font-weight: normal;
                  font-size: vh(16);
                  color: #ffffff;
                  font-style: normal;
                  text-align: center;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;

                  .content-item-value {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    height: 100%;

                    .content-item-value-text {
                      width: vw(82);
                      height: vh(82);
                      background-size: cover;
                      background-repeat: no-repeat;
                      background-position: center;
                      margin-bottom: vh(5);
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      font-size: vh(35);
                      font-weight: 700;
                      color: #ffffff;
                      &.bg-light {
                        background-image: url('@/assets/images/assistantdecision/qdbg.png');
                      }

                      &.bg-medium {
                        background-image: url('@/assets/images/assistantdecision/zdbg.png');
                      }

                      &.bg-heavy {
                        background-image: url('@/assets/images/assistantdecision/zhgbg.png');
                      }
                    }
                  }
                  .content-item-title-box {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    height: 100%;
                  }

                  .content-item-title {
                    font-size: vh(23);
                    color: #ffffff;
                    width: vw(82);
                  }
                }
              }
            }
          }
        }

        .module-2-middle {
          flex: 4;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          display: flex;
          flex-direction: column;
          overflow: hidden; /* 添加overflow:hidden防止内容溢出 */
          min-height: 0; /* 确保flex子项可以收缩 */

          .drainage-stats {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden; /* 添加overflow:hidden防止内容溢出 */

            .drainage-title {
              font-size: vh(28);
              color: #ffffff;
              height: vh(45);
              display: flex;
              align-items: center;
            }

            .risk-overview {
              display: flex;
              justify-content: space-around;
              // margin: vh(10) 0;
              padding: vh(17) vh(15);
              border-radius: vh(4);

              .risk-item {
                display: flex;
                align-items: center;

                .risk-label {
                  font-size: vh(22);
                  color: #ffffff;
                  font-weight: 700;
                  margin-right: vh(10);
                }

                .risk-value {
                  font-weight: 900;
                  font-size: vh(28);
                  background-image: -webkit-linear-gradient(bottom, #ffffff, #e2b500);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                  font-weight: bold;
                  margin-right: vh(5);
                }

                .risk-trend {
                  font-size: vh(26);
                  background-image: -webkit-linear-gradient(bottom, #ffffff, #e2b500);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                }
              }
            }

            .drainage-table-container {
              flex: 1;
              overflow: auto;
              border-radius: vh(4);
              padding: vh(10);
              min-height: 0; /* 确保flex子项可以收缩 */

              /* 隐藏滚动条 */
              &::-webkit-scrollbar {
                width: 0;
                display: none;
              }
              scrollbar-width: none; /* Firefox */
              -ms-overflow-style: none; /* IE and Edge */

              .drainage-table {
                width: 100%;
                border-collapse: collapse;
                color: #ffffff;
                table-layout: fixed;

                th {
                  background: #0d4873;
                  padding: vh(15) vw(5);
                  text-align: center;
                  font-size: vh(20);
                  font-weight: normal;
                  border: 1px solid rgba(255, 255, 255, 0.2);
                  position: sticky;
                  top: 0;
                  z-index: 1;
                }

                td {
                  padding: vh(8) vw(5);
                  text-align: center;
                  font-size: vh(24);
                  border: none;
                  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  max-width: vw(100);
                }

                tbody {
                  display: block;
                  max-height: vh(280);
                  overflow-y: auto;

                  /* 隐藏滚动条 */
                  &::-webkit-scrollbar {
                    width: 0;
                    display: none;
                  }
                  scrollbar-width: none; /* Firefox */
                  -ms-overflow-style: none; /* IE and Edge */
                }

                thead,
                tbody tr {
                  display: table;
                  width: 100%;
                  table-layout: fixed;
                }
              }
            }
          }
        }

        .module-2-bottom {
          flex: 3;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          display: flex;
          flex-direction: column;
          padding: vh(10);
          gap: vh(10);
          overflow: hidden; /* 添加overflow:hidden防止内容溢出 */
          min-height: 0; /* 确保flex子项可以收缩 */

          .bottom-title-area {
            height: vh(45);
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            // background: rgba(6, 72, 146, 0.7);
            padding: vh(8) vh(12);

            .area-title {
              font-weight: normal;
              font-size: vh(24);
              color: #ffffff;
              font-style: normal;
            }
          }

          .bottom-content-area {
            flex: 1;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden; /* 添加overflow:hidden防止内容溢出 */

            .material-table-container {
              width: 100%;
              height: 100%;
              background: rgba(255, 255, 255, 0.05);
              border-radius: vh(4);
              padding: vh(10);
              display: flex;
              flex-direction: column;
              overflow: hidden; /* 添加overflow:hidden防止内容溢出 */

              .material-table {
                width: 100%;
                border-collapse: collapse;
                color: #ffffff;
                table-layout: fixed;
                border-spacing: 0;

                th {
                  background: #0d4873;
                  padding: vh(10) vw(5);
                  text-align: center;
                  font-size: vh(22);
                  font-weight: normal;
                  border: none;
                  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                  position: sticky;
                  top: 0;
                  z-index: 1;
                }

                td {
                  padding: vh(10) vw(5);
                  text-align: center;
                  font-size: vh(24);
                  border: none;
                  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  max-width: vw(100);
                }

                tbody {
                  display: block;
                  max-height: vh(320);
                  overflow-y: auto;

                  /* 隐藏滚动条 */
                  &::-webkit-scrollbar {
                    width: 0;
                    display: none;
                  }
                  scrollbar-width: none; /* Firefox */
                  -ms-overflow-style: none; /* IE and Edge */
                }

                thead,
                tbody tr {
                  display: table;
                  width: 100%;
                  table-layout: fixed;
                }

                tbody {
                  display: block;
                  max-height: vh(280);
                  overflow-y: auto;

                  /* 隐藏滚动条 */
                  &::-webkit-scrollbar {
                    width: 0;
                    display: none;
                  }
                  scrollbar-width: none; /* Firefox */
                  -ms-overflow-style: none; /* IE and Edge */
                }

                thead,
                tbody tr {
                  display: table;
                  width: 100%;
                  table-layout: fixed;
                }
              }
            }
          }
        }
      }

      .second-module-3 {
        flex: 3;
        height: 100%;

        border-radius: vh(4);
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        .module-3-top {
          width: 100%;
          height: 45%;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: center;
          background: linear-gradient(90deg, rgba(6, 72, 146, 0.24) 0%, rgba(6, 79, 156, 0.06) 100%);
          gap: vh(10);
          .top-title-area {
            height: vh(45);
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            // background: rgba(6, 72, 146, 0.7);
            padding: 0 0 0 vh(45);

            .area-title {
              font-weight: normal;
              font-size: vh(26);
              color: #ffffff;
              font-style: normal;
            }
          }

          .top-content-area {
            width: 100%;
            height: calc(100% - vh(45));
            // background: rgba(6, 72, 146, 0.5);
            border-radius: vh(4);
            padding: vh(10);
            overflow-y: auto;

            /* 隐藏滚动条 */
            &::-webkit-scrollbar {
              width: 0;
              display: none;
            }
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */

            .grid-container {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              grid-gap: vh(10);
              width: 100%;
              height: 100%;
            }

            .grid-item {
              border-radius: vh(4);
              display: flex;
              flex-direction: row;
              align-items: center;
              padding: vh(8) vh(10);
              transition: all 0.3s ease;

              .item-icon {
                width: vh(70);
                height: vh(70);
                min-width: vh(70);
                border-radius: vh(4);
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: vh(10);
                overflow: hidden;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
              }

              .item-text {
                display: flex;
                flex-direction: column;
                justify-content: center;
                flex: 1;
              }

              .item-name {
                font-size: vh(22);
                color: #949fbc;
                font-weight: 350;
                text-align: left;
              }

              .item-status {
                font-weight: 700;
                font-size: vh(24);
                color: #fff;
                margin-top: vh(5);
                text-align: left;
              }
            }
          }
        }

        .equipment-container {
          width: 100%;
          height: 53%;
          display: flex;
          flex-direction: column;
          background: linear-gradient(90deg, rgba(6, 72, 146, 0.24) 0%, rgba(6, 79, 156, 0.06) 100%);
          gap: vh(10);

          .equipment-header {
            height: vh(45);
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: vh(8) vh(12);
            border-radius: vh(4);

            .equipment-title {
              font-weight: normal;
              font-size: vh(26);
              color: #ffffff;
              font-style: normal;
            }
          }

          .equipment-filter {
            width: 100%;
            padding: vh(10);
            border-radius: vh(4);

            .filter-item {
              display: flex;
              align-items: center;
              gap: vw(10);

              .filter-label {
                font-size: vh(18);
                color: #ffffff;
                white-space: nowrap;
              }

              .filter-select {
                flex: 1;
                height: vh(40);
                border-radius: vh(4);
                color: #ffffff;
                padding: 0 vh(10);
                font-size: vh(18);
                background: rgba(13, 72, 115, 0.6);
                border: 1px solid #1f8ad4;
                position: relative;
                appearance: none;
                background-image: url("data:image/svg+xml;utf8,<svg fill='white' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/></svg>");
                background-repeat: no-repeat;
                background-position: right vh(10) center;

                &:focus {
                  outline: none;
                  border-color: rgba(255, 255, 255, 0.5);
                }

                option {
                  background-color: rgba(6, 72, 146, 0.9);
                  color: #ffffff;
                  padding: vh(5);
                  font-size: vh(18);
                }
              }
            }
          }

          .equipment-table-container {
            flex: 1;
            width: 100%;
            border-radius: vh(4);
            padding: vh(10);
            overflow: auto;

            /* 隐藏滚动条 */
            &::-webkit-scrollbar {
              width: 0;
              display: none;
            }
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */

            .equipment-table {
              width: 100%;
              border-collapse: collapse;
              color: #ffffff;
              table-layout: fixed;

              th {
                background: #0d4873;
                padding: vh(10) vw(5);
                text-align: center;
                font-size: vh(22);
                font-weight: normal;
                border: none;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                position: sticky;
                top: 0;
                z-index: 1;
              }

              td {
                padding: vh(10) vw(5);
                text-align: center;
                font-size: vh(24);
                border: none;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
              }

              tbody {
                display: block;
                max-height: vh(465);
                overflow-y: auto;

                /* 隐藏滚动条 */
                &::-webkit-scrollbar {
                  width: 0;
                  display: none;
                }
                scrollbar-width: none; /* Firefox */
                -ms-overflow-style: none; /* IE and Edge */
              }

              thead,
              tbody tr {
                display: table;
                width: 100%;
                table-layout: fixed;
              }
            }
          }
        }
      }
    }
  }
}
</style>
