{"name": "vue3_vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --open", "prod": "vite --mode production", "test": "vite --mode test", "build": "vite build", "preview": "vite preview", "lint": "eslint --ext .js,.vue --ignore-path .gitignore --fix src", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "build:test": "vite build --mode test"}, "dependencies": {"ant-design-vue": "^4.2.6", "axios": "^1.10.0", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "element-plus": "^2.10.4", "element-resize-detector": "^1.2.4", "flv.js": "^1.6.2", "hls.js": "^1.6.7", "pinia": "^3.0.0", "pinia-plugin-persistedstate": "^4.3.0", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/eslint-parser": "^7.28.0", "@eslint/css": "^0.9.0", "@eslint/js": "^9.30.1", "@eslint/json": "^0.12.0", "@vitejs/plugin-vue": "^6.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^7.0.0", "globals": "^16.3.0", "prettier": "^3.6.2", "prettier-eslint": "^16.4.2", "sass": "^1.89.2", "sass-loader": "^16.0.5", "terser": "^5.29.2", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.0"}}