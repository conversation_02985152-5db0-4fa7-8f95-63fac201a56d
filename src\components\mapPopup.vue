<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="20%"
    :before-close="handleClose"
    :close-on-click-modal="closeOnClickModal"
    :show-close="showClose"
    :destroy-on-close="destroyOnClose"
    :modal="false"
    class="custom-dialog"
    :style="dialogStyle"
  >
    <div class="dialog-content">
      <!-- 使用v-if/v-else根据数据源的存在与否显示不同内容 -->
      <div v-if="deviceData" class="popup-content">
        <div class="popup-list">
          <div v-if="deviceData.sbcode" class="popup-item">
            编码：<span class="popup-value">{{ deviceData.sbcode || '未知' }}</span>
          </div>
          <div v-if="deviceData.category" class="popup-item">
            分类：<span class="popup-value">{{ deviceData.category }}</span>
          </div>
          <div v-if="deviceData.sstype" class="popup-item">
            类型：<span class="popup-value">{{ deviceData.sstype }}</span>
          </div>
          <div v-if="deviceData.address" class="popup-item">
            地址：<span class="popup-value">{{ deviceData.address }}</span>
          </div>
          <div v-if="deviceData.memo" class="popup-item">
            厂商：<span class="popup-value">{{ deviceData.memo }}</span>
          </div>
          <div v-if="deviceData.address.includes('泵站')" class="popup-item">
            排水分区： <span class="popup-value">{{ deviceData.address }}</span>
          </div>

          <!-- 防汛车辆 -->
          <div v-if="deviceData.cphm" class="popup-item">
            cphm：<span class="popup-value">{{ deviceData.cphm }}</span>
          </div>
          <div v-if="deviceData.cxmc" class="popup-item">
            cxmc：<span class="popup-value">{{ deviceData.cxmc }}</span>
          </div>
          <div v-if="deviceData.clpp" class="popup-item">
            clpp：<span class="popup-value">{{ deviceData.clpp }}</span>
          </div>
          <div v-if="deviceData.jsrxm" class="popup-item">
            jsrxm：<span class="popup-value">{{ deviceData.jsrxm }}</span>
          </div>
          <div v-if="deviceData.ssbm" class="popup-item">
            ssbm：<span class="popup-value">{{ deviceData.ssbm }}</span>
          </div>
          <div v-if="deviceData.cjr" class="popup-item">
            cjr：<span class="popup-value">{{ deviceData.cjr }}</span>
          </div>
          <div v-if="deviceData.zrrlxdh" class="popup-item">
            zrrlxdh：<span class="popup-value">{{ deviceData.zrrlxdh }}</span>
          </div>
          <!-- 应急物资 -->
          <div v-if="deviceData.wzlx" class="popup-item">
            物资名称：<span class="popup-value">{{ deviceData.wzlx }}</span>
          </div>
          <div v-if="deviceData.sl" class="popup-item">
            物资数量：<span class="popup-value">{{ deviceData.sl }}</span>
          </div>
          <div v-if="deviceData.jldw" class="popup-item">
            单位：<span class="popup-value">{{ deviceData.jldw }}</span>
          </div>
          <div v-if="deviceData.ckname" class="popup-item">
            仓库：<span class="popup-value">{{ deviceData.ckname }}</span>
          </div>
        </div>
        <div class="popup-img">
          <div class="popup-img-item">
            <img src="@/assets/images/bengzhan2.png" alt="" />
          </div>
          <div v-if="deviceData.address.includes('泵站')" class="popup-img-item">
            <img src="@/assets/images/bengzhan.png" alt="" />
          </div>
        </div>
      </div>

      <div v-else-if="singleMarkerData" class="popup-content">
        <div class="popup-list">
          <div class="popup-item">
            名称：<span v-if="singleMarkerData.tableData?.name" class="popup-value">{{
              singleMarkerData.tableData?.name || '未知'
            }}</span>
          </div>
          <div class="popup-item">
            类型：<span v-if="singleMarkerData.tableData?.type" class="popup-value">{{
              singleMarkerData.tableData?.type || '未知'
            }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.accumulated_area" class="popup-item">
            积水面积：<span class="popup-value">{{ singleMarkerData.tableData?.accumulated_area }} ㎡</span>
          </div>
          <div v-if="singleMarkerData.tableData?.max_water_depth" class="popup-item">
            最大水深：<span class="popup-value">{{ singleMarkerData.tableData?.max_water_depth }} m</span>
          </div>
          <div v-if="singleMarkerData.tableData?.alert_level" class="popup-item">
            预警等级：<span class="popup-value">{{ singleMarkerData.tableData?.alert_level }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.alert_time" class="popup-item">
            预警时间：<span class="popup-value">{{ singleMarkerData.tableData?.alert_time }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.eventGridName" class="popup-item">
            事件位置：<span class="popup-value">{{ singleMarkerData.tableData?.eventGridName }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.eventName" class="popup-item">
            上报人：<span class="popup-value">{{ singleMarkerData.tableData?.eventName }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.eventRepDepartName" class="popup-item">
            事件处置部门<span class="popup-value">{{ singleMarkerData.tableData?.eventRepDepartName }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.eventStatusName" class="popup-item">
            审核状态<span class="popup-value">{{ singleMarkerData.tableData?.eventStatusName }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.eventTypeName" class="popup-item">
            事件描述：<span class="popup-value">{{ singleMarkerData.tableData?.eventTypeName }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.rptTime" class="popup-item">
            事件发生事件：<span class="popup-value">{{ singleMarkerData.tableData?.rptTime }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.station" class="popup-item">
            站名：<span class="popup-value">{{ singleMarkerData.tableData?.station }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.time_period" class="popup-item">
            时段：<span class="popup-value">{{ singleMarkerData.tableData?.time_period }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.water_change" class="popup-item">
            积水面积：<span class="popup-value">{{ singleMarkerData.tableData?.water_change }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.water_storage_time" class="popup-item">
            积水深度：<span class="popup-value">{{ singleMarkerData.tableData?.water_storage_time }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.area_expansion" class="popup-item">
            积水面积：<span class="popup-value">{{ singleMarkerData.tableData?.area_expansion }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.device_name" class="popup-item">
            设备名称：<span class="popup-value">{{ singleMarkerData.tableData?.device_name }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.status" class="popup-item">
            泵站状况：<span class="popup-value">{{ singleMarkerData.tableData?.status }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.pumping_volume" class="popup-item">
            抽水量：<span class="popup-value">{{ singleMarkerData.tableData?.pumping_volume }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.discharge_volume" class="popup-item">
            抽排量：<span class="popup-value">{{ singleMarkerData.tableData?.discharge_volume }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.shutdown_status" class="popup-item">
            启停状况：<span class="popup-value">{{ singleMarkerData.tableData?.shutdown_status }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.location" class="popup-item">
            地址：<span class="popup-value">{{ singleMarkerData.tableData?.location }}</span>
          </div>
          <div v-if="singleMarkerData.tableData.device_name.includes('泵站')" class="popup-item">
            排水分区： <span class="popup-value">{{ singleMarkerData.tableData?.device_name }}</span>
          </div>
        </div>
        <div class="popup-img">
          <div class="popup-img-item">
            <img src="@/assets/images/bengzhan2.png" alt="" />
          </div>
          <div v-if="singleMarkerData.tableData.device_name.includes('泵站')" class="popup-img-item">
            <img src="@/assets/images/bengzhan.png" alt="" />
          </div>
        </div>
      </div>

      <slot v-else></slot>
    </div>
    <template #footer>
      <div v-if="showFooter" class="dialog-footer">
        <el-button @click="handleCancel">{{ cancelText }}</el-button>
        <el-button type="primary" @click="handleConfirm">{{ confirmText }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
// 获取assets静态资源
const getAssetsFile = url => {
  return new URL(`../../assets/images/${url}`, import.meta.url).href
}

const props = defineProps({
  // 控制对话框是否显示
  visible: {
    type: Boolean,
    default: false
  },
  // 对话框标题
  title: {
    type: String,
    default: ''
  },
  // 对话框宽度
  width: {
    type: String,
    default: '20%' // 从 '30%' 改为 '50%'
  },
  // 是否显示关闭按钮
  showClose: {
    type: Boolean,
    default: true
  },
  // 是否可以点击模态框关闭
  closeOnClickModal: {
    type: Boolean,
    default: false
  },
  // 是否在关闭时销毁内容
  destroyOnClose: {
    type: Boolean,
    default: false
  },
  // 是否显示底部按钮
  showFooter: {
    type: Boolean,
    default: false
  },
  // 取消按钮文本
  cancelText: {
    type: String,
    default: '取消'
  },
  // 确认按钮文本
  confirmText: {
    type: String,
    default: '确定'
  },
  // 鼠标点击位置的X坐标
  mouseX: {
    type: Number,
    default: 0
  },
  // 鼠标点击位置的Y坐标
  mouseY: {
    type: Number,
    default: 0
  },
  // 设备数据
  deviceData: {
    type: Object,
    default: null
  },
  // 单个标记数据
  singleMarkerData: {
    type: Object,
    default: null
  }
})

// 事件
const emit = defineEmits(['update:visible', 'cancel', 'confirm', 'close'])

// 计算属性：对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: val => emit('update:visible', val)
})

// 对话框样式，根据鼠标位置设置
const dialogStyle = computed(() => {
  return {
    position: 'absolute',
    left: '50%',
    top: '50%',
    transform: 'translate(-50%, -50%)',
    margin: '0',
    minHeight: '500px', // 添加最小高度
    background: ' rgba(6, 72, 146, 0.2353) ',
    height: '800px !important'
  }
})

// 关闭前的回调
const handleClose = () => {
  emit('close')
  emit('update:visible', false)
}

// 取消按钮点击事件
const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}

// 确认按钮点击事件
const handleConfirm = () => {
  emit('confirm')
  emit('update:visible', false)
}
</script>

<style lang="scss" scoped>
:deep(.el-icon svg) {
  width: 200px !important;
  height: 200px !important;
}
:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: #ffffff !important;
}
.custom-dialog {
  position: absolute !important;
  z-index: 9999;
}
.dialog-content {
  padding: 15px 0; // 增加内边距
  font-size: 24px; // 增加内容字体大小
}
.popup-img {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 400px;
  .popup-img-item {
    width: 50%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    .popup-img-item-title {
      font-size: 24px;
      color: #ffffff;
    }
    img {
      width: 100%;
      height: 100%;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px; // 增加底部边距
}

/* 确保关闭按钮可点击 */
:deep(.el-dialog__header) {
  pointer-events: auto;
}

:deep(.el-dialog__headerbtn) {
  pointer-events: auto;
  z-index: 10;
  transform: scale(2); // 放大关闭按钮
}

:deep(.el-dialog__close) {
  pointer-events: auto;
  font-size: 80px;
  color: #ffffff;
  background: #fff; // 放大关闭图标
}

:deep(.el-dialog) {
  margin: 0 !important;
  background-color: rgba(0, 32, 96, 0.8) !important;
  border: 1px solid #00a0e9;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 160, 233, 0.5);
  pointer-events: auto;
  min-height: 300px; // 添加最小高度
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #00a0e9;
  padding: 15px; // 增加头部内边距
}

:deep(.el-dialog__title) {
  color: #ffffff;
  font-size: 24px; // 增加标题字体大小
}

:deep(.el-dialog__body) {
  color: #ffffff;
  padding: 20px; // 增加内容区域内边距
  font-size: 24px; // 增加内容字体大小
}

:deep(.el-button) {
  font-size: 60px; // 增加按钮字体大小
  padding: 12px 20px; // 增加按钮内边距
}

:deep(.el-dialog__close) {
  color: #ffffff;
}

/* 弹窗内容样式 */
.popup-content {
  padding: 5px;
  font-size: 28px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 30px;
  .popup-item {
    margin-bottom: 8px;
    color: #ffffff;

    .popup-value {
      color: #ffffff;
      font-weight: bold;
    }
  }
}
</style>
