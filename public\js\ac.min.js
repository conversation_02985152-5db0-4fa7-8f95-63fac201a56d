var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(v){var z=0;return function(){return z<v.length?{done:!1,value:v[z++]}:{done:!0}}};$jscomp.arrayIterator=function(v){return{next:$jscomp.arrayIteratorImpl(v)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(v,z,y){if(v==Array.prototype||v==Object.prototype)return v;v[z]=y.value;return v};$jscomp.getGlobal=function(v){v=["object"==typeof globalThis&&globalThis,v,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var z=0;z<v.length;++z){var y=v[z];if(y&&y.Math==Math)return y}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(v,z){var y=$jscomp.propertyToPolyfillSymbol[z];if(null==y)return v[z];y=v[y];return void 0!==y?y:v[z]};
$jscomp.polyfill=function(v,z,y,E){z&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(v,z,y,E):$jscomp.polyfillUnisolated(v,z,y,E))};$jscomp.polyfillUnisolated=function(v,z,y,E){y=$jscomp.global;v=v.split(".");for(E=0;E<v.length-1;E++){var I=v[E];if(!(I in y))return;y=y[I]}v=v[v.length-1];E=y[v];z=z(E);z!=E&&null!=z&&$jscomp.defineProperty(y,v,{configurable:!0,writable:!0,value:z})};
$jscomp.polyfillIsolated=function(v,z,y,E){var I=v.split(".");v=1===I.length;E=I[0];E=!v&&E in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var x=0;x<I.length-1;x++){var D=I[x];if(!(D in E))return;E=E[D]}I=I[I.length-1];y=$jscomp.IS_SYMBOL_NATIVE&&"es6"===y?E[I]:null;z=z(y);null!=z&&(v?$jscomp.defineProperty($jscomp.polyfills,I,{configurable:!0,writable:!0,value:z}):z!==y&&(void 0===$jscomp.propertyToPolyfillSymbol[I]&&(y=1E9*Math.random()>>>0,$jscomp.propertyToPolyfillSymbol[I]=$jscomp.IS_SYMBOL_NATIVE?
$jscomp.global.Symbol(I):$jscomp.POLYFILL_PREFIX+y+"$"+I),$jscomp.defineProperty(E,$jscomp.propertyToPolyfillSymbol[I],{configurable:!0,writable:!0,value:z})))};$jscomp.initSymbol=function(){};$jscomp.iteratorPrototype=function(v){v={next:v};v[Symbol.iterator]=function(){return this};return v};$jscomp.underscoreProtoCanBeSet=function(){var v={a:!0},z={};try{return z.__proto__=v,z.a}catch(y){}return!1};
$jscomp.setPrototypeOf=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf:$jscomp.underscoreProtoCanBeSet()?function(v,z){v.__proto__=z;if(v.__proto__!==z)throw new TypeError(v+" is not extensible");return v}:null;$jscomp.makeIterator=function(v){var z="undefined"!=typeof Symbol&&Symbol.iterator&&v[Symbol.iterator];return z?z.call(v):$jscomp.arrayIterator(v)};$jscomp.generator={};
$jscomp.generator.ensureIteratorResultIsObject_=function(v){if(!(v instanceof Object))throw new TypeError("Iterator result "+v+" is not an object");};$jscomp.generator.Context=function(){this.isRunning_=!1;this.yieldAllIterator_=null;this.yieldResult=void 0;this.nextAddress=1;this.finallyAddress_=this.catchAddress_=0;this.finallyContexts_=this.abruptCompletion_=null};
$jscomp.generator.Context.prototype.start_=function(){if(this.isRunning_)throw new TypeError("Generator is already running");this.isRunning_=!0};$jscomp.generator.Context.prototype.stop_=function(){this.isRunning_=!1};$jscomp.generator.Context.prototype.jumpToErrorHandler_=function(){this.nextAddress=this.catchAddress_||this.finallyAddress_};$jscomp.generator.Context.prototype.next_=function(v){this.yieldResult=v};
$jscomp.generator.Context.prototype.throw_=function(v){this.abruptCompletion_={exception:v,isException:!0};this.jumpToErrorHandler_()};$jscomp.generator.Context.prototype.return=function(v){this.abruptCompletion_={return:v};this.nextAddress=this.finallyAddress_};$jscomp.generator.Context.prototype.jumpThroughFinallyBlocks=function(v){this.abruptCompletion_={jumpTo:v};this.nextAddress=this.finallyAddress_};$jscomp.generator.Context.prototype.yield=function(v,z){this.nextAddress=z;return{value:v}};
$jscomp.generator.Context.prototype.yieldAll=function(v,z){v=$jscomp.makeIterator(v);var y=v.next();$jscomp.generator.ensureIteratorResultIsObject_(y);if(y.done)this.yieldResult=y.value,this.nextAddress=z;else return this.yieldAllIterator_=v,this.yield(y.value,z)};$jscomp.generator.Context.prototype.jumpTo=function(v){this.nextAddress=v};$jscomp.generator.Context.prototype.jumpToEnd=function(){this.nextAddress=0};
$jscomp.generator.Context.prototype.setCatchFinallyBlocks=function(v,z){this.catchAddress_=v;void 0!=z&&(this.finallyAddress_=z)};$jscomp.generator.Context.prototype.setFinallyBlock=function(v){this.catchAddress_=0;this.finallyAddress_=v||0};$jscomp.generator.Context.prototype.leaveTryBlock=function(v,z){this.nextAddress=v;this.catchAddress_=z||0};
$jscomp.generator.Context.prototype.enterCatchBlock=function(v){this.catchAddress_=v||0;v=this.abruptCompletion_.exception;this.abruptCompletion_=null;return v};$jscomp.generator.Context.prototype.enterFinallyBlock=function(v,z,y){y?this.finallyContexts_[y]=this.abruptCompletion_:this.finallyContexts_=[this.abruptCompletion_];this.catchAddress_=v||0;this.finallyAddress_=z||0};
$jscomp.generator.Context.prototype.leaveFinallyBlock=function(v,z){z=this.finallyContexts_.splice(z||0)[0];if(z=this.abruptCompletion_=this.abruptCompletion_||z){if(z.isException)return this.jumpToErrorHandler_();void 0!=z.jumpTo&&this.finallyAddress_<z.jumpTo?(this.nextAddress=z.jumpTo,this.abruptCompletion_=null):this.nextAddress=this.finallyAddress_}else this.nextAddress=v};$jscomp.generator.Context.prototype.forIn=function(v){return new $jscomp.generator.Context.PropertyIterator(v)};
$jscomp.generator.Context.PropertyIterator=function(v){this.object_=v;this.properties_=[];for(var z in v)this.properties_.push(z);this.properties_.reverse()};$jscomp.generator.Context.PropertyIterator.prototype.getNext=function(){for(;0<this.properties_.length;){var v=this.properties_.pop();if(v in this.object_)return v}return null};$jscomp.generator.Engine_=function(v){this.context_=new $jscomp.generator.Context;this.program_=v};
$jscomp.generator.Engine_.prototype.next_=function(v){this.context_.start_();if(this.context_.yieldAllIterator_)return this.yieldAllStep_(this.context_.yieldAllIterator_.next,v,this.context_.next_);this.context_.next_(v);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.return_=function(v){this.context_.start_();var z=this.context_.yieldAllIterator_;if(z)return this.yieldAllStep_("return"in z?z["return"]:function(y){return{value:y,done:!0}},v,this.context_.return);this.context_.return(v);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.throw_=function(v){this.context_.start_();if(this.context_.yieldAllIterator_)return this.yieldAllStep_(this.context_.yieldAllIterator_["throw"],v,this.context_.next_);this.context_.throw_(v);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.yieldAllStep_=function(v,z,y){try{var E=v.call(this.context_.yieldAllIterator_,z);$jscomp.generator.ensureIteratorResultIsObject_(E);if(!E.done)return this.context_.stop_(),E;var I=E.value}catch(x){return this.context_.yieldAllIterator_=null,this.context_.throw_(x),this.nextStep_()}this.context_.yieldAllIterator_=null;y.call(this.context_,I);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.nextStep_=function(){for(;this.context_.nextAddress;)try{var v=this.program_(this.context_);if(v)return this.context_.stop_(),{value:v.value,done:!1}}catch(z){this.context_.yieldResult=void 0,this.context_.throw_(z)}this.context_.stop_();if(this.context_.abruptCompletion_){v=this.context_.abruptCompletion_;this.context_.abruptCompletion_=null;if(v.isException)throw v.exception;return{value:v.return,done:!0}}return{value:void 0,done:!0}};
$jscomp.generator.Generator_=function(v){this.next=function(z){return v.next_(z)};this.throw=function(z){return v.throw_(z)};this.return=function(z){return v.return_(z)};this[Symbol.iterator]=function(){return this}};$jscomp.generator.createGenerator=function(v,z){z=new $jscomp.generator.Generator_(new $jscomp.generator.Engine_(z));$jscomp.setPrototypeOf&&v.prototype&&$jscomp.setPrototypeOf(z,v.prototype);return z};
$jscomp.asyncExecutePromiseGenerator=function(v){function z(E){return v.next(E)}function y(E){return v.throw(E)}return new Promise(function(E,I){function x(D){D.done?E(D.value):Promise.resolve(D.value).then(z,y).then(x,I)}x(v.next())})};$jscomp.asyncExecutePromiseGeneratorFunction=function(v){return $jscomp.asyncExecutePromiseGenerator(v())};$jscomp.asyncExecutePromiseGeneratorProgram=function(v){return $jscomp.asyncExecutePromiseGenerator(new $jscomp.generator.Generator_(new $jscomp.generator.Engine_(v)))};
$jscomp.polyfill("Array.prototype.includes",function(v){return v?v:function(z,y){var E=this;E instanceof String&&(E=String(E));var I=E.length;y=y||0;for(0>y&&(y=Math.max(y+I,0));y<I;y++){var x=E[y];if(x===z||Object.is(x,z))return!0}return!1}},"es7","es3");
$jscomp.checkStringArgs=function(v,z,y){if(null==v)throw new TypeError("The 'this' value for String.prototype."+y+" must not be null or undefined");if(z instanceof RegExp)throw new TypeError("First argument to String.prototype."+y+" must not be a regular expression");return v+""};$jscomp.stringPadding=function(v,z){v=void 0!==v?String(v):" ";return 0<z&&v?v.repeat(Math.ceil(z/v.length)).substring(0,z):""};
$jscomp.polyfill("String.prototype.padStart",function(v){return v?v:function(z,y){var E=$jscomp.checkStringArgs(this,null,"padStart");return $jscomp.stringPadding(y,z-E.length)+E}},"es8","es3");$jscomp.iteratorFromArray=function(v,z){v instanceof String&&(v+="");var y=0,E=!1,I={next:function(){if(!E&&y<v.length){var x=y++;return{value:z(x,v[x]),done:!1}}E=!0;return{done:!0,value:void 0}}};I[Symbol.iterator]=function(){return I};return I};
$jscomp.polyfill("Array.prototype.values",function(v){return v?v:function(){return $jscomp.iteratorFromArray(this,function(z,y){return y})}},"es8","es3");
var acapi=function(v){function z(a){function b(c,d){return c.reduce(function(e,f){var l;Array.isArray(f)&&(l=b(f,d+1));return l>e?l:e},d)}return b(a,1)}function y(a){if(void 0!=a){var b=[];if(/^(rgb|RGB)/.test(a)){a=a.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",");for(var c of a)b.push(Number((Number(c)/255).toFixed(6)))}else/^#/.test(a)?a.includes("#")?(a=a.replace("#",""),3==a.length&&(a=a[0]+""+a[0]+a[1]+a[1]+a[2]+a[2]),6!=a.length?(console.error("\u975e\u5341\u516d\u8fdb\u5236\u989c\u8272\u683c\u5f0f"),
b=void 0):(b=parseInt(a.substring(0,2),16),c=parseInt(a.substring(2,4),16),a=parseInt(a.substring(4,6),16),b=[b/255,c/255,a/255])):(console.error("\u975e\u5341\u516d\u8fdb\u5236\u989c\u8272\u683c\u5f0f"),b=void 0):a instanceof Array&&(b=a);3==b.length&&b.push(1);return b}}function E(a){if(void 0!=a){var b,c=z(a);2==c?b=[[a]]:3==c?b=[a]:4==c&&(b=a);if(b)for(let d of b)for(let e of d)for(let f of e)2==f.length&&f.push(0);return b}}function I(a){if(void 0!=a)return 1==z(a)?[a]:a}function x(a){return a instanceof
Array?a:[a]}function D(a){if("undefined"!=typeof a&&null!=a){var b=[];if(a instanceof Array)for(let c in a)b[c]=a[c].toString();else b=[a.toString()];return b}}function ja(a){return!isNaN(parseFloat(a))&&isFinite(a)}function K(a){switch(typeof a){case "undefined":return!0;case "string":if(0===a.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case "boolean":if(!a)return!0;break;case "number":if(isNaN(a))return!0;break;case "object":if(null===a||0===a.length)return!0;for(var b in a)return!1;
return!0}return!1}function Z(a,b){for(a=a.toString();a.length<b;)a="0"+a;return a}function da(){let a=new Date;return Z(a.getHours(),2)+":"+Z(a.getMinutes(),2)+":"+Z(a.getSeconds(),2)+"."+Z(a.getMilliseconds(),3)}function pa(a,b){let c,d=0;return function(...e){const f=this,l=+new Date,g=b-(l-d);0>=g?(d=l,a.apply(f,e),c&&(clearTimeout(c),c=null)):c||(c=setTimeout(()=>{d=+new Date;c=null;a.apply(f,e)},g))}}function qa(a){switch(a){case Q.StartingProcess:return"starting process...";case Q.CheckingBusy:return"checking...";
case Q.ProcessStartFailed:return"process start failed!";case Q.ProcessStarted:return"process started";case Q.LoadingProject:return"loading project...";case Q.ProjectLoaded:return"project loaded";case Q.UserAccessDenied:return"user access denied";case Q.CheckingLicense:return"checking license..."}}function ka(a){"string"===typeof a&&(a=document.getElementById(a));return a}function ra(a){return L.isIOSDevice&&L.isInWeixinBrowser?!1:a==W.abnormal||a==W.instance_disconnected}function N(a,b){return void 0!==
a&&null!==a?a:b}function O(){if(!(this instanceof O))return new O;this.reg=Array(8);this.chunk=[];this.size=0;this.reset()}function sa(a){var b=new O;a=b.sum(a);return b.toArray(a,function(c){return("0"+(c&255).toString(16)).slice(-2)}).join("")}function ea(a){return/^F([1-9]|1[0-2])$/.test(a.key)}function la(a){return a.keyCode===T.Shift&&"ShiftRight"===a.code?T.RightShift:a.keyCode===T.Control&&"ControlRight"===a.code?T.RightControl:a.keyCode===T.Alt&&"AltRight"===a.code?T.RightAlt:a.keyCode}var h=
{None:"None",Camera_PlayAnimation:"PlayCamAnim",Camera_StopAnimation:"StopPlayCamAnim",Misc_EnterReportMode:"EnterReportMode",Misc_ExitReportMode:"ExitReportMode",Misc_SetDateTime:"SetWheatherCondition",Camera_GetCamera:"GetCameraInfo",Camera_SetState:"SetCameraState",Camera_Set:"SetCamera",Camera_LookAtBBox:"SetCameraLookAtBBox",Coord_World2Screen:"TransformPositionW2S",Coord_Screen2World:"TransformPositionS2W",InfoTree_SetVisibility:"SetProjectTree",InfoTree_Show:"ShowProjectTreeLayers",InfoTree_Hide:"HideProjectTreeLayers",
InfoTree_EnableXRay:"EnableXRay",InfoTree_DisableXRay:"DisableXRay",TileLayer_Add:"AddTileLayer",HeatMap_Add:"AddHeatMap",HeatMap_Update:"UpdateHeatMap",HeatMap_AddPoints:"AddPointsToHeatMap",HeatMap_RemovePoints:"RemovePointsFromHeatMap",HeatMap_Delete:"DeleteHeatMap",HeatMap_Show:"ShowHeatMap",HeatMap_Hide:"HideHeatMap",HeatMap_Focus:"FocusHeatMap",HighlightArea_Add:"CreateHighlightArea",HighlightArea_Delete:"RemoveHighlightArea",HighlightArea_Update:"ModifyHighlightArea",Polygon3D_Add:"Create3dpolygon",
Polygon3D_Delete:"Delete3dpolygon",Misc_AddImageButton:"AddImageButtons",Misc_DeleteImageButton:"DeleteImageButtons",Misc_AddAnimatedImageButton:"AddAnimatedImageButtons",Settings_SetMainUIVisibility:"SetMainUIVisibility",Settings_SetMousePickMask:"SetQueryToolState",Tag_Add:"CreateTags",Tag_Delete:"ClearTags",Tag_Update:"UpdateTags",Tag_Clear:"ClearAllTags",Tag_Focus:"FocusTags",Beam_Add:"CreateBeams",Beam_Update:"UpdateBeams",Beam_Delete:"ClearBeams",Beam_Clear:"ClearAllBeams",Beam_Focus:"FocusBeams",
Beam_Show:"ShowBeams",Beam_Hide:"HideBeams",Beam_ShowAll:"ShowAllBeams",Beam_HideAll:"HideAllBeams",RadiationPoint_Add:"CreateRadiationPoints",RadiationPoint_Update:"UpdateRadiationPoints",RadiationPoint_Delete:"ClearRadiationPoints",RadiationPoint_Clear:"ClearAllRadiationPoints",Polygon_Add:"CreatePolygons",Polygon_Update:"UpdatePolygons",Polygon_Delete:"ClearPolygons",Polygon_Clear:"ClearAllPolygons",Polygon_Highlight:"HighlightPolygons",Polygon_StopHighlight:"StopHighLightPolygons",Polygon_Glow:"GlowPolygons",
Polygon_Focus:"FocusPolygons",Tag_FocusAll:"FocusAllTags",Tag_Show:"ShowTags",Tag_Hide:"HideTags",Tag_ShowAll:"ShowAllTags",Tag_HideAll:"HideAllTags",RadiationPoint_Focus:"FocusRadiationPoints",RadiationPoint_FocusAll:"FocusAllRadiationPoints",RadiationPoint_Show:"ShowRadiationPoints",RadiationPoint_Hide:"HideRadiationPoints",RadiationPoint_ShowAll:"ShowAllRadiationPoints",RadiationPoint_HideAll:"HideAllRadiationPoints",Polygon_Show:"ShowPolygons",Polygon_Hide:"HidePolygons",Polygon3D_Update:"Update3DPolygons",
Polygon3D_Clear:"ClearAll3DPolygon",Polygon3D_Focus:"Focus3DPolygons",Polygon3D_Show:"Show3DPolygons",Polygon3D_Hide:"Hide3DPolygons",Polygon3D_Highlight:"Highlight3DPolygons",Polygon3D_Glow:"Glow3DPolygons",Polygon3D_StopGlow:"StopGlow3DPolygons",HighlightArea_Clear:"ClearAllHighlightAreas",HighlightArea_Focus:"FocusHighlightAreas",HighlightArea_Show:"ShowHighlightAreas",HighlightArea_Hide:"HideHighlightAreas",Misc_GetVersion:"GetVersionNumber",TileLayer_Update:"UpdateTileLayer",TileLayer_Delete:"DeleteTileLayer",
TileLayer_Focus:"FocusTileLayer",TileLayer_EnableXRay:"EnableTileLayerXRay",TileLayer_DisableXRay:"DisableTileLayerXRay",TileLayer_Show:"ShowTileLayer",TileLayer_Hide:"HideTileLayer",Tag_Get:"GetTagInfo",RadiationPoint_Get:"GetRadiationPointsInfo",Polygon_Get:"GetPolygonsInfo",Polygon3D_Get:"Get3DPolygonsInfo",HighlightArea_Get:"GetHighlightAreaInfo",TileLayer_Get:"GetTileLayerInfo",Beam_Get:"GetBeamsInfo",HeatMap_Get:"GetHeatMapInfo",Settings_GetMapMode:"GetMapMode",Settings_SetMapMode:"SetMapMode",
Settings_SetMapURL:"SetMapUrl",Settings_SetWMTSLayerVisible:"SetServerVisible",TileLayer_Actor_Show:"ShowTileLayerActor",TileLayer_Actor_Hide:"HideTileLayerActor",TileLayer_Actor_Focus:"FocusTileLayerActor",TileLayer_Actor_Highlight:"HighLightTileLayerActor",TileLayer_Actor_StopHightlight:"StopHightLightTileLayerActor",CustomObject_Add:"AddCustomObj",CustomObject_Update:"UpdateCustomObj",CustomObject_Delete:"DeleteCustomObj",CustomObject_Get:"GetCustomObj",CustomObject_Focus:"FocusCustomObj",CustomObject_Show:"ShowCustomObj",
CustomObject_Hide:"HideCustomObj",CustomObject_SetTintColor:"SetCustomObjectTintColor",HeatMap_Clear:"DeleteAllHeatMap",CustomTag_Clear:"ClearAllTags",CustomTag_Delete:"ClearTags",CustomTag_Focus:"FocusTags",CustomTag_FocusAll:"FocusAllTags",CustomTag_Show:"ShowTags",CustomTag_Hide:"HideTags",CustomTag_ShowAll:"ShowAllTags",CustomTag_HideAll:"HideAllTags",CustomTag_Add:"CreateCustomTags",CustomTag_Update:"UpdateCustomTags",CustomTag_Get:"GetCustomTagInfo",Polyline_Add:"CreatePolylines",Polyline_Update:"UpdatePolylines",
Polyline_Delete:"ClearPolyline",Polyline_Clear:"ClearAllPolylines",Polyline_Focus:"FocusPolylines",Polyline_Get:"GetPolylineInfo",Polyline_Show:"ShowPolyline",Polyline_Hide:"HidePolyline",Polyline_ShowAll:"ShowAllPolylines",Polyline_HideAll:"HideAllPolylines",VideoProjection_Add:"CreateVideoProjecter",VideoProjection_Update:"UpdateVideoProjecter",VideoProjection_Delete:"DeleteVideoProjecter",VideoProjection_Show:"ShowVideoProjecter",VideoProjection_Hide:"HideVideoProjecter",VideoProjection_Get:"GetVideoProjecter",
VideoProjection_Focus:"FocusVideoProjecter",Misc_PlayVideo:"AddMediaWindow",Misc_StopPlayVideo:"DeleteMediaWindow",Weather_SetParams:"SetWeatherParameters",Weather_GetParams:"GetWeatherParameters",Weather_SetDate:"SetDateParameters",Weather_GetDate:"GetDateParameters",ODLine_Add:"CreateODLines",ODLine_Update:"UpdateODLines",ODLine_Delete:"ClearODLine",ODLine_Clear:"ClearAllODLines",ODLine_Focus:"FocusODLines",ODLine_Get:"GetODLineInfo",ODLine_Show:"ShowODLine",ODLine_Hide:"HideODLine",ODLine_ShowAll:"ShowAllODLines",
ODLine_HideAll:"HideAllODLines",Misc_SetWindowResolution:"SetWindowResolution",Misc_CallBPFunction:"CallBPActorFunction",Settings_SetHighlightColor:"SetTileLayerActorColor",InfoTree_ShowByGroupId:"ShowByGroupID",InfoTree_HideByGroupId:"HideByGroupID",InfoTree_HighlightByGroupId:"HighlightByGroupID",InfoTree_DeleteByGroupId:"ClearByGroupID",EditHelper_Start:"EnterCustomBuilder",EditHelper_Quit:"ExitCustomBuilder",EditHelper_Finish:"FinishCustomBuilder",EditHelper_SetParam:"SetCustomBuilderStyle",Settings_SetFovX:"SetFovH",
TileLayer_Actor_ShowAll:"ShowAllTileLayerActors",TileLayer_Actor_HideAll:"HideAllTileLayerActors",CameraTour_Add:"CreateSimpleCamAnim",CameraTour_Update:"UpdateSimpleCamAnim",CameraTour_Play:"PlaySimpleCamAnim",CameraTour_Stop:"StopPlaySimpleCamAnim",CameraTour_Delete:"DeleteSimpleCamAnim",Tag_PopupWindow_Show:"ShowTagsPopUpWindow",Tag_PopupWindow_Hide:"HideTagsPopUpWindow",Tag_PopupWindow_ShowAll:"ShowAllTagsPopUpWindow",Tag_PopupWindow_HideAll:"HideAllTagsPopUpWindow",Settings_SetOceanColor:"SetOceanColor",
Misc_ShowAllFoliages:"ShowAllFoliages",Misc_HideAllFoliages:"HideAllFoliages",Tools_StartPolygonClip:"StartAreaClip",Tools_StopClip:"StopAreaClip",TileLayer_SetAllowClip:"SetTileLayerClip",Panorama_Add:"CreatePanoramicpoint",Panorama_Update:"UpdatePanoramicpoint",Panorama_Delete:"ClearPanoramicpoint",Panorama_Clear:"ClearAllPanoramicpoint",Panorama_Focus:"FocusPanoramicpoint",Panorama_Get:"GetPanoramicpoint",Decal_Add:"CreateDecal",Decal_Update:"UpdateDecal",Decal_Delete:"ClearDecal",Decal_Clear:"ClearAllDecal",
Decal_Focus:"FocusDecal",Decal_FocusAll:"FocusAllDecal",Decal_Get:"GetDecal",Misc_PlayMovie:"PlayMovie",Misc_StopMovie:"StopMovie",Tools_SetMeasurement:"SetMeasure",Tools_StartMeasurement:"EnterMeasure",Tools_StopMeasurement:"ExitMeasure",Viewshed_Add:"CreateViewShedPlane",Viewshed_Update:"UpdateViewShedPlane",Viewshed_Focus:"FocusViewShedPlane",Viewshed_Delete:"DeleteViewShedPlane",Viewshed_Clear:"ClearViewShedPlane",Viewshed_Get:"GetViewShedPlane",TileLayer_SetStyle:"SetTileLayerStyle",Tools_LineIntersect:"LineIntersectPoint",
Coord_PCS2GCS:"ProjectionToGeographic",Coord_GCS2PCS:"GeographicToProjection",DynamicWater_Add:"CreateDynamicWater",DynamicWater_Update:"UpdateDynamicWater",DynamicWater_Focus:"FocusDynamicWater",DynamicWater_Delete:"DeleteDynamicWater",DynamicWater_Clear:"ClearDynamicWater",DynamicWater_Get:"GetDynamicWater",DynamicWater_Show:"ShowDynamicWater",DynamicWater_Hide:"HideDynamicWater",Quit:"QuitAirCity",TileLayer_Actor_Unhighlight:"UnHighLightTileLayerActor",InfoTree_Get:"GetProjectTreeData",Reset:"Reset",
SaveProject:"SaveProjectDatas",CustomObject_Clear:"ClearCustomObj",CustomObject_Highlight:"HighlightCustomObj",CustomObject_Unhighlight:"UnHighlightCustomObj",CustomObject_StopHighlight:"StopHighlightCustomObj",CustomObject_CallFunction:"CallCustomObjectFunction",CustomObject_AddByTileLayer:"AddCustomObjectByTileLayer",Tools_StartPlaneClip:"CreatePlaneClip",Tools_StopPlaneClip:"DeletePlaneClip",Tools_StartVolumeClip:"CreateVolumeClip",Tools_UpdateVolumeClip:"UpdateVolumeClip",Tools_StopVolumeClip:"DeleteVolumeClip",
Tools_StartSkylineAnalysis:"CreateSkyline",Tools_StopSkylineAnalysis:"DeleteSkyline",Tools_ExportSkyline:"ExportSkyline",Tools_StartViewshedAnalysis:"CreateViewShed",Tools_StopViewshedAnalysis:"DeleteViewShed",Camera_Move:"CameraMove",TileLayer_SetCollision:"SetTileLayerCollision",TileLayer_Modifier_Add:"AddTileLayerModifier",TileLayer_Modifier_Update:"UpdateTileLayerModifier",TileLayer_Modifier_Delete:"DeleteTileLayerModifier",TileLayer_Modifier_Clear:"ClearTileLayerModifier",Misc_StartProcess:"StartProcess",
Settings_SetCampassVisible:"CampassVisible",Tools_StartGeometryEdit:"EditGeometry",Tools_StopGeometryEdit:"ExitGeometryEdit",Settings_SetEnableInteract:"SetEnableInteract",Camera_PauseAnimation:"PauseCamAnim",Camera_ResumeAnimation:"RestartCamAnim",Settings_SetInteractiveMode:"SetPlayerRoammode",CameraTour_Pause:"PauseSimpleCamAnim",CameraTour_Resume:"RestartSimpleCamAnim",Settings_SetTerrainAlpha:"SetTileLayerGlobalAlpha",CustomMesh_Add:"CreateSimpleMesh",CustomMesh_Delete:"DeleteSimpleMesh",CustomMesh_Clear:"ClearAllSimpleMesh",
CustomMesh_Update:"UpdateSimpleMesh",CustomMesh_Get:"GetSimpleMesh",CustomMesh_Focus:"FocusSimpleMesh",CustomMesh_Show:"ShowSimpleMesh",CustomMesh_Hide:"HideSimpleMesh",CustomMesh_ShowAll:"ShowAllSimpleMesh",CustomMesh_HideAll:"HideAllSimpleMesh",Settings_EnableCameraMovingEvent:"EnableCameraMovingEvent",Marker_Add:"CreatePOIs",Marker_Update:"UpdatePOIs",Marker_Get:"GetPOIs",Marker_Delete:"ClearPOIs",Marker_Clear:"ClearAllPOIs",Marker_Focus:"FocusPOIs",Marker_FocusAll:"FocusAllPOIs",Marker_Show:"ShowPOIs",
Marker_Hide:"HidePOIs",Marker_ShowAll:"ShowAllPOIs",Marker_HideAll:"HideAllPOIs",Marker_ShowPopupWindow:"ShowPOIsPopUpWindow",Marker_HidePopupWindow:"HidePOIsPopUpWindow",Marker_ShowAllPopupWindow:"ShowAllPOIsPopUpWindow",Marker_HideAllPopupWindow:"HideAllPOIsPopUpWindow",TileLayer_SetViewportVisible:"SetTileLayerViewPort",TileLayer_GetObjectIDs:"GetTileLayerObjectIDs",Polygon3D_AddHighlight:"AddHighlight3DPolygon",Polygon3D_StopHighlight:"StopHightLight3DPolygon",Tools_StartFloodFill:"CreateFloodFill",
Tools_StopFloodFill:"DeleteFloodFill",FloodFill_Add:"CreateFloodFillObject",FloodFill_Delete:"DeleteFloodFillObject",FloodFill_Clear:"ClearFloodFillObject",FloodFill_Update:"UpdateFloodFillObject",FloodFill_Get:"GetFloodFillObject",FloodFill_Focus:"FocusFloodFillObject",FloodFill_Show:"ShowFloodFillObject",FloodFill_Hide:"HideFloodFillObject",FloodFill_ShowAll:"ShowAllFloodFillObject",FloodFill_HideAll:"HideAllFloodFillObject",Cesium3DTile_Add:"CreateCesium3DTile",Cesium3DTile_Delete:"DeleteCesium3DTile",
Cesium3DTile_Clear:"ClearCesium3DTile",Cesium3DTile_Update:"UpdateCesium3DTile",Cesium3DTile_Get:"GetCesium3DTile",Cesium3DTile_Focus:"FocusCesium3DTile",Cesium3DTile_Show:"ShowCesium3DTile",Cesium3DTile_Hide:"HideCesium3DTile",Cesium3DTile_ShowAll:"ShowAllCesium3DTile",Cesium3DTile_HideAll:"HideAllCesium3DTile",TileLayer_GetActorInfoFromDB:"GetTileLayerDetailedInformation",ShapeFileLayer_Add:"CreateShapeFile",ShapeFileLayer_Delete:"DeleteShapeFile",ShapeFileLayer_Clear:"ClearShapeFile",ShapeFileLayer_Update:"UpdateShapeFile",
ShapeFileLayer_Get:"GetShapeFile",ShapeFileLayer_Focus:"FocusShapeFile",ShapeFileLayer_Show:"ShowShapeFile",ShapeFileLayer_Hide:"HideShapeFile",ShapeFileLayer_ShowAll:"ShowAllShapeFile",ShapeFileLayer_HideAll:"HideAllShapeFile",ShapeFileLayer_OpenShapeFileLayer:"OpenShapeFile",Weather_SimulateTime:"SetSimulateTime",TileLayer_Actor_GetInfo:"GetTileLayerActorInfo",Camera_GetAnimationList:"GetAnimationList",TileLayer_CutPolygon_Add:"AddTileLayerCutPolygon",TileLayer_CutPolygon_Update:"UpdateTileLayerCutPolygon",
TileLayer_CutPolygon_Delete:"DeleteTileLayerCutPolygon",TileLayer_CutPolygon_Clear:"ClearTileLayerCutPolygon",Marker3D_Add:"Create3DMarker",Marker3D_Delete:"Delete3DMarker",Marker3D_Clear:"Clear3DMarker",Marker3D_Update:"Update3DMarker",Marker3D_Get:"Get3DMarker",Marker3D_Focus:"Focus3DMarker",Marker3D_Show:"Show3DMarker",Marker3D_Hide:"Hide3DMarker",Marker3D_ShowAll:"ShowAll3DMarker",Marker3D_HideAll:"HideAll3DMarker",Camera_GetAnimationImage:"GetAnimationImage",Tools_ReplaceTexture:"ReplaceTexture",
Tools_RestoreTexture:"RestoreTexture",Light_Add:"CreateLight",Light_Delete:"DeleteLight",Light_Update:"UpdateLight",Light_Get:"GetLight",Light_Clear:"ClearLight",Light_Focus:"FocusLight",Light_Show:"ShowLight",Light_Hide:"HideLight",Light_ShowAll:"ShowAllLight",Light_HideAll:"HideAllLight",WaterMesh_Add:"CreateWaterMesh",WaterMesh_Update:"UpdateWaterMesh",WaterMesh_Delete:"DeleteWaterMesh",WaterMesh_Clear:"ClearWaterMesh",WaterMesh_Get:"GetWaterMesh",WaterMesh_Focus:"FocusWaterMesh",WaterMesh_Show:"ShowWaterMesh",
WaterMesh_Hide:"HideWaterMesh",WaterMesh_ShowAll:"ShowAllWaterMesh",WaterMesh_HideAll:"HideAllWaterMesh",CustomObject_StartMove:"MoveCustomObj",CustomObject_StopMove:"StopMoveCustomObj",CustomObject_OverrideMaterial:"OverrideCustomObjMaterials",CustomObject_RestoreMaterial:"RestoreOverrideCustomObjMaterials",Settings_SetReport:"SetReportSettings",Settings_GetReport:"GetReportSettings",Settings_SetControl:"SetControllSettings",Settings_GetControl:"GetControllSettings",Settings_SetPostProcess:"SetPostProcessSettings",
Settings_GetPostProcess:"GetPostProcessSettings",Settings_SetCamera:"SetCameraSettings",Settings_GetCamera:"GetCameraSettings",CustomObject_SetViewportVisible:"SetCustomObjViewPort",WaterFlowField_Add:"AddFlowVectorField",WaterFlowField_Update:"UpdateFlowVectorField",WaterFlowField_Delete:"DeleteFlowVectorField",WaterFlowField_Clear:"ClearFlowVectorField",WaterFlowField_Get:"GetFlowVectorField",WaterFlowField_Focus:"FocusFlowVectorField",WaterFlowField_Show:"ShowFlowVectorField",WaterFlowField_Hide:"HideFlowVectorField",
TileLayer_SetViewHeightRange:"SetTileLayerVisibleHeight",Tools_StartVisiblityAnalysis:"StartVisiblityAnalysis",Tools_StopVisiblityAnalysis:"StopVisiblityAnalysis",Tools_StartViewDomeAnalysis:"StartViewDomeAnalysis",Tools_StopViewDomeAnalysis:"StopViewDomeAnalysis",Tools_StartCutFillAnalysis:"StartCutFillAnalysis",Tools_StopCutFillAnalysis:"StopCutFillAnalysis",Tools_StartSunshineAnalysis:"StartSunshineAnalysis",Tools_StopSunshineAnalysis:"StopSunshineAnalysis",Tools_StartTerrainSlopeAnalysis:"StartTerrainSlopeAnalysis",
Tools_StopTerrainSlopeAnalysis:"StopTerrainSlopeAnalysis",Tools_StartContourLineAnalysis:"StartTerrainHeightAnalysis",Tools_StopContourLineAnalysis:"StopTerrainHeightAnalysis",TileLayer_GetAllFlattenData:"GetAllTileLayerFlattenData",Marker_SetupPOIAttachment:"SetupPOIAttachment",HeatMap3D_Add:"AddHeatMap3d",HeatMap3D_Update:"UpdateHeatMap3d",HeatMap3D_Focus:"FocusHeatMap3d",HeatMap3D_Delete:"DeleteHeatMap3d",HeatMap3D_Show:"ShowHeatMap3d",HeatMap3D_Hide:"HideHeatMap3d",HeatMap3D_Get:"GetHeatMap3d",
HeatMap3D_Clear:"ClearHeatMap3d",ShapeFileLayer_HighlightFeature:"HighlightShapeObject",ShapeFileLayer_UnHighlightFeature:"UnHighlightShapeObject",ShapeFileLayer_FocusFeature:"FocusShapeObject",ShapeFileLayer_GetFeatureInfo:"GetShapeObjectFieldsInfo",Misc_EnterMultiViewport:"EnterMultiViewport",Misc_ExitMultiViewport:"ExitMultiViewport",Misc_SetActivateMultiViewport:"ActivateMultiViewport",Misc_GetActivateMultiViewport:"GetActivateMultiViewport",Panorama_Enter:"EnterPanorama",Panorama_Exit:"ExitPanorama",
Panorama_Switch:"SwitchPanorama",VTPKService_Get:"GetVTPKService",VTPKService_Set:"SetVTPKService",SimulateTest__:"SimulateTest__",Settings_CursorAutoSync:"SetUseSoftwareCursor",InfoTree_Focus:"FoucsByGroupID",Marker_ShowByGroupId:"ShowMarkerByGroupID",Marker_HideByGroupId:"HideMarkerByGroupID",Marker_DeleteByGroupId:"DeleteMarkerGroupID",ImageryLayer_Init:"InitServerOrService",ImageryLayer_Add:"AddServerOrService",Camera_LockBBox:"EnterMoveRangeCheck",Camera_UnLockBBox:"ExitMoveRangeCheck",Settings_SetWMTSLayerOpacity:"SetServerAlpha",
Camera_EnterEarthCapture:"EnterEarthCapture",Camera_ExitEarthCapture:"ExitEarthCapture",Tools_AnalysisPopupAttributes:"AnalysisPopupAttributes",Tools_AnalysisCloseAttributes:"AnalysisCloseAttributes",ImageryLayer_Show:"ShowServerOrService",ImageryLayer_Hide:"HideServerOrService",ImageryLayer_Delete:"DeleteServerOrService",Settings_GetInteractiveMode:"GetInteractiveMode",VideoProjection_Clear:"ClearVideoProjecter",Misc_UpdateMultiViewport:"UpdateMultiViewport",Misc_downloadPakFiles:"OnlineDownloadAssets",
Settings_SetCampassPosition:"SetCompassPosition",Polygon3D_ShowAll:"ShowAll3DPolygons",Polygon3D_HideAll:"HideAll3DPolygons",GeoJSONLayer_Add:"CreateFeatureLayer",GeoJSONLayer_Show:"ShowFeatureLayer",GeoJSONLayer_Hide:"HideFeatureLayer",GeoJSONLayer_Delete:"RemoveFeatureLayer",Vehicle_Add:"CreateVehicle",Vehicle_Update:"UpdateVehicle",Vehicle_AddWayPoints:"SetVehicleWayPoints",Vehicle_ClearWayPoints:"ClearVehicleWayPoints",Vehicle_Focus:"FocusVehicle",Vehicle_Show:"ShowVehicle",Vehicle_Hide:"HideVehicle",
Vehicle_Get:"GetVehicle",Vehicle_Delete:"DeleteVehicle",Vehicle_Clear:"ClearVehicle",Vehicle_MoveTo:"AddVehicleWayPoint",Misc_ConvexHull2D:"ConvexHull2D",HeatMap_AddVoxels:"AddHeatmap3DVoxels",TileLayer_GetCollision:"GetTileLayerCollision",TileLayer_SetPointSize:"SetTileLayerPointSize",Misc_QueryActorOrMaterial:"GetObjectParameters",CustomObject_StartGlow:"GlowCustomObject",CustomObject_StopGlow:"StopGlowCustomObject",Coord_Transform:"TransformCoordinate",RegisterJsCommunication:"RegisterJSTick",
UnRegisterJsCommunication:"UnRegisterJSTick",Vehicle_CallBatchFunction:"CallVehicleFunction",Camera_FlyAround:"FlyAround",Marker3D_ShowByGroupId:"Show3DMarkerGroupID",Marker3D_HideByGroupId:"Hide3DMarkerGroupID",Marker3D_DeleteByGroupId:"Delete3DMarkerGroupID",Camera_EnterWorldAnimation:"EnterWorldAnimation",Camera_ExitWorldAnimation:"ExitWorldAnimation",TileLayer_SetDecalAttach:"SetTileLayerReceiveDecal",Settings_SetGameBoardVisible:"SetGameBoardVisible",Antenna_Add:"AddRadiationPattern",Antenna_Update:"UpdateRadiationPattern",
Antenna_Delete:"DeleteRadiationPattern",Antenna_Clear:"ClearRadiationPattern",Antenna_Get:"GetRadiationPattern",Antenna_Focus:"FocusRadiationPattern",Antenna_Show:"ShowRadiationPattern",Antenna_Hide:"HideRadiationPattern",Tools_FunctionNavBar:"EnterFunctionNavigationBar",VectorField_Add:"AddVectorField",VectorField_Delete:"DeleteVectorField",VectorField_Clear:"ClearVectorField",WebUIJSON_Get:"GetWebUISaveJsonString",WebUIJSON_Set:"SetWebUISaveJsonString",VectorField_Update:"UpdateVectorField",WebUI_OpenACP:"OpenAcp",
HydrodynamicModel2_Add:"AddHydroModel",HydrodynamicModel2_Update:"UpdateHydroModel",HydrodynamicModel2_Delete:"DeleteHydroModel",HydrodynamicModel2_Clear:"ClearHydroModel",HydrodynamicModel2_Get:"GetHydroModel",HydrodynamicModel2_Focus:"FocusHydroModel",HydrodynamicModel2_Show:"ShowHydroModel",HydrodynamicModel2_Hide:"HideHydroModel",FiniteElement_Add:"AddCAEModel",FiniteElement_Update:"UpdateCAEModel",FiniteElement_Delete:"DeleteCAEModel",FiniteElement_Clear:"ClearCAEModel",FiniteElement_Get:"GetCAEModel",
FiniteElement_Focus:"FocusCAEModel",FiniteElement_Show:"ShowCAEModel",FiniteElement_Hide:"HideCAEModel",WebUI_Minimize:"MinimizeWindow",WebUI_Maximize:"MaximizeWindow",WebUI_Restore:"RestoreWindow",Tools_HideFunctionNavBar:"ExitFunctionNavigationBar",Fluid_Add:"AddFluidFlux",Fluid_Update:"UpdateFluidFlux",Fluid_Reset:"ResetFluidFlux",Fluid_Delete:"DeleteFluidFlux",Fluid_Clear:"ClearFluidFlux",Fluid_Get:"GetFluidFlux",Fluid_Focus:"FocusFluidFlux",Fluid_Show:"ShowFluidFlux",Fluid_Hide:"HideFluidFlux",
Fluid_AddSource:"AddFluidFluxSource",Fluid_RemoveSource:"RemoveFluidFluxSource",TileLayer_Clear:"ClearTileLayer",HeatMap3D_Query:"HeatMap3DQueryVoxel",WebUI_EnterFullScreen:"EnterFullscreen",WebUI_ExitFullScreen:"ExitFullscreen",SignalWave_Add:"AddWaveBeam",SignalWave_Update:"UpdateWaveBeam",SignalWave_Delete:"DeleteWaveBeam",SignalWave_Clear:"ClearWaveBeam",SignalWave_Focus:"FocusWaveBeam",SignalWave_Show:"ShowWaveBeam",SignalWave_Hide:"HideWaveBeam",SignalWave_Get:"GetWaveBeam",HeatMap3D_SetViewPort:"SetHeatMap3DViewPort",
WaterFlowField_SetViewPort:"SetWaterFlowFieldViewPort",VectorField_SetViewPort:"SetVectorFieldViewPort",Settings_SetCharacterRotation:"SetCharacterRotation",River_Add:"AddHydroSimulation",River_Update:"UpdateHydroSimulation",River_Delete:"DeleteHydroSimulation",River_Clear:"ClearHydroSimulation",River_Get:"GetHydroSimulation",River_Focus:"FocusHydroSimulation",River_Show:"ShowHydroSimulation",River_Hide:"HideHydroSimulation",HydrodynamicModel_Add:"AddHydroSimulation",HydrodynamicModel_Update:"UpdateHydroSimulation",
HydrodynamicModel_Delete:"DeleteHydroSimulation",HydrodynamicModel_Clear:"ClearHydroSimulation",HydrodynamicModel_Get:"GetHydroSimulation",HydrodynamicModel_Focus:"FocusHydroSimulation",HydrodynamicModel_Show:"ShowHydroSimulation",HydrodynamicModel_Hide:"HideHydroSimulation",HydroDynamic1D_Add:"AddHydroSimulation",HydroDynamic1D_Update:"UpdateHydroSimulation",HydroDynamic1D_Delete:"DeleteHydroSimulation",HydroDynamic1D_Clear:"ClearHydroSimulation",HydroDynamic1D_Get:"GetHydroSimulation",HydroDynamic1D_Focus:"FocusHydroSimulation",
HydroDynamic1D_Show:"ShowHydroSimulation",HydroDynamic1D_Hide:"HideHydroSimulation",HydroDynamic2D_Add:"AddHydroSimulation",HydroDynamic2D_Update:"UpdateHydroSimulation",HydroDynamic2D_Delete:"DeleteHydroSimulation",HydroDynamic2D_Clear:"ClearHydroSimulation",HydroDynamic2D_Get:"GetHydroSimulation",HydroDynamic2D_Focus:"FocusHydroSimulation",HydroDynamic2D_Show:"ShowHydroSimulation",HydroDynamic2D_Hide:"HideHydroSimulation",Settings_GetWKT:"GetSettingData",SmoothMoveCustomObject:"SmoothMoveCustomObject",
VectorField_Focus:"FocusVectorField",Misc_ReloadPak:"RefrenshAssetsViewer",Misc_SetPakVisibility:"SetOnlineWebUIVisibility",TileLayer_GetDBTabID:"GetTileLayerDBTabID",GeoJSONLayer_Focus:"FocusFeatureLayer",Misc_ProjectAssetCount:"GetAcpResourceInfo",Marker_SetViewPort:"SetMarkerViewVisible",Misc_SwitchShortcutKey:"SwitchShortcutKey",Tools_GetFunctionNavBar:"GetFunctionNavigationBar",GeoJSONLayer_HighlightFeature:"HighlightFeatureLayer",GeoJSONLayer_UnHighlightFeature:"UnHighlightFeatureLayer",GeoJSONLayer_FocusFeature:"FocusFeatureLayerByFID",
GeoJSONLayer_UnHighlightFeaturesByLayerId:"UnHighlightFeatureLayerByID",ImageryLayer_Focus:"FocusImageLayer",ImageryLayer_AddCustom:"AddServerOrService2",VectorField_Show:"ShowVectorField",VectorField_Hide:"HideVectorField",VectorField_Get:"GetVectorField",MarkerLayer_Add:"AddPOILayer",MarkerLayer_Update:"UpdatePOILayer",MarkerLayer_Delete:"DeletePOILayer",MarkerLayer_Clear:"ClearPOILayer",MarkerLayer_Focus:"FocusPOILayer",MarkerLayer_Show:"ShowPOILayer",MarkerLayer_ShowAll:"ShowAllPOILayer",MarkerLayer_Hide:"HidePOILayer",
MarkerLayer_HideAll:"HidePOIAllPOILayer",MarkerLayer_Get:"GetPOILayer",MarkerLayer_FocusMarker:"FocusPOILayer2",Settings_Update:"UpdateServerOrService",GeoJSONLayer_Clear:"ClearFeatureLayer",GeoJSONLayer_Update:"UpdateFeatureLayer",Marker3D_CallFunction:"Call3DMarkerFunction",TileLayer_TileLayerReceiveWMTSDecal:"SetTileLayerReceiveWMTSDecal",CustomObject_ShowByGroupId:"ShowCustomObjectGroupID",CustomObject_HideByGroupId:"HideCustomObjectGroupID",ImageryLayer_SetOpacity:"SetServerAlpha",ImageryLayer_MoveImageLayerToAnotherBefore:"MoveImageLayerToAnotherBefore",
ImageryLayer_MoveImageLayerPrimary:"MoveImageLayerPrimary",ImageryLayer_MoveImageLayerLast:"MoveImageLayerLast",ImageryLayer_VTPK_Add:"AddVTPK",ImageryLayer_VTPK_Visible:"SetVTPKVisible",ImageryLayer_VTPK_Delete:"RemoveVTPK",InfoTree_GetProjectTreeBPFunction:"GetProjectTreeBPFunction",InfoTree_CallProjectTreeBPFunction:"CallProjectTreeBPFunction",Camera_ExportImage:"ExportImageByCamera",CustomObject_Pause:"PauseMoveCustomObj",CustomObject_Resume:"ResumeMoveCustomObj",CustomObject_SetMoveRate:"SetSpeedMoveCustomObj",
Vehicle2_Add:"CreateVehicle2",Vehicle2_Update:"UpdateVehicle2",Vehicle2_Focus:"FocusVehicle2",Vehicle2_Show:"ShowVehicle2",Vehicle2_Hide:"HideVehicle2",Vehicle2_Get:"GetVehicle2",Vehicle2_Delete:"DeleteVehicle2",Vehicle2_Clear:"ClearVehicle2",Vehicle2_MoveTo:"AddVehicle2WayPoint",Camera_CancelFollow:"CancelFollow",SplineMesh_Add:"CreateSplineMesh",SplineMesh_Update:"UpdateSplineMesh",SplineMesh_Delete:"ClearSplineMesh",SplineMesh_Clear:"ClearAllSplineMesh",SplineMesh_Focus:"FocusSplineMesh",SplineMesh_Get:"GetSplineMeshInfo",
SplineMesh_Show:"ShowSplineMesh",SplineMesh_Hide:"HideSplineMesh",SplineMesh_ShowAll:"ShowAllSplineMeshs",SplineMesh_HideAll:"HideAllSplineMeshs",Marker_SetGatherStyle:"CustomPOIGatherStyle",Marker3D_Attach:"ObjectAttachment",SettingsPanel_setPak:"SetPakAsset",TileLayer_EnableFluid:"FluidFluxCollision",Tools_AddAnimation:"CreateAnimation",Tools_EditAnimation:"EditAnimation",Tools_DeleteAnimation:"DeleteAnimation",GetProjectInfo:"GetProjectInformation",Settings_SetMainPanelPos:"SetHomeNavigationPadding",
Settings_SetToolbarVisible:"SetNavigationBarVisible",Settings_ShowPropertiesPanelById:"PopupAttributesPanelById",Settings_HidePropertiesPanel:"RemoveAttributesPanel",Settings_SetPropertiesPanelPos:"SetAttributesPanelPosition",Command_End:1E4},ma=!1;(()=>{if(!ma){ma=!0;for(let a in h)h[h[a]]=a}})();class ta{constructor(a,b){this.id=a;this.visible=b}}class ua{constructor(a,b,c,d){this.id=a.toString();this.coordinate=b;this.radius=c;this.heatValue=d}}class va{constructor(a,b,c,d,e,f,l,g,t,k,n,m,p,q){this.id=
a.toString();this.coordinate=b;this.imagePath=c;this.imageSize=d;this.url=e;this.text=f;this.range=l||[1,1E5];this.showLine=!!g;this.textColor=t||[0,0,0,1];this.textBackgroundColor=k||[1,1,1,.85];this.textBorderColor=n||[0,0,0,0];this.textRange=m||1E5;this.hoverImagePath=p;this.autoHidePopupWindow=q||!0}}class wa{constructor(a,b,c){this.url=a;this.width=b;this.height=c}}class xa{constructor(a,b,c,d,e,f,l,g){this.id=a.toString();this.coordinate=b;this.contentURL=c.url;this.contentSize=[c.width,c.height];
this.popupURL=d.url;this.popupSize=[d.width,d.height];this.pivot=e;this.range=f;this.autoHidePopupWindow=l||!0;this.popupPos=g}}class ya{constructor(a,b,c,d,e,f){this.id=a.toString();this.coordinates=E(b);this.color=y(c);this.heightRange=d;this.intensity=e;this.depthTest=f||!0}}class za{constructor(a,b,c,d,e,f,l,g){this.id=a.toString();this.x=b;this.y=c;this.width=d;this.height=e;this.normalImage=f;this.hoverImage=l;this.tooltip=g}}class Aa{constructor(a,b,c,d,e,f,l,g,t){this.id=a.toString();this.x=
b;this.y=c;this.width=d;this.height=e;this.imageSequecePath=f;this.imageSequeceLength=l;this.loop=!!g;this.interactable=!!t}}class Ba{constructor(a,b,c,d,e,f,l){this.id=a.toString();this.duration=b;this.thickness=c;this.interval=d;this.velocity=e;this.color=y(f);this.coordinates=l}}class Ca{constructor(a,b,c,d,e,f){this.id=a.toString();this.coordinate=b;this.radius=c;this.rippleNumber=d;this.color=y(e);this.brightness=f}}class Da{constructor(a,b,c,d,e,f,l,g){this.id=a.toString();this.style=b;this.coordinates=
E(c);this.color=y(d);this.height=e;this.intensity=f;this.tillingX=l;this.tillingY=g}}class Ea{constructor(a,b,c,d,e,f,l,g,t,k){this.id=a.toString();this.color=y(b);this.coordinates=c;this.style=void 0==d?0:d;if(0>this.style||5<this.style)this.style=0;this.thickness=void 0==e?20:e;this.brightness=void 0==f?.5:f;this.flowRate=void 0==l?.5:l;this.tiling=g||0;this.depthTest=t||!0;this.shape=void 0==k?0:k}}class Fa{constructor(a,b,c,d,e,f,l,g,t,k,n,m,p,q,r,u,w){this.id=a.toString();this.color=y(b);this.coordinates=
c;this.flowRate=void 0==d?.5:d;this.brightness=void 0==e?.5:e;this.bendDegree=void 0==f?.5:f;this.tiling=l||0;this.lineThickness=void 0==g?20:g;this.flowPointSizeScale=void 0==t?20:t;this.labelSizeScale=void 0==k?100:k;this.lineShape=void 0==n?1:n;this.lineStyle=m||0;this.flowShape=p||0;this.startPointShape=q||0;this.endPointShape=r||0;this.startLabelShape=u||0;this.endLabelShape=w||0;this.endLabelStyle=this.startLabelStyle=this.endPointStyle=this.startPointStyle=this.flowStyle=0}}class Ga{constructor(a,
b,c,d,e,f,l,g){this.id=a.toString();this.color=y(b);this.coordinates=E(c);this.framecolor=y(d);this.framethickness=e;this.depthTest=f||!0;this.brightness=l;this.style=g;if(0>this.style||10<this.style)this.style=0}}class Ha{constructor(a,b,c,d,e,f){this.id=a.toString();this.imagePath=b;this.coordinate=c;this.yaw=d;this.groupId=e||"";this.userData=f||""}}class Ia{constructor(a,b,c,d,e,f,l,g){this.id=a.toString();this.order=b;this.texturePath=c;this.location=d;this.rotation=e;this.scale=f;this.groupId=
l;this.userData=g}}class Ja{constructor(a,b,c,d,e){this.id=a.toString();this.fileName=b;this.location=c;this.rotation=d;this.scale=e}}class Ka{constructor(a,b,c,d,e,f,l){this.id=a;this.pakFilePath=b;this.assetPath=c;this.location=d;this.rotation=e;this.scale=f;this.smoothMotion=l}}class La{constructor(a,b,c,d,e,f,l){this.id=a;this.tileLayerId=b;this.objectId=c;this.location=d;this.rotation=e;this.scale=f;this.smoothMotion=l}}class Ma{constructor(a,b,c,d,e,f,l){this.id=a;this.videoURL=b;this.location=
c;this.rotation=d;this.fov=e;this.aspectRatio=f;this.distance=l}}class Na{constructor(a,b,c,d,e){this.id=a;this.coordinates=E(b);this.style=c;this.groupId=d;this.userData=e}}class Oa{constructor(a,b,c,d,e){this.actorTag=a;this.objectName=b;this.functionName=c;this.paramType=d;this.paramValue=e}}class U{constructor(a,b){this.tileLayerId=a;this.tileLayerActorNames=D(b)}}class Pa{constructor(a,b,c,d){this.index=a;this.time=b;this.location=c;this.rotation=d}}class Qa{constructor(a,b,c){this.id=a.toString();
this.name=b.toString();this.keyFrames=c}}class Ra{constructor(){this.queue=[];this.dataMap=new Map}enqueue(a){if(null==a||void 0==a||!a.callbackIndex)return!1;this.queue.push(a.callbackIndex);this.dataMap.set(a.callbackIndex,a);return!0}dequeue(){if(0!=this.queue.length){const a=this.queue.shift();return this.dataMap.get(a)}}removeData(a){this.dataMap.has(a)&&this.dataMap.delete(a)}dataSize(){return this.dataMap.size}queueSize(){return this.queue.length}}class Sa extends Ra{constructor(a){super();
this.onSendAPI=a;this.timer=null;this.isMainThreadBusy=!1}onMainThreadBusy(a){this.isMainThreadBusy=a.busy}push(a){if(a.__noResponse)this.onSendAPI(a);else 0==this.dataSize()?(this.enqueue(a),this.callNext()):1E4<this.dataSize()?console.warn("The api queue has exceeded 10000, and subsequent calls will be ignored!"):this.enqueue(a)}callNext(a){a&&this.removeData(a);this.timer&&clearTimeout(this.timer);0!=this.dataSize()&&(a=this.dequeue())&&(this._callNextForTimeout(3E3),this.onSendAPI(a))}_callNextForTimeout(a){this.timer=
setTimeout(()=>{this.isMainThreadBusy?this._callNextForTimeout(5E3):this.callNext()},a)}}class G{constructor(a,b,c){this.int=a;this.type=b;this.colorProps=c;this._useBatchUpdate=!1;this._tempUpdateData=[]}_checkCommand(a){a=this.type+a;let b=h[a];void 0==b&&this.int.logWithColor("red",`Invalid command: ${a}`);return b}_convertFilePath(a){return this.int.resourcesPath?a.replace("@path:",this.int.resourcesPath+"/"):a}_processProps(a){a=x(a);for(var b of a)for(let d in b)if("string"==typeof b[d]&&b[d].startsWith("@path:")&&
(b[d]=this._convertFilePath(b[d])),"imagesArray"==d)for(let e=0;e<b[d].length;e++)b[d][e]=this._convertFilePath(b[d][e]);for(let d of a)"string"!=typeof d.id&&(d.id=d.id.toString());if(this.colorProps){b=this.colorProps.split("|");for(var c of a)for(let d of b)c.hasOwnProperty(d)&&(c[d]=y(c[d]))}if(-1!=["Polygon","Polygon3D","HighlightArea","DynamicWater"].indexOf(this.type))for(let d of a)d.coordinates=E(d.coordinates);for(let d of a)d.hasOwnProperty("userData")&&(c=d.userData,this._isJsonString(c)&&
(d.userData=c.replace(/"/g,"~!@~!@~!@")))}_isJsonString(a){try{return"string"===typeof a?(JSON.parse(a),!0):!1}catch(b){return!1}}_add(a,b){this._processProps(a);return this.int.call({command:this._checkCommand("_Add"),data:x(a)},b)}_update(a,b){this._processProps(a);return this.int.call({command:this._checkCommand("_Update"),data:x(a)},b)}_delete(a,b){return this.int.call({command:this._checkCommand("_Delete"),ids:D(a)},b)}_clear(a){return this.int.call0(this._checkCommand("_Clear"),a)}_get(a,b){return this.int.call({command:this._checkCommand("_Get"),
ids:D(a)},b)}_focus(a,b,c,d,e){"function"==typeof c&&(e=c,c=void 0);"function"==typeof d&&(e=d,d=null);return this.int.call({command:this._checkCommand("_Focus"),ids:D(a),distance:b||0,flyTime:c,rotation:d},e)}_focusAll(a,b,c,d){"function"==typeof b&&(d=b,b=void 0);"function"==typeof c&&(d=c,c=null);return this.int.call({command:this._checkCommand("_FocusAll"),ids:[],distance:a||0,flyTime:b,rotation:c},d)}_show(a,b){return this.int.call({command:this._checkCommand("_Show"),ids:D(a)},b)}_showAll(a){return this.int.call0(this._checkCommand("_ShowAll"),
a)}_hide(a,b){return this.int.call({command:this._checkCommand("_Hide"),ids:D(a)},b)}_hideAll(a){return this.int.call0(this._checkCommand("_HideAll"),a)}_updateOneProp(a,b,c,d){if(this._useBatchUpdate){d=!1;for(var e of this._tempUpdateData)if(e.id==a.toString()){d=!0;e[b]=c;break}d||(d={},d.id=a.toString(),d[b]=c,this._tempUpdateData.push(d))}else return e={},e.id=a.toString(),e[b]=c,this.update(e,d)}updateBegin(){this._useBatchUpdate=!0;this._tempUpdateData=[]}updateEnd(a){a=this.update(this._tempUpdateData,
a);this._tempUpdateData=null;this._useBatchUpdate=!1;return a}test(){alert("test")}}class Ta extends G{constructor(a){super(a,"Beam","color")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}showAll(a){return super._showAll(a)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setDuration(a,
b,c){return super._updateOneProp(a,"duration",b,c)}setThickness(a,b,c){return super._updateOneProp(a,"thickness",b,c)}setInterval(a,b,c){return super._updateOneProp(a,"interval",b,c)}setVelocity(a,b,c){return super._updateOneProp(a,"velocity",b,c)}setColor(a,b,c){return super._updateOneProp(a,"color",y(b),c)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",b,c)}}class Ua extends G{constructor(a){super(a,"CameraTour")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,
b)}delete(a,b){return super._delete(a,b)}play(a,b){return this.int.call({command:h.CameraTour_Play,ids:D(a)},b)}setMouseClickToPause(a,b,c){return this.int.call({command:h.CameraTour_Play,ids:D(a),mouseInput:b},c)}setTime(a,b,c){return this.int.call({command:h.CameraTour_Play,ids:D(a),playerTime:b},c)}pause(a){return this.int.call0(h.CameraTour_Pause,a)}resume(a){return this.int.call0(h.CameraTour_Resume,a)}stop(a){return this.int.call({command:h.CameraTour_Stop},a)}setUserData(a,b,c){return super._updateOneProp(a,
"userData",b,c)}setDuration(a,b,c){return super._updateOneProp(a,"duration",b,c)}setKeyFrames(a,b,c){return super._updateOneProp(a,"keyFrames",b,c)}setName(a,b,c){return super._updateOneProp(a,"name",b,c)}}class Va extends G{constructor(a){super(a,"Cesium3DTile")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,
b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setTileURL(a,b,c){return super._updateOneProp(a,"tileURL",b,c)}}class Wa extends G{constructor(a){super(a,"CustomMesh","color")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,
b)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",b,c)}setIndices(a,b,c){return super._updateOneProp(a,"indices",b,c)}setColor(a,b,c){return super._updateOneProp(a,"color",b,c)}}const fa={OK:0,InvalidParameters:1,InternalError:2,ResourceNotFound:3,AcpProjWKTNotSet:4,CoordinateConversionFailed:5,IDExists:6,InvalidRequestType:7,InvalidRequestString:8,NoCommand:9,DataTypeNotSupport:10,InvalidOperation:11,ProjectNotOpened:12,CodeMax:65535},aa={MouseClick:1,MouseMove:2,MouseHover:4},
R={V1:1,V2:2,V3:4,V4:8,All:255},na={ClearObjects:1,ResetSettings:2,ResetCamera:4};class Xa extends G{constructor(a){super(a,"CustomObject")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e,f){"function"==typeof c&&(f=c,c=void 0);"function"==typeof d&&(f=d,d=null);"function"==typeof e&&(f=e,e=0);1<e&&(2==e?d=[-45,0,0]:3==e?d=[0,0,0]:4==e?d=[-90,0,0]:5==e?d=[90,0,0]:6==e?d=[0,90,0]:7==e?d=[0,270,
0]:8==e?e=2:d=[0,0,0]);!K(a)&&-1==b&&K(c)&&K(d)&&K(e)&&(e=1,b=0);return this.int.call({command:h.CustomObject_Focus,ids:D(a),distance:b||0,flyTime:c,rotation:d||null,follow:e||0},f)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}addByTileLayer(a,b){if(a instanceof Array)for(let c of a)this._fixTileLayerProperty(c);else this._fixTileLayerProperty(a);return this.int.call({command:h.CustomObject_AddByTileLayer,data:x(a)},b)}_fixTileLayerProperty(a){a.tileLayerId instanceof
Array||(a.tileLayerId=x(a.tileLayerId));a.objectId instanceof Array||(a.objectId=x(a.objectId))}highlight(a,b){return this.int.call({command:h.CustomObject_Highlight,ids:D(a)},b)}unhighlight(a,b){return"undefined"==typeof a||"function"==typeof a?("function"==typeof a&&(b=a),this.int.call({command:h.CustomObject_StopHighlight},b)):this.int.call({command:h.CustomObject_Unhighlight,ids:D(a)},b)}glow(a,b){a=x(a);if(a instanceof Array)for(let c of a){let d;d=y(c.color);c.colors=[{color:[d[0]/2,d[1]/2,
d[2]/2],value:0},{color:c.color,value:1}]}else o.colors=[{color:o.color,value:0},{color:o.color,value:1}];return this.int.call({command:h.CustomObject_StartGlow,data:x(a)},b)}stopGlow(a,b){return this.int.call({command:h.CustomObject_StopGlow,ids:D(a)},b)}getBPFunction(a,b){return this.int.call({command:h.Misc_QueryActorOrMaterial,idOrPaths:x(a)},b)}callBPFunction(a,b,c,d,e,f){this.callFunction(a,b,c,d,e,f)}callFunction(a,b,c,d,e,f){return this.int.call({command:h.CustomObject_CallFunction,data:x({id:a.toString(),
functionName:b,paramType:c,paramValue:d,parameters:e})},f)}callBPFunction(a,b){this.callBatchBPFunction(a,b)}callBatchBPFunction(a,b){this.callFunction4CustomObjectArr(a,b)}callBatchFunction(a,b){this.callFunction4CustomObjectArr(a,b)}callFunction4CustomObjectArr(a,b){return this.int.call({command:h.CustomObject_CallFunction,data:x(a)},b)}setLocation(a,b,c,d){return"function"==typeof c?super._updateOneProp(a,"location",b,c):"number"==typeof c?this.int.call({command:h.CustomObject_Update,data:[{id:a,
location:b,smoothTime:c}]},d):super._updateOneProp(a,"location",b,d)}setSmoothTime(a,b,c){return super._updateOneProp(a,"smoothTime",b,c)}moveTo(a,b){return this.int.call({command:h.CustomObject_Update,data:x(a)},b)}setRotation(a,b,c){return super._updateOneProp(a,"rotation",b,c)}setLocalRotation(a,b,c){return super._updateOneProp(a,"localRotation",b,c)}setScale(a,b,c){return super._updateOneProp(a,"scale",b,c)}setSmoothMotion(a,b,c){return super._updateOneProp(a,"smoothMotion",b,c)}setTintColor(a,
b,c){a={ids:x(a),color:y(b)};return this.int.call({command:h.CustomObject_SetTintColor,data:x(a)},c)}overrideMaterial(a,b){return this.int.call({command:h.CustomObject_OverrideMaterial,data:x(a)},b)}restoreMaterial(a,b){return this.int.call({command:h.CustomObject_RestoreMaterial,ids:x(a)},b)}setViewportVisible(a,b,c){let d=[];d.push({viewportIndex:1,viewportVisible:!!(b&R.V1)});d.push({viewportIndex:2,viewportVisible:!!(b&R.V2)});d.push({viewportIndex:3,viewportVisible:!!(b&R.V3)});d.push({viewportIndex:4,
viewportVisible:!!(b&R.V4)});return this.int.call({command:h.CustomObject_SetViewportVisible,id:a,data:d},c)}startMove(a,b,c,d){return this.int.call({command:h.CustomObject_StartMove,id:a,coordinateType:b,data:x(c)},d)}pause(a,b){return this.int.call({command:h.CustomObject_Pause,ids:x(a)},b)}resume(a,b){return this.int.call({command:h.CustomObject_Resume,ids:x(a)},b)}setMoveRate(a,b){return this.int.call({command:h.CustomObject_SetMoveRate,data:x(a)},b)}stop(a,b){return this.int.call({command:h.CustomObject_StopMove,
ids:x(a)},b)}showByGroupId(a,b){return this.int.call({command:h.CustomObject_ShowByGroupId,groupIds:D(a)},b)}hideByGroupId(a,b){return this.int.call({command:h.CustomObject_HideByGroupId,groupIds:D(a)},b)}}class Ya extends G{constructor(a){super(a,"CustomTag")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}focusAll(a,b,c,d){return super._focusAll(a,b,c,d)}show(a,
b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setAutoHidePopupWindow(a,b,c){return super._updateOneProp(a,"autoHidePopupWindow",b,c)}}class Za extends G{constructor(a){super(a,"Decal")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}focusAll(a,
b,c,d){return super._focusAll(a,b,c,d)}get(a,b){return super._get(a,b)}}class $a extends G{constructor(a){super(a,"DynamicWater")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}focusAll(a,b,c,d){return super._focusAll(a,b,c,d)}get(a,b){return super._get(a,b)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",E(b),c)}setMaterialPath(a,b,c){return super._updateOneProp(a,
"materialPath",b,c)}setStyle(a,b,c){return super._updateOneProp(a,"style",b,c)}}class ab extends G{constructor(a){super(a,"FiniteElement","")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}}class bb extends G{constructor(a){super(a,"FloodFill","color")}add(a,b){return super._add(a,
b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setRange(a,b,c,d){b instanceof Array&&c instanceof Array?0<b.length&&0<c.length?this.update({id:a,min:b,max:c},d):console.error("\u6570\u7ec4\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a."):
console.error("\u6570\u7ec4\u53c2\u6570\u683c\u5f0f\u4e0d\u6b63\u786e.")}setMin(a,b,c){return super._updateOneProp(a,"min",b,c)}setMax(a,b,c){return super._updateOneProp(a,"max",b,c)}setSeed(a,b,c){return super._updateOneProp(a,"seed",b,c)}setElevation(a,b,c){return super._updateOneProp(a,"elevation",b,c)}setColor(a,b,c){return super._updateOneProp(a,"color",y(b),c)}setPrecision(a,b,c){return super._updateOneProp(a,"precision",b,c)}}class cb extends G{constructor(a){super(a,"Fluid","color")}add(a,
b){this._fixMarkerData(a,"add");return super._add(a,b)}_fixMarkerProperty(a,b){b=[[[120,250,350,3],[.6,1,1,3E-5],[.117708,.294268,.470833,0],[1,1.2,1.15,.65],0],[[70,180,350,5],[.05,.25,.3,1.5E-4],[.15,.3,.45,0],[1,1.2,1.15,.65],0],[[140,180,300,2.5],[.14,.33,.36,2E-4],[.077778,.203,.333333,0],[.9,1,1.1,.6],0],[[120,180,300,2.3],[.21,.27,.32,2.5E-4],[.195513,.244391,.317709,0],[.8,1,1.1,.7],0],[[120,250,350,2.5],[.5,.9,1,4E-5],[.09,.18,.2,0],[.9,1,1,.75],0],[[120,250,350,1.5],[.5,.9,1,4E-5],[.09,
.18,.2,0],[.9,1,1,.75],0],[[80,280,400,4.5],[.15,.35,.3,7E-5],[.092187,.184375,.184375,0],[1.5,1.2,.8,.5],0],[[70,180,330,1.8],[.1,.3,.4,3E-4],[.1,.4,.4,4E-4],[.8,1,1,.9],.5],[[215,200,150,1.6],[.8,1,1.1,1.1E-4],[.358,.357187,.3043,1],[.75,.75,.653692,.8],0],[[220,280,250,6],[.38,.33,.2,2.5E-5],[.216667,.191617,.037043,0],[1,1,.8,.35],0],[[200,200,140,1],[.2,.2,.05,6E-4],[.2,.198243,.137327,.01],[.95,1,.85,.5],.25],[[200,20,20,2],[.3,.2,.05,5E-4],[.870833,.117,.074,0],[1.1,1,.7,.4],0],[[240,200,30,
12],[1,.75,.2,2E-4],[.5125,.461171,.145208,0],[1.1,1,.7,.6],0],[[70,170,350,8],[.1,.3,.45,1.7E-4],[.139727,.279454,.405208,1],[1,1.1,1.1,.7],.9],[[60,100,200,98],[.15,.2,.3,1.4E-5],[.25,.31,.34,0],[.95,1.12,1.2,.65],.25],[[100,160,350,6.75],[.3,.35,.35,4E-4],[.3,.35,.35,7E-4],[1,1,.9,.7],1],[[290,260,140,.8],[.95,.8,.6,4E-4],[.166667,.106061,.061033,3E-4],[1.15,1,.9,.6],0],[[250,280,220,1],[.8,.83,.83,2.9E-4],[.187552,.216667,.187552,0],[1,1,.8,.35],1],[[320,350,300,.8],[1.3,1.1,.6,3E-4],[.317708,
.317708,.166363,0],[.9,1,.6,.5],1],[[80,500,400,8],[.07,.3,.65,6E-5],[.3,.65,.65,0],[1,1,.9,.7],1],[[180,500,250,6],[.4,1,.7,4E-5],[.3736,.463542,.263357,0],[1,1.1,1,.8],1],[[460,600,270,2],[.2009,.30063,.500225,5.5E-4],[.461049,.475,.290938,0],[1,1.05,.85,.6],1],[[260,340,200,7],[.4,.53,.5,2.3E-4],[.461049,.475,.290938,0],[1,1.05,.85,.8],1],[[333,270,200,12],[.7,.8,.4,2E-4],[.461049,.475,.290938,0],[1,.9,.8,.8],1]];if(ja(a.style)&&0<=a.style&&23>=a.style){var c=a.style;a.color1=b[c][0];a.color2=
b[c][1];a.color3=b[c][2];a.color4=b[c][3];a.color5=b[c][4]}else 23<a.style&&27>=a.style||(a.style=0,a.color1=b[0][0],a.color2=b[0][1],a.color3=b[0][2],a.color4=b[0][3],a.color5=b[0][4]);if(0<a.sources.length)for(b=0;b<a.sources.length;b++){c=a.sources[b].velocity;let d=c[1];-2>=c[0]&&(a.sources[b].velocity[0]=-2);2<=d&&(a.sources[b].velocity[1]=2)}}_fixMarkerData(a,b){if(a instanceof Array)for(let c of a)this._fixMarkerProperty(c,b);else this._fixMarkerProperty(a,b)}update(a,b){return super._update(a,
b)}pause(a,b){return this.update({id:x(a),active:!1},b)}continue(a,b){return this.update({id:x(a),active:!0},b)}reset(a,b){return this.int.call({command:h.Fluid_Reset,ids:x(a)},b)}addSource(a,b){return this.int.call({command:h.Fluid_AddSource,data:x(a)},b)}removeSource(a,b){return this.int.call({command:h.Fluid_RemoveSource,data:x(a)},b)}continueSource(a,b){if(a instanceof Array)for(let c of a)c.sources.active=!0;return this.update(a,b)}stopSource(a,b){if(a instanceof Array)for(let c of a)c.sources.active=
!1;return this.update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}}class db extends G{constructor(a){super(a,"GeoJSONLayer")}add(a,b){this.load(a,b)}update(a,b){a.sourceJson&&(a.url=a.sourceJson);"object"==typeof a.url&&(a.url=JSON.stringify(a.url));Array.isArray(a.viewHeightRange)&&(a.minVisibleHeight=a.viewHeightRange[0],
a.maxVisibleHeight=a.viewHeightRange[1]);return super._update(a,b)}load(a,b){a.sourceJson&&(a.url=a.sourceJson);"object"==typeof a.url&&(a.url=JSON.stringify(a.url));Array.isArray(a.viewHeightRange)&&(a.minVisibleHeight=a.viewHeightRange[0],a.maxVisibleHeight=a.viewHeightRange[1]);return super._add(a,b)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}highlightFeature(a,
b,c){return this.int.call({command:h.GeoJSONLayer_HighlightFeature,data:x({id:a,featureIds:[b]})},c)}stopHighlightFeature(a,b,c){return this.int.call({command:h.GeoJSONLayer_UnHighlightFeature,data:x({id:a,featureIds:[b]})},c)}highlightFeatures(a,b){return this.int.call({command:h.GeoJSONLayer_HighlightFeature,data:x(a)},b)}stopHighlightFeatures(a,b){return this.int.call({command:h.GeoJSONLayer_UnHighlightFeature,data:x(a)},b)}stopAllHighlightFeaturesById(a,b){return this.int.call({command:h.GeoJSONLayer_UnHighlightFeaturesByLayerId,
ids:x(a)},b)}focusFeature(a,b,c,d,e,f){return this.int.call({command:h.GeoJSONLayer_FocusFeature,data:x({id:a,featureIds:[b]}),distance:c,flyTime:d,rotation:e},f)}setViewHeightRange(a,b,c,d){return this.int.call({command:h.GeoJSONLayer_Update,data:[{id:a,minVisibleHeight:b,maxVisibleHeight:c}]},d)}}class eb extends G{constructor(a){super(a,"HeatMap")}_checkBBox(a){if(a instanceof Array){if(6!=a.length||a[0]>a[3]||a[1]>a[4]||a[2]>a[5])return!1}else return!1;return!0}addByHeatPoints(a,b){let c=a.id,
d=a.bbox,e=a.range,f=a.data,l=a.style,g=a.textureSize,t=a.opacityMode,k=a.opacityRange,n=a.blur,m=a.colors,p=a.blendMode,q=a.light;a=a.viewHeightRange;if(!this._checkBBox(d))return this.int.logWithColor("red","Invalid bbox value"),!1;if("undefined"==typeof l||"function"==typeof l)return"function"==typeof l&&(b=l),this.int.call({command:h.HeatMap_Add,id:c.toString(),bbox:d,range:e,data:x(f),style:-1},b);if(0==l||1==l){let r=-1E9,u=1E9;Array.isArray(a)&&(r=a[0],u=a[1]);return this.int.call({command:h.HeatMap_Add,
id:c.toString(),bbox:d,range:e,data:x(f),style:l,textureSize:g,opacityMode:t,opacityRange:k,blur:n,colors:m,blendMode:p,light:q,minVisibleHeight:r,maxVisibleHeight:u},b)}"function"==typeof l&&(b=l);return this.int.call({command:h.HeatMap_Add,id:c.toString(),bbox:d,range:e,data:x(f),style:-1},b)}updateByHeatPoints(a,b){let c=a.id,d=a.bbox,e=a.range,f=a.data,l=a.style,g=a.textureSize,t=a.opacityMode,k=a.opacityRange,n=a.blur,m=a.colors,p=a.blendMode,q=a.light,r=a.updateTime;a=a.viewHeightRange;if(!this._checkBBox(d))return this.int.logWithColor("red",
"Invalid bbox value"),!1;if("undefined"==typeof l||"function"==typeof l)return"function"==typeof l&&(b=l),this.int.call({command:h.HeatMap_Update,id:c.toString(),bbox:d,range:e,data:x(f),style:-1},b);if(0==l||1==l){let u=-1E9,w=1E9;Array.isArray(a)&&(u=a[0],w=a[1]);return this.int.call({command:h.HeatMap_Update,id:c.toString(),bbox:d,range:e,data:x(f),style:l,textureSize:g,opacityMode:t,opacityRange:k,blur:n,colors:m,blendMode:p,light:q,updateTime:r,minVisibleHeight:u,maxVisibleHeight:w},b)}"function"==
typeof l&&(b=l);return this.int.call({command:h.HeatMap_Update,id:c.toString(),bbox:d,range:e,data:x(f),style:-1},b)}add(a,b,c,d,e,f,l,g,t,k,n,m,p,q,r,u){if(!this._checkBBox(b))return this.int.logWithColor("red","Invalid bbox value"),!1;if("undefined"==typeof e||"function"==typeof e)return"function"==typeof e&&(u=e),this.int.call({command:h.HeatMap_Add,id:a.toString(),bbox:b,range:c,data:x(d),style:-1},u);if(0==e||1==e){let w=-1E9,C=1E9;Array.isArray(r)&&(w=r[0],C=r[1]);return this.int.call({command:h.HeatMap_Add,
id:a.toString(),bbox:b,range:c,data:x(d),style:e,textureSize:f,opacityMode:l,opacityRange:g,blur:t,colors:k,blendMode:n,light:m,binaryFile:p,updateTime:q,minVisibleHeight:w,maxVisibleHeight:C},u)}"function"==typeof e&&(u=e);return this.int.call({command:h.HeatMap_Add,id:a.toString(),bbox:b,range:c,data:x(d),style:-1},u)}update(a,b,c,d,e,f,l,g,t,k,n,m,p,q,r,u){a={command:h.HeatMap_Update,id:a.toString()};b&&(this._checkBBox(b)||this.int.logWithColor("red","Invalid bbox value"),a.bbox=b);c&&(a.range=
c);d&&(a.data=x(d));"undefined"!=typeof e&&"function"!=typeof e&&(f&&(a.textureSize=f),l&&(a.opacityMode=l),g&&(a.opacityRange=g),t&&(a.blur=t),k&&(a.colors=k),n&&(a.blendMode=n),m&&(a.light=m),p&&(a.binaryFile=p),q&&(a.updateTime=q),r&&Array.isArray(r)?(a.minVisibleHeight=r[0],a.maxVisibleHeight=r[1]):(a.minVisibleHeight=-1E9,a.maxVisibleHeight=1E9));"function"==typeof e&&(u=e);return this.int.call(a,u)}load(a,b){return this.int.call({command:h.HeatMap_Add,id:a.id,range:a.range||null,data:a.data||
[null],style:a.style||1,opacityMode:a.opacityMode||1,opacityRange:a.opacityRange||[0,1],blur:a.blur||.85,colors:{gradient:a.colors.gradient||!0,invalidColor:a.colors.invalidColor||[0,0,0,1],colorStops:a.colors.colorStops||null},blendMode:a.blendMode||0,light:a.light||!1,tifAnimation:{minHeight:a.tifAnimation.minHeight||0,maxHeight:a.tifAnimation.maxHeight||1E3,totalSeconds:a.tifAnimation.totalSeconds||10,time:a.tifAnimation.time||0,files:a.tifAnimation.files||null}},b)}play(a,b){return this.int.call({command:h.HeatMap_Update,
id:a,tifAnimation:{active:!0}},b)}pause(a,b){return this.int.call({command:h.HeatMap_Update,id:a,tifAnimation:{active:!1}},b)}setTime(a,b,c){return this.int.call({command:h.HeatMap_Update,id:a,tifAnimation:{time:b||0,active:!0}},c)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}addPoints(a,b,c){return this.int.call({command:h.HeatMap_AddPoints,
id:a,data:x(b)},c)}removePoints(a,b,c){return this.int.call({command:h.HeatMap_RemovePoints,id:a,pointIds:x(b)},c)}setRange(a,b,c){return this.int.call({command:h.HeatMap_Update,id:a,range:b},c)}setBBox(a,b,c){return this._checkBBox(b)?this.int.call({command:h.HeatMap_Update,id:a,bbox:b},c):(this.int.logWithColor("red","Invalid bbox value"),!1)}addByTif(a,b){K(a.colors)&&(a.colors={gradient:!0,invalidColor:[0,0,0,1],colorStops:[{value:0,color:[0,0,1,1]},{value:.25,color:[0,1,0,1]},{value:.5,color:[1,
1,0,1]},{value:1,color:[1,0,0,1]}]});return this.int.call({command:h.HeatMap_Add,id:a.id,style:a.style||0,range:a.range||[0,1],opacityMode:a.opacityMode||1,opacityRange:a.opacityRange||[0,1],blur:a.blur||.85,colors:{gradient:a.colors.gradient||!0,invalidColor:a.colors.invalidColor||[0,0,0,1],colorStops:a.colors.colorStops||[{value:0,color:[0,0,1,1]},{value:.25,color:[0,1,0,1]},{value:.5,color:[1,1,0,1]},{value:1,color:[1,0,0,1]}]},blendMode:a.blendMode||0,light:a.light||!1,tifFile:{minHeight:a.tifFile.minHeight||
-1E3,maxHeight:a.tifFile.maxHeight||1E4,file:a.tifFile.file||null}},b)}highlightPixels(a,b,c){return this.int.call({command:h.HeatMap_Update,id:a,highlight:{pixels:b||[],brightness:[1,5],speed:1}},c)}unHighlightAllPixels(a,b){return this.int.call({command:h.HeatMap_Update,id:a,highlight:{pixels:[]}},b)}}class fb extends G{constructor(a){super(a,"HeatMap3D")}_checkBBox(a){if(a instanceof Array){if(6!=a.length||a[0]>a[3]||a[1]>a[4]||a[2]>a[5])return!1}else return!1;return!0}addByImages(a,b){this.add(a,
b)}addByBinaryFiles(a,b){this.add(a,b)}addByHeatPoints(a,b){this.add(a,b)}addByVoxels(a,b){this.add(a,b)}addBySparseVoxels(a,b){this.add(a,b)}addByTif(a,b){this.add(a,b)}add(a,b){return super._add(a,b)}addHeatPoints(a,b){this.addVoxels(a,b)}addVoxels(a,b){return this.int.call({command:h.HeatMap_AddVoxels,data:x(a)},b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,
b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}queryVoxel(a,b,c){return this.int.call({command:h.HeatMap3D_Query,id:a,coordinate:b},c)}setDisplayMode(a,b,c){return this.int.call({command:h.HeatMap3D_Update,id:a,displayMode:b},c)}setViewportVisible(a,b,c){let d=[];d.push({viewportIndex:1,viewportVisible:!!(b&Viewport.V1)});d.push({viewportIndex:2,viewportVisible:!!(b&Viewport.V2)});d.push({viewportIndex:3,viewportVisible:!!(b&Viewport.V3)});d.push({viewportIndex:4,viewportVisible:!!(b&
Viewport.V4)});return this.int.call({command:h.HeatMap3D_SetViewPort,id:a,data:d},c)}}class gb extends G{constructor(a){super(a,"HighlightArea","color")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}setColor(a,b,c){return super._updateOneProp(a,"color",y(b),c)}setCoordinates(a,
b,c){return super._updateOneProp(a,"coordinates",b,c)}setHeightRange(a,b,c){return super._updateOneProp(a,"heightRange",b,c)}setIntensity(a,b,c){return super._updateOneProp(a,"intensity",b,c)}setDepthTest(a,b,c){return super._updateOneProp(a,"depthTest",b,c)}}class hb extends G{constructor(a){super(a,"HydrodynamicModel2")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,
b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}setTime(a,b){return this.update(a,b)}startPlay(a,b){if(a instanceof Array){for(let c of a)c.playing=!0;return this.update(a,b)}return this.update({id:a.id,displayMode:a.displayMode,time:a.time,playing:!0},b)}stopPlay(a,b){if(a instanceof Array){let c=[];for(let d of a)a={},a.id=d,a.playing=!1,c.push(a);return this.update(c,b)}return this.update({id:a,playing:!1},b)}pause(a,b){if(a instanceof
Array){let c=[];for(let d of a)a={},a.id=d,a.paused=!0,c.push(a);return this.update(c,b)}return this.update({id:a,paused:!0},b)}resume(a,b){if(a instanceof Array){let c=[];for(let d of a)a={},a.id=d,a.paused=!1,c.push(a);return this.update(c,b)}return this.update({id:a,paused:!1},b)}}class ib extends G{constructor(a){super(a,"Antenna")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,
b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}}class jb extends G{constructor(a){super(a,"HydroDynamic1D","arrowColor|waterColor")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}}class kb extends G{constructor(a){super(a,
"HydroDynamic2D","arrowColor|waterColor")}addByTif(a,b){this.add(a,b)}addBySdb(a,b){this.add(a,b)}addByBin(a,b){this.add(a,b)}addByShp(a,b){this.add(a,b)}_fixMarkerProperty(a,b){"add"==b&&(a.waterDepthAttributeName&&(a.waterDepth=a.waterDepthAttributeName),a.uvAttributeName&&(a.flowField=a.uvAttributeName),a.sdb&&(a.sdbFilePath=a.sdb),a.depthRange&&(a.valueRange=a.depthRange))}_fixMarkerData(a,b){if(a instanceof Array)for(let c of a)this._fixMarkerProperty(c,b);else this._fixMarkerProperty(a,b)}add(a,
b){this._fixMarkerData(a,"add");return super._add(a,b)}update(a,b){this._fixMarkerData(a,"add");return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}}class lb extends G{constructor(a){super(a,"HydrodynamicModel","arrowColor|waterColor")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,
b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}}class mb extends G{constructor(a){super(a,"ImageryLayer")}init(a,b){return this.int.call({command:h.ImageryLayer_Init,xmlPath:a.xmlUrl,layerName:a.layerName,tileMatrixName:a.tileMatrixName,ogcEPSG:a.ogcEPSG,cachePath:a.cachePath,mapMode:a.mapMode,renderMode:a.renderMode},b)}add(a,b){const c=
this;return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){return a instanceof Array?new Promise(d=>$jscomp.asyncExecutePromiseGeneratorFunction(function*(){let e;for(let f of a)if(e=yield c.addOne(f,b),0!=e.result){d(e);break}d(e)})):c.addOne(a,b)})}addOne(a,b){return this.int.call({command:h.ImageryLayer_Add,id:a.id,url:a.url,xmlPath:a.xmlPath,layerName:a.layerName,tileMatrixName:a.tileMatrixName,ogcEPSG:a.ogcEPSG},b)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}delete(a,
b){return super._delete(a,b)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}setDrawOrder(a,b,c){return this.int.call({command:h.ImageryLayer_MoveImageLayerToAnotherBefore,id1:a,id2:b},c)}setDrawTop(a,b){return this.int.call({command:h.ImageryLayer_MoveImageLayerPrimary,id1:a},b)}setDrawBottom(a,b){return this.int.call({command:h.ImageryLayer_MoveImageLayerLast,id1:a},b)}addVTPK(a,b){return this.int.call({command:h.ImageryLayer_VTPK_Add,id:a},b)}deleteVTPK(a,b){return this.int.call({command:h.ImageryLayer_VTPK_Delete,
id:a},b)}setVTPKVisable(a,b,c){return this.int.call({command:h.ImageryLayer_VTPK_Visible,id:a,visible:b},c)}}class nb extends G{constructor(a){super(a,"Light","color")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,
b){return super._get(a,b)}}class ob extends G{constructor(a){super(a,"Marker","fontColor|fontOutlineColor|textBackgroundColor|lineColor")}add(a,b){this._fixMarkerData(a,"add");return super._add(a,b)}_fixMarkerProperty(a,b){a.textColor&&(a.fontColor=a.textColor);a.url&&(a.popupURL=a.url);a.dispalyMode&&(a.displayMode=a.dispalyMode);K(a.displayMode)&&"add"==b&&(a.displayMode=4);a.popupURL&&!a.popupSize&&"add"==b&&(a.popupSize=[600,400]);0==a.showLine&&(a.lineSize=[0,0]);1!=a.showLine||null!=a.lineSize&&
void 0!=a.lineSize||(a.lineSize=[2,100]);4==a.displayMode&&0<a.autoDisplayModeSwitchFirstRatio&&0<a.autoDisplayModeSwitchSecondRatio&&(a.autoDisplayModeSwitchRatio=[a.autoDisplayModeSwitchFirstRatio,a.autoDisplayModeSwitchSecondRatio]);Array.isArray(a.viewHeightRange)&&(a.minVisibleHeight=a.viewHeightRange[0],a.maxVisibleHeight=a.viewHeightRange[1])}_fixMarkerData(a,b){if(a instanceof Array)for(let c of a)this._fixMarkerProperty(c,b);else this._fixMarkerProperty(a,b)}update(a,b){this._fixMarkerData(a,
"update");return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}focusAll(a,b,c,d){return super._focusAll(a,b,c,d)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}showPopupWindow(a,b){return this.int.call({command:h.Marker_ShowPopupWindow,ids:D(a)},b)}hidePopupWindow(a,b){return this.int.call({command:h.Marker_HidePopupWindow,
ids:D(a)},b)}showAllPopupWindow(a){return this.int.call0(h.Marker_ShowAllPopupWindow,a)}hideAllPopupWindow(a){return this.int.call0(h.Marker_HideAllPopupWindow,a)}get(a,b){return super._get(a,b)}setGroupId(a,b,c){return super._updateOneProp(a,"groupId",b,c)}setUserData(a,b,c){return super._updateOneProp(a,"userData",b,c)}setAnchors(a,b,c){return this.setScreenOffset(a,b,c)}setScreenOffset(a,b,c){return super._updateOneProp(a,"anchors",b,c)}setCoordinate(a,b,c){return super._updateOneProp(a,"coordinate",
b,c)}setImagePath(a,b,c){return super._updateOneProp(a,"imagePath",b,c)}setImageSize(a,b,c){return super._updateOneProp(a,"imageSize",b,c)}setHoverImagePath(a,b,c){return super._updateOneProp(a,"hoverImagePath",b,c)}setURL(a,b,c){return super._updateOneProp(a,"url",b,c)}setText(a,b,c){return super._updateOneProp(a,"text",b,c)}setTextOffset(a,b,c){return super._updateOneProp(a,"textOffset",b,c)}setFontSize(a,b,c){return super._updateOneProp(a,"fontSize",b,c)}setFontOutlineSize(a,b,c){return super._updateOneProp(a,
"fontOutlineSize",b,c)}setRange(a,b,c){return super._updateOneProp(a,"range",b,c)}setTextRange(a,b,c){return super._updateOneProp(a,"textRange",b,c)}setFontColor(a,b,c){return super._updateOneProp(a,"fontColor",b,c)}setFontOutlineColor(a,b,c){return super._updateOneProp(a,"fontOutlineColor",b,c)}setTextBackgroundColor(a,b,c){return super._updateOneProp(a,"textBackgroundColor",b,c)}setAutoHidePopupWindow(a,b,c){return super._updateOneProp(a,"autoHidePopupWindow",b,c)}setPopupURL(a,b,c){return super._updateOneProp(a,
"popupURL",b,c)}setPopupSize(a,b,c){return super._updateOneProp(a,"popupSize",b,c)}setPopupOffset(a,b,c){return super._updateOneProp(a,"popupOffset",b,c)}setLineSize(a,b,c){return super._updateOneProp(a,"lineSize",b,c)}setLineColor(a,b,c){return super._updateOneProp(a,"lineColor",b,c)}setLineOffset(a,b,c){return super._updateOneProp(a,"lineOffset",b,c)}setPriority(a,b,c){return super._updateOneProp(a,"priority",b,c)}setOcclusionCull(a,b,c){return super._updateOneProp(a,"occlusionCull",b,c)}setAttachCustomObject(a,
b){return this.int.call({command:h.Marker_SetupPOIAttachment,data:x(a)},b)}setViewportVisible(a,b,c){let d=[];d.push({viewportIndex:1,viewportVisible:!!(b&Viewport.V1)});d.push({viewportIndex:2,viewportVisible:!!(b&Viewport.V2)});d.push({viewportIndex:3,viewportVisible:!!(b&Viewport.V3)});d.push({viewportIndex:4,viewportVisible:!!(b&Viewport.V4)});return this.int.call({command:h.Marker_SetViewPort,id:a,data:d},c)}showByGroupId(a,b){return this.int.call({command:h.Marker_ShowByGroupId,ids:x(a)},b)}hideByGroupId(a,
b){return this.int.call({command:h.Marker_HideByGroupId,ids:x(a)},b)}deleteByGroupId(a,b){return this.int.call({command:h.Marker_DeleteByGroupId,ids:x(a)},b)}setClusterStyle(a,b){if(a)return this.int.call({command:h.Marker_SetGatherStyle,path:a.imagePath||"",drawSize:a.imageSize||[20,20],textSize:a.fontSize||12,textColor:y(a.fontColor)||[1,1,1,1]},b);console.error("\u6837\u5f0f\u5bf9\u8c61\u683c\u5f0f\u9519\u8bef!")}}class pb extends G{constructor(a){super(a,"Marker3D","textColor|textOutlineColor")}add(a,
b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}showByGroupId(a,b){return this.int.call({command:h.Marker3D_ShowByGroupId,groupIds:D(a)},b)}hideByGroupId(a,b){return this.int.call({command:h.Marker3D_HideByGroupId,
groupIds:D(a)},b)}deleteByGroupId(a,b){return this.int.call({command:h.Marker3D_DeleteByGroupId,groupIds:D(a)},b)}getBPFunction(a,b){return this.int.call({command:h.Misc_QueryActorOrMaterial,idOrPaths:x(a)},b)}callBPFunction(a,b){this.callBatchBPFunction(a,b)}callBatchBPFunction(a,b){return this.int.call({command:h.Marker3D_CallFunction,data:x(a)},b)}setViewHeightRange(a,b,c,d){return this.int.call({command:h.Marker3D_Update,data:[{id:a,minVisibleHeight:b,maxVisibleHeight:c}]},d)}setAttachCustomObject(a,
b){return this.int.call({command:h.Marker3D_Attach,data:x(a)},b)}}class qb extends G{constructor(a){super(a,"MarkerLayer","color|fontColor|fontOutlineColor|textBackgroundColor|lineColor")}add(a,b){this._fixMarkerProperty(a);return super._add(a,b)}_fixMarkerProperty(a){Array.isArray(a.viewHeightRange)&&(a.minVisibleHeight=a.viewHeightRange[0],a.maxVisibleHeight=a.viewHeightRange[1])}_fixMarkerData(a,b){if(a instanceof Array)for(let c of a)this._fixMarkerProperty(c,b);else this._fixMarkerProperty(a,
b)}update(a,b){this._fixMarkerProperty(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}focusByMarkerId(a,b,c,d,e,f){return this.int.call({command:h.MarkerLayer_FocusMarker,data:[{id:a,ids:[b]}],distance:c,flyTime:d,rotation:e},f)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}setViewHeightRange(a,b,c,d){return this.int.call({command:h.MarkerLayer_Update,
data:[{id:a,minVisibleHeight:b,maxVisibleHeight:c}]},d)}}class rb extends G{constructor(a){super(a,"ODLine","color")}add(a,b){this._fixODlineData(a);return super._add(a,b)}_fixODlineProperty(a){a.intensity&&(a.brightness=a.intensity)}_fixODlineData(a){if(a instanceof Array)for(let b of a)this._fixODlineProperty(b);else this._fixODlineProperty(a)}update(a,b){this._fixODlineData(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,
b,c,d,e)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",b,c)}setColor(a,b,c){return super._updateOneProp(a,"color",y(b),c)}setFlowRate(a,b,c){return super._updateOneProp(a,"flowRate",b,c)}setBrightness(a,b,c){return super._updateOneProp(a,"brightness",b,c)}setTiling(a,b,c){return super._updateOneProp(a,"tiling",
b,c)}setBendDegree(a,b,c){return super._updateOneProp(a,"bendDegree",b,c)}setLineThickness(a,b,c){return super._updateOneProp(a,"lineThickness",b,c)}setflowPointSizeScale(a,b,c){return super._updateOneProp(a,"flowPointSizeScale",b,c)}setLabelSizeScale(a,b,c){return super._updateOneProp(a,"labelSizeScale",b,c)}setLineShape(a,b,c){return super._updateOneProp(a,"lineShape",b,c)}setLineStyle(a,b,c){return super._updateOneProp(a,"lineStyle",b,c)}setFlowShape(a,b,c){return super._updateOneProp(a,"flowShape",
b,c)}setStartPointShape(a,b,c){return super._updateOneProp(a,"startPointShape",b,c)}setEndPointShape(a,b,c){return super._updateOneProp(a,"endPointShape",b,c)}setStartLabelShape(a,b,c){return super._updateOneProp(a,"startLabelShape",b,c)}setEndLabelShape(a,b,c){return super._updateOneProp(a,"endLabelShape",b,c)}}class sb extends G{constructor(a){super(a,"Panorama")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,
b,c,d,e){return super._focus(a,b,c,d,e)}get(a,b){return super._get(a,b)}enter(a,b){return this.int.call({command:h.Panorama_Enter,ids:D(a)},b)}exit(a){return this.int.call({command:h.Panorama_Exit},a)}switchMode(a){return this.int.call({command:h.Panorama_Switch},a)}}class tb extends G{constructor(a){super(a,"Polygon","color|frameColor")}add(a,b){this._fixPolygonData(a);return super._add(a,b)}_fixPolygonProperty(a){a.brightness=0==a.intensity||1==a.intensity?a.intensity:1;11==a.style&&(a.style=0,
a.brightness=1,a.intensity=1);Array.isArray(a.viewHeightRange)&&(a.minVisibleHeight=a.viewHeightRange[0],a.maxVisibleHeight=a.viewHeightRange[1])}_fixPolygonData(a){if(a instanceof Array)for(let b of a)this._fixPolygonProperty(b);else this._fixPolygonProperty(a)}update(a,b){this._fixPolygonData(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,
b)}get(a,b){return super._get(a,b)}glow(a,b,c){return this.int.call({command:h.Polygon_Glow,ids:D(a),duration:b},c)}highlight(a,b){return this.int.call({command:h.Polygon_Highlight,ids:D(a)},b)}stopHighlight(a,b){return this.int.call({command:h.Polygon_StopHighlight,ids:D(a)},b)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",E(b),c)}setColor(a,b,c){return super._updateOneProp(a,"color",y(b),c)}setDepthTest(a,b,c){return super._updateOneProp(a,"depthTest",b,c)}setViewHeightRange(a,
b,c,d){return this.int.call({command:h.Polygon_Update,data:[{id:a,minVisibleHeight:b,maxVisibleHeight:c}]},d)}}class ub extends G{constructor(a){super(a,"Polygon3D","color")}add(a,b){this._fixPolygonData(a);return super._add(a,b)}_fixPolygonProperty(a){11==a.style&&(a.style=9,a.intensity=1);17==a.style&&(a.style=11);Array.isArray(a.viewHeightRange)&&(a.minVisibleHeight=a.viewHeightRange[0],a.maxVisibleHeight=a.viewHeightRange[1])}_fixPolygonData(a){if(a instanceof Array)for(let b of a)this._fixPolygonProperty(b);
else this._fixPolygonProperty(a)}update(a,b){this._fixPolygonData(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}showAll(a){return super._showAll(a)}hideAll(a){return super._hideAll(a)}highlight(a,b){return this.int.call({command:h.Polygon3D_Highlight,ids:D(a)},b)}stopHighlight(a,b){return this.int.call({command:h.Polygon3D_StopHighlight,
ids:D(a)},b)}glow(a,b){if(a instanceof Array)for(let c of a){let d;d=y(c.color);c.colors=[{color:[d[0]/2,d[1]/2,d[2]/2],value:0},{color:c.color,value:1}]}else o.colors=[{color:o.color,value:0},{color:o.color,value:1}];return this.int.call({command:h.Polygon3D_Glow,data:x(a)},b)}stopGlow(a,b){return this.int.call({command:h.Polygon3D_StopGlow,ids:D(a)},b)}setStyle(a,b,c){return super._updateOneProp(a,"style",b,c)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",b,c)}setColor(a,b,c){return super._updateOneProp(a,
"color",y(b),c)}setHeight(a,b,c){return super._updateOneProp(a,"height",b,c)}setIntensity(a,b,c){return super._updateOneProp(a,"intensity",b,c)}setTillingX(a,b,c){return super._updateOneProp(a,"tillingX",b,c)}setTillingY(a,b,c){return super._updateOneProp(a,"tillingY",b,c)}setViewHeightRange(a,b,c,d){return this.int.call({command:h.Polygon3D_Update,data:[{id:a,minVisibleHeight:b,maxVisibleHeight:c}]},d)}enableClip(a,b){let c=[];a=x(a);for(let d of a)c.push({id:d,bClip:!0});return this.int.call({command:h.Polygon3D_Update,
data:c},b)}disableClip(a,b){let c=[];a=x(a);for(let d of a)c.push({id:d,bClip:!1});return this.int.call({command:h.Polygon3D_Update,data:c},b)}}class vb extends G{constructor(a){super(a,"Polyline","color")}add(a,b){this._fixPolylineData(a);return super._add(a,b)}_fixPolylineProperty(a){a.intensity&&(a.brightness=a.intensity);8==a.style&&(a.style=4,a.brightness=1,a.intensity=1);Array.isArray(a.viewHeightRange)&&(a.minVisibleHeight=a.viewHeightRange[0],a.maxVisibleHeight=a.viewHeightRange[1])}_fixPolylineData(a){if(a instanceof
Array)for(let b of a)this._fixPolylineProperty(b);else this._fixPolylineProperty(a)}update(a,b){this._fixPolylineData(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",
b,c)}setStyle(a,b,c){return super._updateOneProp(a,"style",b,c)}setThickness(a,b,c){return super._updateOneProp(a,"thickness",b,c)}setColor(a,b,c){return super._updateOneProp(a,"color",y(b),c)}setFlowRate(a,b,c){return super._updateOneProp(a,"flowRate",b,c)}setBrightness(a,b,c){return super._updateOneProp(a,"brightness",b,c)}setShape(a,b,c){return super._updateOneProp(a,"shape",b,c)}setDepth(a,b,c){return super._updateOneProp(a,"depthTest",b,c)}setViewHeightRange(a,b,c,d){return this.int.call({command:h.Polyline_Update,
data:[{id:a,minVisibleHeight:b,maxVisibleHeight:c}]},d)}}class wb extends G{constructor(a){super(a,"RadiationPoint","color")}add(a,b){this._fixRadiationPointData(a);return super._add(a,b)}_fixRadiationPointProperty(a){a.intensity&&(a.brightness=a.intensity)}_fixRadiationPointData(a){if(a instanceof Array)for(let b of a)this._fixRadiationPointProperty(b);else this._fixRadiationPointProperty(a)}update(a,b){this._fixRadiationPointData(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,
b,c,d,e){return super._focus(a,b,c,d,e)}focusAll(a,b,c,d){return super._focusAll(a,b,c,d)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}showAll(a){return super._showAll(a)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setCoordinate(a,b,c){return super._updateOneProp(a,"coordinate",b,c)}setRadius(a,b,c){return super._updateOneProp(a,"radius",b,c)}setRippleNumber(a,b,c){return super._updateOneProp(a,"rippleNumber",b,c)}setColor(a,b,c){return super._updateOneProp(a,
"color",y(b),c)}setBrightness(a,b,c){return super._updateOneProp(a,"brightness",b,c)}setAutoHeight(a,b,c){return super._updateOneProp(a,"autoHeight",b,c)}}class xb extends G{constructor(a){super(a,"River","arrowColor|waterColor")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,
b)}}class yb extends G{constructor(a){super(a,"ShapeFileLayer","color")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}_fix_property(a){if(a instanceof Array){let b=[];for(let c of a)K(c.cacheAllField)&&(c.cacheAllField=!1,b.push(c));a=b}else K(a.cacheAllField)&&(a.cacheAllField=!1);return a}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}get(a,b){return super._get(a,b)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,
b){return super._hide(a,b)}showAll(a){return super._showAll(a)}hideAll(a){return super._hideAll(a)}open(a,b){return this.int.call({command:h.ShapeFileLayer_OpenShapeFileLayer,data:x(a)},b)}highlightFeature(a,b,c){return this.int.call({command:h.ShapeFileLayer_HighlightFeature,data:x({shpId:a,featureIds:[b]})},c)}stopHighlightFeature(a,b,c){return this.int.call({command:h.ShapeFileLayer_UnHighlightFeature,data:x({shpId:a,featureIds:[b]})},c)}highlightFeatures(a,b){return this.int.call({command:h.ShapeFileLayer_HighlightFeature,
data:x(a)},b)}stopHighlightFeatures(a,b){return this.int.call({command:h.ShapeFileLayer_UnHighlightFeature,data:x(a)},b)}focusFeature(a,b,c,d,e,f){return this.int.call({command:h.ShapeFileLayer_FocusFeature,data:x({shpId:a,featureIds:[b]}),distance:c,flyTime:d,rotation:e},f)}getFeature(a,b){return this.int.call({command:h.ShapeFileLayer_GetFeatureInfo,data:x(a)},b)}}class zb extends G{constructor(a){super(a,"SignalWave","olor")}add(a,b){this._fixMarkerData(a,"add");return super._add(a,b)}_fixMarkerProperty(a,
b){if(0<=a.alpha&&1>=a.alpha)if(0==a.alpha)a.alpha=.9;else if(1==a.alpha)a.alpha=.8;else{b=a.alpha;var c=[0,1],d=[.9,.8];b=b<c[0]||b>c[1]?NaN:(b-c[0])*(d[1]-d[0])/(c[1]-c[0])+d[0];a.alpha=b}else a.alpha=1}_fixMarkerData(a,b){if(a instanceof Array)for(let c of a)this._fixMarkerProperty(c,b);else this._fixMarkerProperty(a,b)}update(a,b){this._fixMarkerData(a,"update");return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,
b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}}class Ab extends G{constructor(a){super(a,"SplineMesh")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,
b)}}class Bb extends G{constructor(a){super(a,"Tag","textColor|textBackgroundColor|textBorderColor")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}focusAll(a,b,c,d){return super._focusAll(a,b,c,d)}show(a,b){return super._show(a,b)}showAll(a){return super._showAll(a)}hide(a,b){return super._hide(a,b)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,
b)}setCoordinate(a,b,c){return super._updateOneProp(a,"coordinate",b,c)}setImagePath(a,b,c){return super._updateOneProp(a,"imagePath",b,c)}setImageSize(a,b,c){return super._updateOneProp(a,"imageSize",b,c)}setText(a,b,c){return super._updateOneProp(a,"text",b,c)}setRange(a,b,c){return super._updateOneProp(a,"range",b,c)}setTextColor(a,b,c){return super._updateOneProp(a,"textColor",b,c)}setTextBackgroundColor(a,b,c){return super._updateOneProp(a,"textBackgroundColor",b,c)}setTextBorderColor(a,b,c){return super._updateOneProp(a,
"textBorderColor",b,c)}setShowLine(a,b,c){return super._updateOneProp(a,"showLine",b,c)}setAutoHidePopupWindow(a,b,c){return super._updateOneProp(a,"autoHidePopupWindow",b,c)}setURL(a,b,c){return super._updateOneProp(a,"url",b,c)}showPopupWindow(a,b){return this.int.call({command:h.Tag_PopupWindow_Show,ids:D(a)},b)}hidePopupWindow(a,b){return this.int.call({command:h.Tag_PopupWindow_Hide,ids:D(a)},b)}showAllPopupWindow(a){return this.int.call0(h.Tag_PopupWindow_ShowAll,a)}hideAllPopupWindow(a){return this.int.call0(h.Tag_PopupWindow_HideAll,
a)}}class Cb{constructor(){this._where=this._fields=this._id=null}get id(){return this._id}set id(a){this._id=a}get fields(){return this._fields}set fields(a){this._fields=a}get where(){return this._where}set where(a){this._where=a}get limit(){return this._limit}set limit(a){this._limit=a}get offset(){return this._offset}set offset(a){this._offset=a}}class Db extends G{constructor(a){super(a,"TileLayer")}add(a,b){this._fixMarkerProperty(a);return super._add(a,b)}_fixMarkerProperty(a){Array.isArray(a.viewHeightRange)&&
(a.minVisibleHeight=a.viewHeightRange[0],a.maxVisibleHeight=a.viewHeightRange[1])}_fixMarkerData(a,b){if(a instanceof Array)for(let c of a)this._fixMarkerProperty(c,b);else this._fixMarkerProperty(a,b)}update(a,b){this._fixMarkerProperty(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}clear(a){return super._clear(a)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}getAllFlattenInfo(a){return this.int.call({command:h.TileLayer_GetAllFlattenData},
a)}getActorInfoFromDB(a,b){return this.int.call({command:h.TileLayer_GetActorInfoFromDB,data:x(a)},b)}enableXRay(a,b,c){return this.int.call({command:h.TileLayer_EnableXRay,ids:D(a),color:y(b)},c)}disableXRay(a,b){return this.int.call({command:h.TileLayer_DisableXRay,ids:D(a)},b)}enableClip(a,b){let c=[];a=x(a);for(let d of a)c.push({id:d,allowClip:!0});return this.int.call({command:h.TileLayer_SetAllowClip,data:c},b)}disableClip(a,b){let c=[];a=x(a);for(let d of a)c.push({id:d,allowClip:!1});return this.int.call({command:h.TileLayer_SetAllowClip,
data:c},b)}setFileName(a,b,c){return super._updateOneProp(a,"fileName",b,c)}setLocation(a,b,c){return super._updateOneProp(a,"location",b,c)}setTranslation(a,b,c){return this.setLocation(a,b,c)}setRotation(a,b,c){return super._updateOneProp(a,"rotation",b,c)}setScale(a,b,c){return super._updateOneProp(a,"scale",b,c)}showActor(a,b,c){a=new U(a,b);return this.int.call({command:h.TileLayer_Actor_Show,data:x(a)},c)}showActors(a,b){let c=[];if(a instanceof Array)for(let e=0;e<a.length;e++){var d=a[e];
d={tileLayerId:d.id,tileLayerActorNames:D(d.objectIds)};c.push(d)}else a={tileLayerId:a.id,tileLayerActorNames:D(a.objectIds)},c.push(a);return this.int.call({command:h.TileLayer_Actor_Show,data:c},b)}hideActor(a,b,c){a=new U(a,b);return this.int.call({command:h.TileLayer_Actor_Hide,data:x(a)},c)}hideActors(a,b){let c=[];if(a instanceof Array)for(let e=0;e<a.length;e++){var d=a[e];d={tileLayerId:d.id,tileLayerActorNames:D(d.objectIds)};c.push(d)}else a={tileLayerId:a.id,tileLayerActorNames:D(a.objectIds)},
c.push(a);return this.int.call({command:h.TileLayer_Actor_Hide,data:c},b)}focusActor(a,b,c,d,e,f){a=new U(a,b);return this.int.call({command:h.TileLayer_Actor_Focus,data:x(a),distance:c,flyTime:d,rotation:e},f)}focusActors(a,b,c,d,e){a=new U(a.id,a.objectIds);return this.int.call({command:h.TileLayer_Actor_Focus,data:x(a),distance:b,flyTime:c,rotation:d},e)}highlightActor(a,b,c){a=new U(a,b);return this.int.call({command:h.TileLayer_Actor_Highlight,data:x(a)},c)}stopHighlightActor(a,b,c){let d=void 0==
a;"function"==typeof a&&(c=a,d=!0);if(d)return this.stopHighlightActors(c);if(K(a)||K(b))console.error("\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a!");else return this.stopHighlightActors({id:a,objectIds:[b]},c)}highlightActors(a,b){return this.int.call({command:h.TileLayer_Actor_Highlight,data:this._fix_highlight_actors_data(a)},b)}stopHighlightActors(a,b){let c=!a;"function"==typeof a&&(b=a,c=!0);return c?this.int.call0(h.TileLayer_Actor_StopHightlight,b):this.int.call({command:h.TileLayer_Actor_Unhighlight,
data:this._fix_highlight_actors_data(a)},b)}_fix_highlight_actors_data(a){if(K(a))console.error("\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a!");else{var b=[];if(a instanceof Array)for(let c=0;c<a.length;c++){let d=new U(a[c].id,a[c].objectIds);b.push(d)}else a=new U(a.id,a.objectIds),b.push(a);return b}}stopHighlightAllActors(a){return this.int.call0(h.TileLayer_Actor_StopHightlight,a)}showAllActors(a,b){return this.int.call({command:h.TileLayer_Actor_ShowAll,ids:D(a)},b)}hideAllActors(a,b){return this.int.call({command:h.TileLayer_Actor_HideAll,
ids:D(a)},b)}setStyle(a,b,c,d,e,f,l,g){return this.int.call({command:h.TileLayer_SetStyle,ids:D(a),style:b,color:y(c),saturation:d,brightness:e,contrast:f,contrastbase:l},g)}setHeatMapStyle(a,b,c){return this.int.call({command:h.TileLayer_SetStyle,ids:D(a),style:7,colors:{gradient:!0,invalidColor:[1,1,1,0],colorStops:b}},c)}setCollision(a,b,c,d,e,f){a=D(a);let l=[];for(let g of a)l.push({id:g,enableCollision:b,enableMouseRoma:c,enableFunctionInteract:d,enableCharacterCollision:e});return this.int.call({command:h.TileLayer_SetCollision,
data:l},f)}getCollision(a,b){return this.int.call({command:h.TileLayer_GetCollision,ids:D(a)},b)}addModifiers(a,b){return this.int.call({command:h.TileLayer_Modifier_Add,data:x(a)},b)}addModifierByShapeFile(a,b){return this.int.call({command:h.TileLayer_Modifier_Add,data:x(a)},b)}addModifier(a,b,c,d,e){this.addModifiers([{id:a,tileLayerId:b,coordinates:c,ententBufferSize:d}])}updateModifier(a,b,c,d,e){return this.int.call({command:h.TileLayer_Modifier_Update,data:x([{id:a,tileLayerId:b,coordinates:c,
ententBufferSize:d}])},e)}deleteModifier(a,b,c){return this.int.call({command:h.TileLayer_Modifier_Delete,id:a,tileLayerId:b},c)}clearModifier(a,b){return this.int.call({command:h.TileLayer_Modifier_Clear,ids:D(a)},b)}setViewportVisible(a,b,c){let d=[];d.push({viewportIndex:1,viewportVisible:!!(b&R.V1)});d.push({viewportIndex:2,viewportVisible:!!(b&R.V2)});d.push({viewportIndex:3,viewportVisible:!!(b&R.V3)});d.push({viewportIndex:4,viewportVisible:!!(b&R.V4)});return this.int.call({command:h.TileLayer_SetViewportVisible,
id:a,data:d},c)}getObjectIDs(a,b){return this.int.call({command:h.TileLayer_GetObjectIDs,ids:D(a)},b)}getActorInfo(a,b){return this.int.call({command:h.TileLayer_Actor_GetInfo,data:x(a)},b)}addHoleByShapeFile(a,b){return this.int.call({command:h.TileLayer_CutPolygon_Add,data:x(a)},b)}addHole(a,b){return this.int.call({command:h.TileLayer_CutPolygon_Add,data:x(a)},b)}updateHole(a,b,c,d,e){return this.int.call({command:h.TileLayer_CutPolygon_Update,data:x({id:a,tileLayerId:b,coordinates:c,isReverseCut:d})},
e)}deleteHole(a,b,c){return this.int.call({command:h.TileLayer_CutPolygon_Delete,id:a,tileLayerId:b},c)}clearHole(a,b){return this.int.call({command:h.TileLayer_CutPolygon_Clear,ids:D(a)},b)}setViewHeightRange(a,b,c,d){return this.int.call({command:h.TileLayer_SetViewHeightRange,data:[{id:a,minVisibleHeight:b,maxVisibleHeight:c}]},d)}setPointCloudSize(a,b,c){return this.int.call({command:h.TileLayer_SetPointSize,data:[{tileLayerId:a,size:b}]},c)}enableDecal(a,b){this.setEnableDecal(a,b)}enableFluid(a,
b){return this.int.call({command:h.TileLayer_EnableFluid,data:x(a)},b)}setEnableDecal(a,b){return this.int.call({command:h.TileLayer_SetDecalAttach,data:a},b)}enableImageLayerDecal(a,b){return this.int.call({command:h.TileLayer_TileLayerReceiveWMTSDecal,data:a},b)}getDBTabID(a,b){return this.int.call({command:h.TileLayer_GetDBTabID,ids:x(a)},b)}createQuery(){return new Cb}query(a,b){const c=this;return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){if(!a||!a.id)return c.int.logWithColor("red",
"Invalid queryOption!"),Promise.reject();let d=yield c.getDBTabID(a.id);return d.result==fa.OK&&d.data&&0!=d.data.length?c.int.sendApiToCS({type:"api",command:"PG_Query",id:d.data[0],fields:a.fields,where:a.where,limit:a.limit,offset:a.offset},b):(c.int.logWithColor("red","Unable to get dbTabId based on tilelayerId!"),Promise.reject())})}updateRecord(a,b,c,d){const e=this;return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){if(!b||!c)return e.int.logWithColor("red","Invalid parameters!"),
Promise.reject();let f=yield e.getDBTabID(a);return f.result==fa.OK&&f.data&&0!=f.data.length?e.int.sendApiToCS({type:"api",command:"PG_Update",id:f.data[0],newValMap:b,where:c},d):(e.int.logWithColor("red","Unable to get dbTabId based on tilelayerId!"),Promise.reject())})}}class Eb extends G{constructor(a){super(a,"VectorField")}add(a,b){this._fixMarkerData(a,"add");return super._add(a,b)}_fixMarkerProperty(a,b){"add"==b&&a.binFilePath&&(a.vectorFieldFilePath=a.binFilePath,a.vetorFieldFilePath=a.binFilePath)}_fixMarkerData(a,
b){if(a instanceof Array)for(let c of a)this._fixMarkerProperty(c,b);else this._fixMarkerProperty(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}setViewportVisible(a,b,c){let d=[];d.push({viewportIndex:1,viewportVisible:!!(b&Viewport.V1)});d.push({viewportIndex:2,viewportVisible:!!(b&Viewport.V2)});
d.push({viewportIndex:3,viewportVisible:!!(b&Viewport.V3)});d.push({viewportIndex:4,viewportVisible:!!(b&Viewport.V4)});return this.int.call({command:h.VectorField_SetViewPort,id:a,data:d},c)}}class Fb extends G{constructor(a){super(a,"Vehicle","color")}add(a,b){this._fixVehicleData(a);return super._add(a,b)}_fixVehicleData(a){if(a instanceof Array)for(let b of a)this._fixVehicleProperty(b);else this._fixVehicleProperty(a)}_fixVehicleProperty(a){if(void 0===a.delay||null===a.delay||""===a.delay)a.delay=
.5;0<a.colorType&&K(a.color)?a.initFunctionInfos=[{functionName:"setVehicleColor",parameters:[{paramType:2,paramValue:a.colorType}]}]:a.colorType=0;a.color&&(a.initFunctionInfos=[{functionName:"setColor",parameters:[{paramType:6,paramValue:y(a.color)}]}])}update(a,b){this._fixVehicleData(a);return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e,f,l,g){return this.int.call({command:h.Vehicle_Focus,ids:x(a),distance:c||0,flyTime:d||2,rotation:e||
[0,0,0],distanceRotation:f||[0,0,0],followEnabled:b||!1,offset:l||[0,0,0],sensitivity:.02},g)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}setWayPoint(a,b){return this.int.call({command:h.Vehicle_AddWayPoints,data:x(a)},b)}moveTo(a,b){return this.int.call({command:h.Vehicle_MoveTo,data:x(a)},b)}start(a,b){if(K(a))console.error("\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a!");else{if(0<=a.timeStamp)a.active=!0;else if(a instanceof Array)for(let c of a)c.active=
!0;else{console.error("\u53c2\u6570\u975e\u6cd5!");return}return this.int.call({command:h.Vehicle_Update,data:x(a)},b)}}pause(a,b){a=this._fixIds(a,!1);if(!K(a))return this.int.call({command:h.Vehicle_Update,data:a},b)}resume(a,b){a=this._fixIds(a,!0);if(!K(a))return this.int.call({command:h.Vehicle_Update,data:a},b)}stop(a,b){a=this._fixIds(a,!1);if(!K(a))return this.int.call({command:h.Vehicle_Update,data:a},b)}_fixIds(a,b){let c=[];if(K(a))console.error("\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a!");
else{if(a instanceof Array){let d=!1;for(let e of a){if("string"!=typeof e){d=!0;break}c.push({id:e,active:b})}if(d){console.error("\u53c2\u6570\u975e\u6cd5!");return}return c}if("string"!==typeof a||K(a))console.error("\u53c2\u6570\u975e\u6cd5!");else return c.push({id:a,active:b}),c}}callBatchFunction(a,b){return this.int.call({command:h.Vehicle_CallBatchFunction,data:x(a)},b)}}class Gb extends G{constructor(a){super(a,"Vehicle2","color")}add(a,b){this._fixVehicleData(a);return super._add(a,b)}_fixVehicleData(a){if(a instanceof
Array)for(let b of a)this._fixVehicleProperty(b);else this._fixVehicleProperty(a)}_fixVehicleProperty(a){a.timeMode=1;if(void 0===a.delay||null===a.delay||""===a.delay)a.delay=.5;0<a.colorType&&K(a.color)?a.initFunctionInfos=[{functionName:"setVehicleColor",parameters:[{paramType:2,paramValue:a.colorType}]}]:a.colorType=0;a.color&&(a.initFunctionInfos=[{functionName:"setColor",parameters:[{paramType:6,paramValue:y(a.color)}]}])}update(a,b){this._fixVehicleData(a);return super._update(a,b)}delete(a,
b){return super._delete(a,b)}clear(a){return super._clear(a)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}focus(a,b,c,d,e,f){K(d)&&(d=[0,0,0]);return this.int.call({command:h.Vehicle2_Focus,ids:x(a),followEnabled:!1,distance:b||5,flyTime:c||2,viewPitch:d[0]||0,viewYaw:d[1]||0,offset:e||[0,0,0],sensitivity:.02},f)}setFollow(a,b,c,d,e,f,l,g,t){return this.int.call({command:h.Vehicle2_Focus,ids:x(a),distance:b||0,flyTime:c||2,viewPitch:d||0,viewYaw:e||
0,offset:g||[0,0,0],sensitivity:f||.02,viewControl:l||!1,followEnabled:!0},t)}moveTo(a,b){return this.int.call({command:h.Vehicle2_MoveTo,data:x(a)},b)}callBPFunction(a,b){this.callBatchFunction(a,b)}callBatchFunction(a,b){return this.int.call({command:h.Vehicle_CallBatchFunction,data:x(a)},b)}}class Hb extends G{constructor(a){super(a,"VideoProjection","frustumColor")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,
b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,b)}setVideoURL(a,b,c){return super._updateOneProp(a,"videoURL",b,c)}setLocation(a,b,c){return super._updateOneProp(a,"location",b,c)}setRotation(a,b,c){return super._updateOneProp(a,"rotation",b,c)}setFovy(a,b,c){return super._updateOneProp(a,"fov",b,c)}setAspectRatio(a,b,c){return super._updateOneProp(a,"aspectRatio",b,c)}setDistance(a,b,c){return super._updateOneProp(a,
"distance",b,c)}setDepthCulling(a,b,c){return super._updateOneProp(a,"depthCulling",b,c)}setFrustumColor(a,b,c){return super._updateOneProp(a,"frustumColor",b,c)}}class Ib extends G{constructor(a){super(a,"WaterFlowField")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}get(a,b){return super._get(a,
b)}setViewportVisible(a,b,c){let d=[];d.push({viewportIndex:1,viewportVisible:!!(b&Viewport.V1)});d.push({viewportIndex:2,viewportVisible:!!(b&Viewport.V2)});d.push({viewportIndex:3,viewportVisible:!!(b&Viewport.V3)});d.push({viewportIndex:4,viewportVisible:!!(b&Viewport.V4)});return this.int.call({command:h.WaterFlowField_SetViewPort,id:a,data:d},c)}}class Jb extends G{constructor(a){super(a,"WaterMesh","waterColor")}add(a,b){return super._add(a,b)}update(a,b){return super._update(a,b)}delete(a,
b){return super._delete(a,b)}clear(a){return super._clear(a)}focus(a,b,c,d,e){return super._focus(a,b,c,d,e)}show(a,b){return super._show(a,b)}hide(a,b){return super._hide(a,b)}showAll(a){return super._showAll(a)}hideAll(a){return super._hideAll(a)}get(a,b){return super._get(a,b)}setCoordinates(a,b,c){return super._updateOneProp(a,"coordinates",b,c)}setIndices(a,b,c){return super._updateOneProp(a,"indices",b,c)}setNormals(a,b,c){return super._updateOneProp(a,"normals",b,c)}setWaterColor(a,b,c){return super._updateOneProp(a,
"waterColor",b,c)}setWaterSpeed(a,b,c){return super._updateOneProp(a,"waterSpeed",b,c)}setWaterUVRepeat(a,b,c){return super._updateOneProp(a,"waterUVRepeat",b,c)}setWaterDirection(a,b,c){return super._updateOneProp(a,"waterDirection",b,c)}setWaterWaveScale(a,b,c){return super._updateOneProp(a,"waveScale",b,c)}}var Kb={VersionMismatch:"Warning:\n--------------------------------------------- -----\n The version of the ac.min.js file ({0}) and the version of the cloud rendering server ({1}) are inconsistent,\n may cause interface call errors or crashes, please confirm!",
Disconnect:"Disconnect",TimeConsuming:"TimeConsuming: ",RequestTime:"RequestTime: ",ResponseTime:"ResponseTime: ",MessageLength:"MessageLength: ",DomLoading:"DOM is still loading, cannot initialize DigitalTwinPlayer at this time!",CannotChangeProject:"The current instance has been set to lock the project, and the project cannot be switched through the API interface",InstanceNotExist:"The corresponding instance does not exist, please confirm",ProjectNotExist:"The corresponding project does not exist, please confirm",
JSQueue:"JS queue:",BackQueue:"Background queue:",Execting:"Executing: ",PleaseWait:"Please wait...",DisconnectForIdle:"Disconnected after a long time of inactivity",NodeInfo:"Render node information:",Host:"Host: ",HostAddress:"Host address:",Project:"Project: ",ProjectId:"ProjectId: ",IID:"IID: ",Adaptive:"Adaptive: ",LimitMaxRes:"Limit max resolution: ",Resolution:"Resolution: ",Codec:"Codec: ",Platform:"Platform: ",Destroyed:"destroyed - user destroyed",Reconnect5s:"Will reconnect in 5 seconds...",
RestartAndRetry:"The current instance is busy and cannot be connected! \nDo you want to restart this instance now to connect?",EnterFullscreen:"enter fullscreen",ExitFullscreen:"exit fullscreen",TriggerSysTouch:"The system touch operation is triggered, and the TouchEnd event will be sent manually",RestartInstNow:"Reboot instance",LeftClickTip:"Click with the left mouse button: return to the saved position or the initial position of the project",RightClickTip:"Right mouse click: save the current camera position",
MiddleClickTip:"Middle mouse click: set the current position as the initial position of the project and jump",Close:"Close",Connections:"Connections",ConnInfo:"ConnInfo:",Duration:"Duration:",Received:"Received:",ReceivedFrames:"Frames:",Dropped:"Dropped:",DroppedTip:"Number of dropped packets / Number of dropped frames",DecodeTimeTip:"browser decoding time",DecodingTime:"DecTime:",DecodeFrames:"DecFrame:",DecodeFramesTip:"Decoded Frames / Decoded Keyframes",Bitrate:"Bitrate:",FPS:"FPS:",QP:"QP:",
FPSTip:"Rendering frame rate/video frame rate",MaxQP:"MaxQP:",MaxQPTip:"The larger the value, the rougher the picture quality and the smaller the required bandwidth",MaxBitrate:"MaxBitrate:"},Lb={VersionMismatch:"\u8b66\u544a\uff1a\n--------------------------------------------------\n ac.min.js\u6587\u4ef6\u7248\u672c\uff08{0}\uff09\u548c\u4e91\u6e32\u67d3\u670d\u52a1\u5668\u7248\u672c\uff08{1}\uff09\u4e0d\u4e00\u81f4\uff0c\n \u53ef\u80fd\u9020\u6210\u63a5\u53e3\u8c03\u7528\u9519\u8bef\u6216\u5d29\u6e83\uff0c\u8bf7\u786e\u8ba4!",
Disconnect:"\u8fde\u63a5\u65ad\u5f00",TimeConsuming:"\u8017\u65f6\uff1a",RequestTime:"\u8bf7\u6c42\u65f6\u95f4\uff1a",ResponseTime:"\u54cd\u5e94\u65f6\u95f4\uff1a",MessageLength:"\u6d88\u606f\u957f\u5ea6\uff1a",DomLoading:"DOM\u5c1a\u5728\u52a0\u8f7d\u4e2d\uff0c\u6b64\u65f6\u4e0d\u80fd\u521d\u59cb\u5316DigitalTwinPlayer!",CannotChangeProject:"\u5f53\u524d\u5b9e\u4f8b\u5df2\u8bbe\u7f6e\u9501\u5b9a\u5de5\u7a0b\uff0c\u65e0\u6cd5\u901a\u8fc7API\u63a5\u53e3\u5207\u6362\u5de5\u7a0b",InstanceNotExist:"\u5bf9\u5e94\u7684\u5b9e\u4f8b\u4e0d\u5b58\u5728\uff0c\u8bf7\u786e\u8ba4",
ProjectNotExist:"\u5bf9\u5e94\u7684\u5de5\u7a0b\u4e0d\u5b58\u5728\uff0c\u8bf7\u786e\u8ba4",JSQueue:"JS\u961f\u5217\uff1a",BackQueue:"\u540e\u53f0\u961f\u5217\uff1a",Execting:"\u6b63\u5728\u6267\u884c\uff1a",PleaseWait:"\uff0c\u8bf7\u7a0d\u5019...",DisconnectForIdle:"\u957f\u65f6\u95f4\u672a\u4f7f\u7528\u8fde\u63a5\u5df2\u65ad\u5f00",NodeInfo:"\u6e32\u67d3\u8282\u70b9\u4fe1\u606f\uff1a",Host:"\u4e3b\u673a\u540d\u79f0\uff1a",HostAddress:"\u4e3b\u673a\u5730\u5740\uff1a",Project:"\u5de5\u7a0b\uff1a",
ProjectId:"\u5de5\u7a0bID\uff1a",IID:"\u5b9e\u4f8bID\uff1a",Adaptive:"\u81ea\u9002\u5e94\uff1a",LimitMaxRes:"\u9650\u5236\u6700\u5927\u5206\u8fa8\u7387\uff1a",Resolution:"\u5206\u8fa8\u7387\uff1a",Codec:"\u7f16\u89e3\u7801\u5668\uff1a",Platform:"\u5e73\u53f0\uff1a",Destroyed:"destroyed - \u7528\u6237\u9500\u6bc1",Reconnect5s:"\u5c06\u57285\u79d2\u540e\u91cd\u65b0\u8fde\u63a5...",RestartAndRetry:"\u5f53\u524d\u5b9e\u4f8b\u6b63\u5fd9\u65e0\u6cd5\u8fde\u63a5\uff01\n\u662f\u5426\u7acb\u5373\u91cd\u542f\u8be5\u5b9e\u4f8b\u4ee5\u8fdb\u884c\u8fde\u63a5\uff1f",
EnterFullscreen:"\u8fdb\u5165\u5168\u5c4f",ExitFullscreen:"\u9000\u51fa\u5168\u5c4f",TriggerSysTouch:"\u89e6\u53d1\u4e86\u7cfb\u7edf\u89e6\u6478\u64cd\u4f5c\uff0c\u5c06\u624b\u52a8\u53d1\u9001TouchEnd\u4e8b\u4ef6",RestartInstNow:"\u7acb\u5373\u91cd\u542f\u5b9e\u4f8b",LeftClickTip:"\u9f20\u6807\u5de6\u952e\u70b9\u51fb\uff1a\u56de\u5230\u4fdd\u5b58\u7684\u4f4d\u7f6e\u6216\u8005\u5de5\u7a0b\u521d\u59cb\u4f4d\u7f6e",RightClickTip:"\u9f20\u6807\u53f3\u952e\u70b9\u51fb\uff1a\u4fdd\u5b58\u5f53\u524d\u7684\u76f8\u673a\u4f4d\u7f6e",
MiddleClickTip:"\u9f20\u6807\u4e2d\u952e\u70b9\u51fb\uff1a\u5c06\u5f53\u524d\u4f4d\u7f6e\u8bbe\u7f6e\u4e3a\u5de5\u7a0b\u521d\u59cb\u4f4d\u7f6e\u5e76\u8df3\u8f6c",Close:"\u5173\u95ed",Connections:"\u8fde\u63a5\u4e2a\u6570",ConnInfo:"\u8fde\u63a5\u4fe1\u606f\uff1a",Duration:"\u8fd0\u884c\u65f6\u957f\uff1a",Received:"\u63a5\u6536\u6570\u636e\uff1a",ReceivedFrames:"\u63a5\u6536\u5e27\u6570\uff1a",Dropped:"\u4e22\u5305\u4e22\u5e27\uff1a",DroppedTip:"\u4e22\u5305\u6570\u91cf / \u4e22\u5e27\u6570\u91cf",
DecodeTimeTip:"\u6d4f\u89c8\u5668\u89e3\u7801\u65f6\u95f4",DecodingTime:"\u89e3\u7801\u65f6\u95f4\uff1a",DecodeFrames:"\u89e3\u7801\u5e27\u6570\uff1a",DecodeFramesTip:"\u5df2\u89e3\u7801\u7684\u5e27\u6570 / \u5df2\u89e3\u7801\u7684\u5173\u952e\u5e27\u6570\u91cf",Bitrate:"\u7801\u7387\uff1a",FPS:"\u5e27\u7387\uff1a",QP:"QP\uff1a",FPSTip:"\u6e32\u67d3\u5e27\u7387/\u89c6\u9891\u5e27\u7387",MaxQP:"QP\u4e0a\u9650\uff1a",MaxQPTip:"\u503c\u8d8a\u5927\uff0c\u753b\u8d28\u8d8a\u7c97\u7cd9\uff0c\u9700\u8981\u7684\u5e26\u5bbd\u8d8a\u5c0f",
MaxBitrate:"\u7801\u7387\u4e0a\u9650\uff1a"};String.prototype.format=function(){if(0==arguments.length)return this;for(var a=this,b=0;b<arguments.length;b++)a=a.replace(new RegExp("\\{"+b+"\\}","g"),arguments[b]);return a};class B{}B.language="en";B.onLanguageChangedCallbacks=[];B.setLanguage=a=>{B.language=a;B.languageMap="zh"==a?Lb:Kb};B.getString=a=>B.languageMap[a];class ba{static valid(){return"object"==typeof ue&&"object"==typeof ue.internal}static _check(){if(!this.valid())throw"This method must be called on the WebUI page or Marker pop-up page!";
}static execute(a){this._check();ue.internal.execute(a)}static getViewportSize(){this._check();return ue.internal.execute("viewportSize")}static setData(a,b){this._check();ue.internal.execute("setItem",a,b)}static getData(a){this._check();return ue.internal.execute("getItem",a)}static showTickWindow(a){this._check();return ue.internal.showtickwindow(a)}static close(){this._check();return ue.internal.closewindow()}static postEvent(a){this._check();ue.internal.postevent(a)}}"object"==typeof window&&
(window.FdExternal=ba);class Mb{constructor(a){this.int=a;this._useOldDataFormat=!1}useOldDataFormat(a){this._useOldDataFormat=a||!0}get(a){return this.int.call0(h.Camera_GetCamera,a)}_setByArray(a,b,c){return this.set(a[0],a[1],a[2],a[3],a[4],b,c)}_setByObject(a,b,c){return this.set(a.x,a.y,a.z,a.pitch,a.yaw,b,c)}set(a,b,c,d,e,f,l){if(a instanceof Array&&5<=a.length)return this._setByArray(a,b,c);if("object"==typeof a&&a.hasOwnProperty("x"))return this._setByObject(a,b,c);"function"==typeof f&&(l=
f,f=void 0);if(this._useOldDataFormat){let g=d;d=e;e=g}return this.int.call({command:h.Camera_Set,coordinate:[a,b,c],pitch:d||0,yaw:e||0,flyTime:f},l)}lookAt(a,b,c,d,e,f,l,g){"function"==typeof l&&(g=l,l=void 0);if(this._useOldDataFormat){let t=e;e=f;f=t}return this.int.call({command:h.Camera_Set,coordinate:[a,b,c],distance:d,pitch:e||0,yaw:f||0,flyTime:l},g)}lookAtBBox(a,b,c,d,e){"function"==typeof d&&(e=d,d=void 0);if(this._useOldDataFormat){let f=b;b=c;c=f}return this.int.call({command:h.Camera_LookAtBBox,
bbox:a,pitch:b||0,yaw:c||0,flyTime:d},e)}playAnimation(a,b){return this.int.call({command:h.Camera_PlayAnimation,id:a},b)}stopAnimation(a){return this.int.call0(h.Camera_StopAnimation,a)}pauseAnimation(a){return this.int.call0(h.Camera_PauseAnimation,a)}resumeAnimation(a){return this.int.call0(h.Camera_ResumeAnimation,a)}getAnimationList(a){return this.int.call0(h.Camera_GetAnimationList,a)}getAnimationImage(a,b){return this.int.call({command:h.Camera_GetAnimationImage,name:a},b)}moveForward(a){return this.int.call({command:h.Camera_Move,
moveForward:!0},a)}moveBackward(a){return this.int.call({command:h.Camera_Move,moveBackward:!0},a)}moveLeft(a){return this.int.call({command:h.Camera_Move,moveLeft:!0},a)}moveRight(a){return this.int.call({command:h.Camera_Move,moveRight:!0},a)}moveUp(a){return this.int.call({command:h.Camera_Move,moveUp:!0},a)}moveDown(a){return this.int.call({command:h.Camera_Move,moveDown:!0},a)}turnLeft(a){return this.int.call({command:h.Camera_Move,turnLeft:!0},a)}turnRight(a){return this.int.call({command:h.Camera_Move,
turnRight:!0},a)}turnUp(a){return this.int.call({command:h.Camera_Move,turnUp:!0},a)}turnDown(a){return this.int.call({command:h.Camera_Move,turnDown:!0},a)}stop(a){return this.int.call({command:h.Camera_Move,stop:!0},a)}getEulerAngle(a,b){const c=360/(2*Math.PI);let d=b[0]-a[0],e=b[1]-a[1];return[Math.atan2(b[2]-a[2],Math.sqrt(d*d+e*e))*c,Math.atan2(d,e)*c-90,0]}lockByBBox(a,b){return this.int.call({command:h.Camera_LockBBox,bbox:a},b)}unlock(a){return this.int.call({command:h.Camera_UnLockBBox},
a)}flyAround(a,b,c,d,e){return this.int.call({command:h.Camera_FlyAround,coordinate:a,rotation:b||[0,90,0],distance:c||1E3,time:d||10},e)}enterWorld(a){return this.int.call({command:h.Camera_ExitWorldAnimation},a)}exitWorld(a){return this.int.call({command:h.Camera_EnterWorldAnimation},a)}cancelFollow(a){return this.int.call({command:h.Camera_CancelFollow},a)}}class Nb{constructor(a){this.int=a}screen2World(a,b,c){return this.int.call({command:h.Coord_Screen2World,screenPosition:[a,b]},c)}world2Screen(a,
b,c,d){return this.int.call({command:h.Coord_World2Screen,worldlocation:[a,b,c]},d)}gcs2pcs(a,b,c){"function"==typeof b&&(c=b);K(b)&&(b=1);if(1==b)return this.int.call({command:h.Coord_GCS2PCS,coordinates:I(a)},c);2==b?this.transform(I(a),2,0,c):3==b?this.transform(I(a),3,0,c):console.error("\u672a\u77e5\u5750\u6807\u7cfb\u7c7b\u578b")}pcs2gcs(a,b,c){"function"==typeof b&&(c=b);K(b)&&(b=1);if(1==b)return this.int.call({command:h.Coord_PCS2GCS,coordinates:I(a)},c);2==b?this.transform(I(a),0,2,c):3==
b?this.transform(I(a),0,3,c):console.error("\u672a\u77e5\u5750\u6807\u7cfb\u7c7b\u578b")}transform(a,b,c,d){return this.int.call({command:h.Coord_Transform,coordinates:I(a),src:b,dest:c},d)}}class Ob{constructor(a){this.int=a}start(a){return this.int.call0(h.EditHelper_Start,a)}cancel(a){return this.int.call0(h.EditHelper_Quit,a)}finish(a,b){return this.int.call({command:h.EditHelper_Finish,withOffset:a||!0},b)}setParam(a,b,c,d){return this.int.call({command:h.EditHelper_SetParam,lineType:a,buildType:b,
color:y(c)},d)}}class Pb{constructor(a){this.int=a}setVisibility(a,b){return this.int.call({command:h.InfoTree_SetVisibility,layers:x(a)},b)}show(a,b){return this.int.call({command:h.InfoTree_Show,ids:D(a)},b)}hide(a,b){return this.int.call({command:h.InfoTree_Hide,ids:D(a)},b)}enableXRay(a,b,c){return this.int.call({command:h.InfoTree_EnableXRay,ids:x(a),color:y(b)},c)}disableXRay(a,b){return this.int.call({command:h.InfoTree_DisableXRay,ids:x(a)},b)}showByGroupId(a,b){return this.int.call({command:h.InfoTree_ShowByGroupId,
ids:D(a)},b)}hideByGroupId(a,b){return this.int.call({command:h.InfoTree_HideByGroupId,ids:D(a)},b)}highlightByGroupId(a,b){return this.int.call({command:h.InfoTree_HighlightByGroupId,ids:D(a)},b)}deleteByGroupId(a,b){return this.int.call({command:h.InfoTree_DeleteByGroupId,ids:D(a)},b)}get(a){return this.int.call0(h.InfoTree_Get,a)}focus(a,b){return this.int.call({command:h.InfoTree_Focus,ids:D(a)},b)}getBPFunction(a,b){return this.int.call({command:h.InfoTree_GetProjectTreeBPFunction,ids:x(a)},
b)}callBPFunction(a,b){return this.int.call({command:h.InfoTree_CallProjectTreeBPFunction,data:x(a)},b)}}class Qb{constructor(a){this.int=a;this.apiVersion="6.1";this.apiVersionServer=""}isApiVersionMatched(){let a=this.apiVersionServer,b=this.int.getVersion();return"1.0.0"==this.apiVersionServer||a==b||a&&b&&a.substring(0,a.lastIndexOf("."))==b.substring(0,b.lastIndexOf("."))?!0:!1}addImageButtons(a,b){return this.int.call({command:h.Misc_AddImageButton,data:x(a)},b)}deleteImageButtons(a,b){return this.int.call({command:h.Misc_DeleteImageButton,
ids:D(a)},b)}addAnimatedImageButtons(a,b){return this.int.call({command:h.Misc_AddAnimatedImageButton,data:x(a)},b)}setApiVersionReceived(a){this.int.onApiVersionReceived=a}playVideo(a,b,c,d,e,f,l){return this.int.call({command:h.Misc_PlayVideo,data:[{id:a.toString(),position:[b,c],size:[d,e],url:f}]},l)}stopPlayVideo(a,b){return this.int.call({command:h.Misc_StopPlayVideo,ids:D(a)},b)}playMovie(a,b,c){"function"==typeof b&&(c=b,b=!1);return this.int.call({command:h.Misc_PlayMovie,loop:b,url:a},c)}stopMovie(a){return this.int.call({command:h.Misc_StopMovie},
a)}playVideoAlone(a,b,c){let d=[];d.push(`--URL=${a}`);"object"==typeof b&&(b.hasOwnProperty("mute")&&b.mute&&d.push("--Mute"),b.hasOwnProperty("x")&&-1!=b.x&&d.push(`--Left=${b.x}`),b.hasOwnProperty("y")&&-1!=b.y&&d.push(`--Top=${b.y}`),b.hasOwnProperty("cx")&&0!=b.cx&&d.push(`--Width=${b.cx}`),b.hasOwnProperty("cy")&&0!=b.cy&&d.push(`--Height=${b.cy}`),b.hasOwnProperty("title")&&b.title&&d.push(`--Title="${b.title}"`),b.hasOwnProperty("opacity")&&0!=b.opacity&&d.push(`--Opacity=${b.opacity}`),b.hasOwnProperty("style")&&
d.push(`--FormBorderStyle=${b.style}`),b.hasOwnProperty("hideBuffering")&&b.hideBuffering&&d.push("--HideBuffering"),b.hasOwnProperty("maximizeBox")&&b.maximizeBox&&d.push("--MaximizeBox"),b.hasOwnProperty("notTopmost")&&b.notTopmost&&d.push("--NotTopmost"));a=d.join(" ");return this.startProcess("$VlcPlayer",a,!0,c)}stopPlayVideoAlone(a,b){return this.int.test(2,a,b)}setWindowResolution(a,b,c){return this.int.call({command:h.Misc_SetWindowResolution,cx:a,cy:b,mode:0},c)}callBPFunction(a,b){return this.int.call({command:h.Misc_CallBPFunction,
data:x(a)},b)}enterReportMode(a){return this.int.call0(h.Misc_EnterReportMode)}exitReportMode(a){return this.int.call0(h.Misc_ExitReportMode,a)}showAllFoliages(a){return this.int.call0(h.Misc_ShowAllFoliages,a)}hideAllFoliages(a){return this.int.call0(h.Misc_HideAllFoliages,a)}startProcess(a,b,c,d){"function"==typeof c&&(d=c,c=!0);return this.int.call({command:h.Misc_StartProcess,appName:a,commandLine:b,visible:c},d)}enterMultiViewportMode(a,b,c,d){if(null==b||void 0==b)b="#DEA309";if(null==c||void 0==
c)c=2;return this.int.call({command:h.Misc_EnterMultiViewport,type:a,lineColor:y(b),lineThickness:c},d)}exitMultiViewportMode(a){return this.int.call({command:h.Misc_ExitMultiViewport},a)}setActiveViewport(a,b){return this.int.call({command:h.Misc_SetActivateMultiViewport,viewIndex:x(a)},b)}getActiveViewport(a){return this.int.call({command:h.Misc_GetActivateMultiViewport},a)}setMultiviewportInteractSync(a,b){this.setCameraFollow4Viewport(a,b)}setCameraFollow4Viewport(a,b){return this.int.call({command:h.Misc_UpdateMultiViewport,
moverTogether:a},b)}downloadPakFiles(a,b){return this.int.call({command:h.Misc_downloadPakFiles,ids:x(a)},b)}getConvexPolygon(a,b){return this.int.call({command:h.Misc_ConvexHull2D,data:x(a)},b)}getMaterial(a,b){return this.int.call({command:h.Misc_QueryActorOrMaterial,idOrPaths:x(a)},b)}getBPFunction(a,b){return this.int.call({command:h.Misc_QueryActorOrMaterial,idOrPaths:x(a)},b)}reloadPak(a){return this.int.call({command:h.Misc_ReloadPak},a)}projectAssetCount(a,b){return this.int.call({command:h.Misc_ProjectAssetCount,
type:a},b)}projectAssetCountAll(a){return this.int.call({command:h.Misc_ProjectAssetCount,type:0},a)}switchShortcutKey(a,b){return this.int.call({command:h.Misc_SwitchShortcutKey,"switch":a},b)}getJson(){return this.int.call({command:h.WebUIJSON_Get})}setJson(a){return this.int.call({command:h.WebUIJSON_Set,saveJsonString:a})}startPolygonClip(a,b,c){return this.int.tools.startPolygonClip(a,b,c)}stopClip(a){return this.int.tools.stopClip(a)}playAnimation(a,b){return this.int.camera.playAnimation(a,
b)}stopAnimation(a){return this.int.camera.stopAnimation(a)}setDateTime(a,b,c,d,e,f,l){return this.int.weather.setDateTime(a,b,c,d,e,f,l)}setQueryToolState(a,b){return this.int.call({"c.ommand":h.Settings_SetMousePickMask,mouseClick:a},b)}setCampassVisible(a,b){return this.int.settings.setCampassVisible(a,b)}setMainUIVisibility(a,b){return this.int.settings.setMainUIVisibility(a,b)}setMousePickMask(a,b){return this.int.settings.setMousePickMask(a,b)}}class Rb{constructor(a){this.int=a}setMapMode(a,
b,c){b=b||{};return this.int.call({command:h.Settings_SetMapMode,mode:a,serviceType:b.serviceType||0,serviceProvider:b.serviceProvider||0,coordType:b.coordType||0,mapPoint:b.mapPoint||[0,0],longitude:b.longitude||0,latitude:b.latitude||0,cache:b.cache||":memory:",style:b.style||"mapbox://styles/mapbox/streets-v10",groundHeight:b.groundHeight||0,renderMode:b.renderMode||0,decalMode:b.decalMode||1,serverURL:b.serverURL,coordOrder:b.coordOrder,maxLevel:b.maxLevel},c)}getMapMode(a){return this.int.call0(h.Settings_GetMapMode,
a)}setWMTSLayerVisible(a,b){this.setwmtsLayerVisible(a,b)}setwmtsLayerVisible(a,b){return this.int.call({command:h.Settings_SetWMTSLayerVisible,data:x(a)},b)}setWMTSLayerOpacity(a,b){return this.int.call({command:h.Settings_SetWMTSLayerOpacity,data:x(a)},b)}setMapURL(a,b){return this.int.call({command:h.Settings_SetMapURL,url:a},b)}highlightColor(a,b){return this.setHighlightColor(a,b)}setHighlightColor(a,b){return this.int.call({command:h.Settings_SetHighlightColor,color:y(a)},b)}setFovX(a,b){return this.int.call({command:h.Settings_SetFovX,
value:a},b)}setOceanColor(a,b){return this.int.call({command:h.Settings_SetOceanColor,color:y(a)},b)}setEnableInteract(a,b){if(this.int.player)this.int.player.setEnableInteract(a);else return this.int.call({command:h.Settings_SetEnableInteract,enableInteract:a},b)}setInteractiveMode(a,b){return this.int.call({command:h.Settings_SetInteractiveMode,mode:a},b)}setCharacterRoaming(a,b,c,d){return this.int.call({command:h.Settings_SetInteractiveMode,mode:1,location:a,rotation:b,targetArmLength:c},d)}getInteractiveMode(a){return this.int.call({command:h.Settings_GetInteractiveMode},
a)}setCampassVisible(a,b){return this.int.call({command:h.Settings_SetCampassVisible,visible:a},b)}setCampassPosition(a,b,c){return this.int.call({command:h.Settings_SetCampassPosition,position:[a,b]},c)}setMainUIVisibility(a,b){return this.int.call({command:h.Settings_SetMainUIVisibility,visible:a},b)}setMousePickMask(a,b){let c=!1,d=!1,e=!1;a&aa.MouseClick&&(c=!0);a&aa.MouseMove&&(d=!0);a&aa.MouseHover&&(e=!0);return this.int.call({command:h.Settings_SetMousePickMask,mouseClick:c,mouseMove:d,mouseHover:e},
b)}setTerrainAlpha(a,b){return this.int.call({command:h.Settings_SetTerrainAlpha,alpha:a},b)}setEnableCameraMovingEvent(a,b,c){K(b)&&(b=20);return this.int.call({command:h.Settings_EnableCameraMovingEvent,bEnable:a,monitorThreshold:b},c)}setLabelLayer(a,b){return this.int.call({command:h.VTPKService_Set,vtpk:a},b)}getLabelLayer(a){return this.int.call({command:h.VTPKService_Get},a)}removeLabelLayer(a){return this.int.call({command:h.VTPKService_Set,vtpk:""},a)}setRenderedCursorVisible(a,b){this.setCursorAutoSync(a,
b)}setCursorAutoSync(a,b){return this.int.call({command:h.Settings_CursorAutoSync,useSoftwareCursor:a},b)}setScreenControlsVisible(a,b){return this.int.call({command:h.Settings_SetGameBoardVisible,visible:a},b)}getProjectWKT(a){return this.int.call({command:h.Settings_GetWKT},a)}setCharacterRotation(a,b){return this.int.call({command:h.Settings_SetCharacterRotation,rotation:a},b)}setGroundHeight(a,b){this.setGlobalHeight(a,b)}setGlobalHeight(a,b){return this.int.call({command:h.Settings_Update,height:a},
b)}setLabelLayerScale(a,b){return this.int.call({command:h.Settings_Update,symbolSize:a},b)}setImageLayerEnableDecal(a,b){return this.int.call({command:h.Settings_Update,receiveDecalMode:a},b)}setMainPanelPos(a,b,c){return this.int.call({command:h.Settings_SetMainPanelPos,padding:[a,b]},c)}setToolbarVisible(a,b){return this.int.call({command:h.Settings_SetToolbarVisible,visible:a},b)}showPropertiesPanel(a,b){return this.int.call({command:h.Settings_ShowPropertiesPanelById,id:a},b)}hidePropertiesPanel(a){return this.int.call({command:h.Settings_HidePropertiesPanel},
a)}setPropertiesPanelPos(a,b,c){return this.int.call({command:h.Settings_SetPropertiesPanelPos,position:[a,b]},c)}}class Sb{constructor(a){this.int=a}setReportMode(a,b,c,d){return this.int.call({command:h.Settings_SetReport,alignment:a,playMode:b,moveInOtherView:c},d)}getReportMode(a){return this.int.call({command:h.Settings_GetReport},a)}setControlMode(a,b,c,d,e){return this.int.call({command:h.Settings_SetControl,speed:a,yawSpeed:b,rotateSelf:c,useFemale:d},e)}getControlMode(a){return this.int.call({command:h.Settings_GetControl},
a)}setPostProcessMode(a,b){let c=1E3,d=1E3;0<=a.terrainGlobalAlpha&&1>=a.terrainGlobalAlpha&&(c=1E3*a.terrainGlobalAlpha);0<=a.osgbGlobalAlpha&&1>=a.osgbGlobalAlpha&&(d=1E3*a.osgbGlobalAlpha);return this.int.call({command:h.Settings_SetPostProcess,globalIllumination:a.globalIllumination,chromaticAberration:a.chromaticAberration,ambientRadius:a.ambientRadius,ambientFadeDistance:a.ambientFadeDistance,exposureEnabled:a.exposureEnabled,exposureCompensation:a.exposureCompensation,depthFiethSwitch:a.depthFiethSwitch,
focalLength:a.focalLength,aperture:a.aperture,deepBlur:a.deepBlur,contrast:a.contrast,saturation:a.saturation,lensFlareIntensity:a.lensFlareIntensity,ambientIntensity:a.ambientIntensity,bloomIntensity:a.bloomIntensity,lutMode:a.lutMode,lutIntensity:a.lutIntensity,darkCorner:a.darkCorner,screenPercentage:a.screenPercentage,terrainGlobalAlpha:c,terrainGlobalLitStatus:a.terrainGlobalLitStatus,osgbGlobalLitStatus:a.osgbGlobalLitStatus,osgbGlobalAlpha:d,antiAliasing:a.antiAliasing,tonemapper:a.tonemapper,
postProcessEffects:a.postProcessEffects,dofMode:a.dofMode,wireThickness:a.wireThickness,receiveDecalMode:a.receiveDecalMode},b)}getPostProcessMode(a){return this.int.call({command:h.Settings_GetPostProcess},a)}setCameraMode(a,b,c,d,e){d&&"function"==typeof d&&(e=d);return this.int.call({command:h.Settings_SetCamera,nearClipPlane:a,fovH:b,minCamHeight:c,maxCamHeight:d},e)}getCameraMode(a){return this.int.call({command:h.Settings_GetCamera},a)}setMapMode(a,b,c){b=b||{};return this.int.call({command:h.Settings_SetMapMode,
mode:a,serviceType:b.serviceType||0,serviceProvider:b.serviceProvider||0,coordType:b.coordType||0,mapPoint:b.mapPoint||[0,0],longitude:b.longitude||0,latitude:b.latitude||0,cache:b.cache||":memory:",style:b.style||"mapbox://styles/mapbox/streets-v10",groundHeight:b.groundHeight||0,renderMode:b.renderMode||0,decalMode:b.decalMode||1,serverURL:b.serverURL,coordOrder:b.coordOrder,maxLevel:b.maxLevel},c)}getMapMode(a){return this.int.call0(h.Settings_GetMapMode,a)}setPakFile(a,b){return this.int.call({command:h.SettingsPanel_setPak,
bFolder:!1,pakFile:x(a)},b)}setPakFolder(a,b){return this.int.call({command:h.SettingsPanel_setPak,bFolder:!0,pakFolder:x(a)},b)}}class Tb{constructor(a){this.int=a}startPolygonClip(a,b,c){return this.int.call({command:h.Tools_StartPolygonClip,data:x({id:"0",coordinates:E(a),toggleImageCut:b})},c)}startPlaneClip(a,b,c,d,e){return this.int.call({command:h.Tools_StartPlaneClip,location:a,rotation:b,isShowPlane:c,isEdit:d},e)}startVolumeClip(a,b,c,d,e,f){return this.int.call({command:h.Tools_StartVolumeClip,
bbox:a,rotation:e,value:b,isShowPlane:c,isEdit:d},f)}updateVolumeClip(a,b,c,d,e,f){return this.int.call({command:h.Tools_UpdateVolumeClip,bbox:a,rotation:e,value:b,isShowPlane:c,isEdit:d},f)}stopClip(a){return this.stopPolygonClip(a)}stopPolygonClip(a){return this.int.call0(h.Tools_StopClip,a)}stopPlaneClip(a){return this.int.call({command:h.Tools_StopPlaneClip},a)}stopVolumeClip(a){return this.int.call({command:h.Tools_StopVolumeClip},a)}setMeasurement(a,b,c){b=b||{};return this.int.call({command:h.Tools_SetMeasurement,
type:a,pointSize:b.pointSize,textSize:b.textSize,textColor:y(b.textColor),pointColor:y(b.pointColor),lineColor:y(b.lineColor),areaColor:y(b.areaColor),showCoordinateText:b.showCoordinateText},c)}startMeasurement(a){return this.int.call0(h.Tools_StartMeasurement,a)}stopMeasurement(a){return this.int.call0(h.Tools_StopMeasurement,a)}lineIntersect(a,b,c){return this.int.call({command:h.Tools_LineIntersect,data:[{start:a,end:b}],highPrecision:!1,returnDetails:!0},c)}linesIntersect(a,b,c,d){return this.int.call({command:h.Tools_LineIntersect,
data:x(a),highPrecision:b,returnDetails:c},d)}startGeometryEdit(a,b,c){return this.int.call({command:h.Tools_StartGeometryEdit,id:a,type:b},c)}stopGeometryEdit(a){return this.int.call0(h.Tools_StopGeometryEdit,a)}startSkylineAnalysis(a,b){a=a||{};a.outlineColor&&(a.outlineColor=y(a.outlineColor));a.sceneColor&&(a.sceneColor=y(a.sceneColor));a.skylineColor&&(a.skylineColor=y(a.skylineColor));a.backgroundColor&&(a.backgroundColor=y(a.backgroundColor));if(a.tileLayers){a.tileLayers=x(a.tileLayers);for(let c of a.tileLayers)c.hasOwnProperty("color")&&
(c.color=y(c.color))}a.command=h.Tools_StartSkylineAnalysis;return this.int.call(a,b)}stopSkylineAnalysis(a){return this.int.call0(h.Tools_StopSkylineAnalysis,a)}exportSkyline(a,b,c,d){c=c||{};c.skylineColor&&(c.skylineColor=y(c.skylineColor));c.backgroundColor&&(c.backgroundColor=y(c.backgroundColor));if(c.tileLayers){c.tileLayers=x(c.tileLayers);for(let e of c.tileLayer)e.hasOwnProperty("color")&&(e.color=y(e.color))}return this.int.call({command:h.Tools_ExportSkyline,path:a,size:b,skylineColor:c.skylineColor,
backgroundColor:c.backgroundColor},d)}startViewshedAnalysis(a,b){a=a||{};a.visibleColor&&(a.visibleColor=y(a.visibleColor));a.invisibleColor&&(a.invisibleColor=y(a.invisibleColor));a.command=h.Tools_StartViewshedAnalysis;return this.int.call(a,b)}stopViewshedAnalysis(a){return this.int.call0(h.Tools_StopViewshedAnalysis,a)}startVisiblityAnalysis(a,b){a=a||{};a.visibleColor&&(a.visibleColor=y(a.visibleColor));a.invisibleColor&&(a.invisibleColor=y(a.invisibleColor));a.command=h.Tools_StartVisiblityAnalysis;
return this.int.call(a,b)}stopVisiblityAnalysis(a){return this.int.call0(h.Tools_StopVisiblityAnalysis,a)}startViewDomeAnalysis(a,b){a=a||{};a.visibleColor&&(a.visibleColor=y(a.visibleColor));a.invisibleColor&&(a.invisibleColor=y(a.invisibleColor));a.command=h.Tools_StartViewDomeAnalysis;return this.int.call(a,b)}stopViewDomeAnalysis(a){return this.int.call0(h.Tools_StopViewDomeAnalysis,a)}startCutFillAnalysis(a,b){a=a||{};a.cutLineColor&&(a.cutLineColor=y(a.cutLineColor));a.fillLineColor&&(a.fillLineColor=
y(a.fillLineColor));a.cutPointColor&&(a.cutPointColor=y(a.cutPointColor));a.fillPointColor&&(a.fillPointColor=y(a.fillPointColor));a.gridColor&&(a.gridColor=y(a.gridColor));a.command=h.Tools_StartCutFillAnalysis;return this.int.call(a,b)}stopCutFillAnalysis(a){return this.int.call0(h.Tools_StopCutFillAnalysis,a)}startSunshineAnalysis(a,b){var c=/^(0?[0-9]|1[0-9]|[2][0-3]):(0?[0-9]|[1-5][0-9])$/;if(null==a||void 0==a)console.error("\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a");else{let d=a.startTime,e=a.endTime;
if(null==d||""==d||null==e||""==e)console.error("\u65f6\u95f4\u53c2\u6570\u683c\u5f0f\u4e0d\u6b63\u786e");else if(d.match(c)&&e.match(c))if(d.split(":"))if(a.startHour=parseInt(d.split(":")[0]),a.startMinute=parseInt(d.split(":")[1]),e.split(":"))if(a.endHour=parseInt(e.split(":")[0]),a.endMinute=parseInt(e.split(":")[1]),a.startHour>a.endHour)console.error("\u5f00\u59cb\u65f6\u95f4\u4e0d\u80fd\u5927\u4e8e\u7ed3\u675f\u65f6\u95f4");else{if(a.startHour==a.endHour){if(a.startMinute==a.endMinute){console.error("\u5f00\u59cb\u65f6\u95f4\u4e0d\u80fd\u7b49\u4e8e\u7ed3\u675f\u65f6\u95f4");
return}if(a.startMinute>a.endMinute){console.error("\u5f00\u59cb\u65f6\u95f4\u4e0d\u80fd\u5927\u4e8e\u7ed3\u675f\u65f6\u95f4");return}}!K(a.groundElevation)&&ja(a.groundElevation)&&(a.undersideHeight=a.groundElevation,delete a.groundElevation);a.command=h.Tools_StartSunshineAnalysis;return this.int.call(a,b)}else console.error("\u53c2\u6570endTime\u65f6\u95f4\u683c\u5f0f\u4e0d\u6b63\u786e");else console.error("\u53c2\u6570startTime\u65f6\u95f4\u683c\u5f0f\u4e0d\u6b63\u786e");else console.error("\u65f6\u95f4\u53c2\u6570\u683c\u5f0f\u4e0d\u6b63\u786e")}}stopSunshineAnalysis(a){return this.int.call0(h.Tools_StopSunshineAnalysis,
a)}startTerrainSlopeAnalysis(a,b){a.command=h.Tools_StartTerrainSlopeAnalysis;a.arrowColor=y(a.arrowColor);return this.int.call(a,b)}stopTerrainSlopeAnalysis(a){return this.int.call0(h.Tools_StopTerrainSlopeAnalysis,a)}startContourLineAnalysis(a,b){a.command=h.Tools_StartContourLineAnalysis;a.contourLineColor=y(a.contourLineColor);return this.int.call(a,b)}stopContourLineAnalysis(a){return this.int.call0(h.Tools_StopContourLineAnalysis,a)}startFloodFill(a,b){a=a||{};a.color&&(a.color=y(a.color));
a.command=h.Tools_StartFloodFill;return this.int.call(a,b)}stopFloodFill(a){return this.int.call0(h.Tools_StopFloodFill,a)}replaceTextureByVideo(a,b,c){return this.int.call({command:h.Tools_ReplaceTexture,texturePackage:a,newTexture:b,type:1},c)}replaceTextureByImage(a,b,c){return this.int.call({command:h.Tools_ReplaceTexture,texturePackage:a,newTexture:b,type:2},c)}replaceTextureByUrl(a,b,c){return this.int.call({command:h.Tools_ReplaceTexture,texturePackage:a,newTexture:b,type:3},c)}restoreTexture(a,
b){return this.int.call({command:h.Tools_RestoreTexture,ids:x(a)},b)}showPanel(a,b,c){this.showAnalysisPanel(a,b,c)}hidePanel(a){this.hideAnalysisPanel(a)}showAnalysisPanel(a,b,c){return this.int.call({command:h.Tools_AnalysisPopupAttributes,type:a,position:b},c)}hideAnalysisPanel(a){return this.int.call({command:h.Tools_AnalysisCloseAttributes},a)}showUIPanel(a,b,c){return this.int.call({command:h.Tools_FunctionNavBar,type:a,position:b},c)}hideUIPanel(a,b){return this.int.call({command:h.Tools_HideFunctionNavBar,
type:a},b)}getUIPanel(a,b){return this.int.call({command:h.Tools_GetFunctionNavBar,type:a},b)}}class Ub{constructor(a){this.int=a}getParams(a){return this.int.call0(h.Weather_GetParams,a)}_setParam(a,b){return this.int.call({command:h.Weather_SetParams,data:a},b)}setDateTime(a,b,c,d,e,f,l){return this.int.call({command:h.Weather_SetDate,year:a,month:b,day:c,hour:d,minute:e,daynightLoop:f},l)}getDateTime(a){return this.int.call0(h.Weather_GetDate,a)}simulateTime(a,b,c,d){let e=0,f=0,l=0,g=0;a instanceof
Array?(0<a.length&&(e=a[0]),1<a.length&&(f=a[1])):e=parseInt(a);b instanceof Array?(0<b.length&&(l=b[0]),1<b.length&&(g=b[1])):l=parseInt(b);return this.int.call({command:h.Weather_SimulateTime,startHour:e,startMinute:f,endHour:l,endMinute:g,duration:c},d)}setCloudThickness(a,b){return this.int.call({command:h.Weather_SetParams,lowCloudHeight:a},b)}setCloudDensity(a,b){return this.int.call({command:h.Weather_SetParams,lowCloudDensity:a},b)}setCloudHeight(a,b){return this.int.call({command:h.Weather_SetParams,
cloudHeight:a},b)}setRainParam(a,b,c,d,e,f,l){d&&"function"==typeof d&&(l=d);return this.int.call({command:h.Weather_SetParams,rainSnow:1,rainSnowStrength:a,rainSnowSpeed:b,raindropSize:c,rainSnowSize:c||null,rainSnowCamMotionAlignmentScale:e||null,rainSnowColor:d||null,rainSnowOvercastStrength:f||null},l)}setSnowParam(a,b,c,d,e,f,l){d&&"function"==typeof d&&(l=d);return this.int.call({command:h.Weather_SetParams,rainSnow:2,rainSnowStrength:a,rainSnowSpeed:b,snowflakeSize:c,rainSnowSize:c||null,rainSnowCamMotionAlignmentScale:e||
null,rainSnowColor:d||null,rainSnowOvercastStrength:f||null},l)}disableRainSnow(a){return this.int.call({command:h.Weather_SetParams,rainSnow:0},a)}setFogParam(a,b,c,d,e,f){return this.int.call({command:h.Weather_SetParams,fogDensity:a,fogColor:b,fogHeightFalloff:c,fogStartDistance:d,fogOpacity:e},f)}setSunIntensity(a,b){return this.int.call({command:h.Weather_SetParams,sunIntensity:a},b)}setMoonIntensity(a,b){return this.int.call({command:h.Weather_SetParams,moonIntensity:a},b)}setAmbientLightIntensity(a,
b){return this.int.call({command:h.Weather_SetParams,ambientLightIntensity:a},b)}setTemperature(a,b){return this.int.call({command:h.Weather_SetParams,temperature:a},b)}setShadowIntensity(a,b){return this.int.call({command:h.Weather_SetParams,shadowIntensity:a},b)}setShadowQuality(a,b){return this.int.call({command:h.Weather_SetParams,shadowQuality:a},b)}setShadowDistance(a,b){return this.int.call({command:h.Weather_SetParams,shadowDistance:a},b)}setDarkMode(a,b){return this.int.call({command:h.Weather_SetParams,
darkMode:!!a},b)}setSunSize(a,b){return this.int.call({command:h.Weather_SetParams,sunSize:a},b)}setMoonSize(a,b){return this.int.call({command:h.Weather_SetParams,moonSize:a},b)}setSkyVisibleMaxHeight(a,b){return this.int.call({command:h.Weather_SetParams,atmosphereVisibleHeight:a||1E5},b)}setCloudParam(a,b,c,d){return this.int.call({command:h.Weather_SetParams,cloudsColor:y(a),cloudsAltitude:b,cloudShadowStrength:c},d)}setLowCloud(a,b,c,d,e,f){return this.int.call({command:h.Weather_SetParams,lowCloudCoverage:a,
lowCloudDensity:b,lowCloudHeight:c,lowCloudWindSpeed:d,lowCloudWindDirection:e},f)}setHighCloud(a,b,c,d,e,f,l){return this.int.call({command:h.Weather_SetParams,highCloudLayerCoverage:a,highCloudWindSpeed:b,highCloudWindDirection:c,cirrusCloudDensity:d,cirrostratusCloudDensity:e,cirrocumulusCloudDensity:f},l)}}var ha=null;class ca{constructor(a,b){a||(ha=this);this.isReady=!1;this.options=b||{};(this.player=this.options.player)||B.setLanguage();"function"==typeof ca.__onApiConstructed&&ca.__onApiConstructed(this);
if(this.isCalledInCEF=ba.valid())this.isReady=this.isConnected=!0;this.callbackMap=new Map;this.websocket=null;this.callbackIndex=0;this.tickMarkerId="___tick_delegate_marker___";!this.player&&a&&(this.url=`ws://${a}`,this.connectWebSocket());this.apiQueue=new Sa(c=>this.sendApi(c))}getPlayer(){return this.player}setHost(a,b){this.url=`ws://${a}:${b}`}connectWebSocket(){if(!this.player){this.log(`Connecting: ${this.url}`);if("undefined"==typeof window)this.websocket=new (require("ws"))(this.url);
else{if(!("WebSocket"in window)){console.error("Not Support WebSocket!");return}this.websocket=new WebSocket(this.url)}this.websocket.onopen=()=>this.onConnectionOpen();this.websocket.onmessage=a=>this.onConnectionMessage(a.data);this.websocket.onclose=a=>this.onConnectionClose(a);this.websocket.onerror=a=>this.onConnectionError(a)}}setEventCallback(a){this.options.onEvent=a}destroy(){this.isDestroyed=!0;this.player&&this.player.destroy();this.websocket&&(this.websocket.close(),this.websocket=null)}reset(a,
b){return this.call({command:h.Reset,resetType:a||na.ClearObjects},b)}saveProject(a){return this.call0(h.SaveProject,a)}registerTick(a,b,c){b=b||{};"__execute__"!=a&&(b.func="tick");this.call({command:h.RegisterJsCommunication,id:this.tickMarkerId,url:a,func:b.func,x:b.x||4,y:b.y||4,width:b.width||400,height:b.height||300,visible:b.visible||!1},c)}removeTick(a){this.call({command:h.UnRegisterJsCommunication,ids:[this.tickMarkerId]},a)}showTickWindow(a,b){this.isCalledInCEF?ba.showTickWindow(a):a?
this.registerTick("__show__",{},b):this.registerTick("",{},b)}executeJsInTickPage(a,b){this.registerTick("__execute__",{func:a},b)}getVersion(){return"6.1.0507.20026"}checkApiReady(){if(!this.isReady)throw console.error("The interface call is not ready!"),"The interface call is not ready!";return!0}setEnableAliases(){this.ct=this.cameraTour;this.tl=this.tileLayer;this.ctag=this.customTag;this.hdm2=this.hydrodynamicModel2;this.hdm=this.hydrodynamicModel;this.fe=this.finiteElement;this.line=this.polyline;
this.ol=this.odline;this.p3d=this.polygon3d;this.hm=this.heatmap;this.hm3d=this.heatmap3d;this.co=this.customObject;this.ha=this.highlightArea;this.vp=this.videoProjection;this.dw=this.dynamicWater;this.ff=this.floodFill;this.c3d=this.cesium3DTileset;this.shapeFile=this.shp=this.shapeFileLayer;this.ata=this.antenna;this.sw=this.signalWave;this.imagery=this.imageryLayer;this.geoJSON=this.geoJSONLayer;this.vc=this.vehicle;this.eh=this.editHelper;this.sp=this.settingsPanel}getProjectInfo(a){return this.call0(h.GetProjectInfo,
a)}get camera(){if(!this.checkApiReady())return null;this._camera||(this._camera=new Mb(this));return this._camera}get coord(){if(!this.checkApiReady())return null;this._coord||(this._coord=new Nb(this));return this._coord}get infoTree(){if(!this.checkApiReady())return null;this._infoTree||(this._infoTree=new Pb(this));return this._infoTree}get cameraTour(){if(!this.checkApiReady())return null;this._cameraTour||(this._cameraTour=new Ua(this));return this._cameraTour}get tileLayer(){if(!this.checkApiReady())return null;
this._tileLayer||(this._tileLayer=new Db(this));return this._tileLayer}get tag(){if(!this.checkApiReady())return null;this._tag||(this._tag=new Bb(this));return this._tag}get marker(){if(!this.checkApiReady())return null;this._marker||(this._marker=new ob(this));return this._marker}get marker3d(){if(!this.checkApiReady())return null;this._marker3d||(this._marker3d=new pb(this));return this._marker3d}get customTag(){if(!this.checkApiReady())return null;this._customTag||(this._customTag=new Ya(this));
return this._customTag}get radiationPoint(){if(!this.checkApiReady())return null;this._radiationPoint||(this._radiationPoint=new wb(this));return this._radiationPoint}get customMesh(){if(!this.checkApiReady())return null;this._customMesh||(this._customMesh=new Wa(this));return this._customMesh}get waterMesh(){if(!this.checkApiReady())return null;this._waterMesh||(this._waterMesh=new Jb(this));return this._waterMesh}get waterFlowField(){if(!this.checkApiReady())return null;this._waterFlowField||(this._waterFlowField=
new Ib(this));return this._waterFlowField}get vectorField(){if(!this.checkApiReady())return null;this._vectorField||(this._vectorField=new Eb(this));return this._vectorField}get hydrodynamicModel2(){if(!this.checkApiReady())return null;this._hydrodynamicModel2||(this._hydrodynamicModel2=new hb(this));return this._hydrodynamicModel2}get hydrodynamicModel(){if(!this.checkApiReady())return null;this._hydrodynamicModel||(this._hydrodynamicModel=new lb(this));return this._hydrodynamicModel}get finiteElement(){if(!this.checkApiReady())return null;
this._finiteElement||(this._finiteElement=new ab(this));return this._finiteElement}get fluid(){if(!this.checkApiReady())return null;this._fluid||(this._fluid=new cb(this));return this._fluid}get polyline(){if(!this.checkApiReady())return null;this._polyline||(this._polyline=new vb(this));return this._polyline}get odline(){if(!this.checkApiReady())return null;this._odline||(this._odline=new rb(this));return this._odline}get polygon3d(){if(!this.checkApiReady())return null;this._polygon3d||(this._polygon3d=
new ub(this));return this._polygon3d}get polygon(){if(!this.checkApiReady())return null;this._polygon||(this._polygon=new tb(this));return this._polygon}get heatmap(){if(!this.checkApiReady())return null;this._heatmap||(this._heatmap=new eb(this));return this._heatmap}get heatmap3d(){if(!this.checkApiReady())return null;this._heatmap3d||(this._heatmap3d=new fb(this));return this._heatmap3d}get beam(){if(!this.checkApiReady())return null;this._beam||(this._beam=new Ta(this));return this._beam}get highlightArea(){if(!this.checkApiReady())return null;
this._highlightArea||(this._highlightArea=new gb(this));return this._highlightArea}get customObject(){if(!this.checkApiReady())return null;this._customObject||(this._customObject=new Xa(this));return this._customObject}get videoProjection(){if(!this.checkApiReady())return null;this._videoProjection||(this._videoProjection=new Hb(this));return this._videoProjection}get panorama(){if(!this.checkApiReady())return null;this._panorama||(this._panorama=new sb(this));return this._panorama}get decal(){if(!this.checkApiReady())return null;
this._decal||(this._decal=new Za(this));return this._decal}get dynamicWater(){if(!this.checkApiReady())return null;this._dynamicWater||(this._dynamicWater=new $a(this));return this._dynamicWater}get floodFill(){if(!this.checkApiReady())return null;this._floodFill||(this._floodFill=new bb(this));return this._floodFill}get cesium3DTileset(){if(!this.checkApiReady())return null;this._cesium3DTileset||(this._cesium3DTileset=new Va(this));return this._cesium3DTileset}get shapeFileLayer(){if(!this.checkApiReady())return null;
this._shapeFileLayer||(this._shapeFileLayer=new yb(this));return this._shapeFileLayer}get light(){if(!this.checkApiReady())return null;this._light||(this._light=new nb(this));return this._light}get antenna(){if(!this.checkApiReady())return null;this._antenna||(this._antenna=new ib(this));return this._antenna}get signalWave(){if(!this.checkApiReady())return null;this._signalWave||(this._signalWave=new zb(this));return this._signalWave}get river(){if(!this.checkApiReady())return null;this._river||(this._river=
new xb(this));return this._river}get hydrodynamic1d(){if(!this.checkApiReady())return null;this._hydrodynamic1d||(this._hydrodynamic1d=new jb(this));return this._hydrodynamic1d}get hydrodynamic2d(){if(!this.checkApiReady())return null;this._hydrodynamic2d||(this._hydrodynamic2d=new kb(this));return this._hydrodynamic2d}get imageryLayer(){if(!this.checkApiReady())return null;this._imageryLayer||(this._imageryLayer=new mb(this));return this._imageryLayer}get geoJSONLayer(){if(!this.checkApiReady())return null;
this._geoJSONLayer||(this._geoJSONLayer=new db(this));return this._geoJSONLayer}get vehicle(){if(!this.checkApiReady())return null;this._vehicle||(this._vehicle=new Fb(this));return this._vehicle}get vehicle2(){if(!this.checkApiReady())return null;this._vehicle2||(this._vehicle2=new Gb(this));return this._vehicle2}get markerLayer(){if(!this.checkApiReady())return null;this._markerLayer||(this._markerLayer=new qb(this));return this._markerLayer}get splineMesh(){if(!this.checkApiReady())return null;
this._splineMesh||(this._splineMesh=new Ab(this));return this._splineMesh}get misc(){if(!this.checkApiReady())return null;this._misc||(this._misc=new Qb(this));return this._misc}get tools(){if(!this.checkApiReady())return null;this._tools||(this._tools=new Tb(this));return this._tools}get settings(){if(!this.checkApiReady())return null;this._settings||(this._settings=new Rb(this));return this._settings}get weather(){if(!this.checkApiReady())return null;this._weather||(this._weather=new Ub(this));
return this._weather}get editHelper(){if(!this.checkApiReady())return null;this._editHelper||(this._editHelper=new Ob(this));return this._editHelper}get settingsPanel(){if(!this.checkApiReady())return null;this._settingsPanel||(this._settingsPanel=new Sb(this));return this._settingsPanel}_getCallbackIndex(){return++this.callbackIndex}call0(a,b){return this.call({command:a},b)}call(a,b){if(!this.isConnected)return this.logWithColor("red","Not connected!"),b?void 0:Promise.reject("Not connected!");
if(void 0==a.command||a.command==h.None)return this.logWithColor("red","command is undefined or None"),b?void 0:Promise.reject("command is undefined or None");var c=a.command;let d={__command:h[c]||"Unknown"};for(let f in a)"command"!=f&&(d[f]=a[f]);a=d;a.timestamp=Date.now();a.callbackIndex=this._getCallbackIndex();a.__version=this.getVersion();null===b&&(a.__noResponse=!0);a.command=c;let e=a.command+"_"+a.callbackIndex;b&&"function"==typeof b&&(this.callbackMap[e]=b);if(this.isCalledInCEF&&null===
b)return delete a.__noResponse,this.callbackMap[e]=null,this.sendApi(a),a;c=JSON.stringify(a);this.log("");this.logWithColor("RoyalBlue",`Request: ${h[a.command]||"Unknown"}`,!0);this.logWithColor("green","\uff08"+(new Date(a.timestamp)).toLocaleTimeString()+"\uff09");this.logWithColor("gray",`${c}`);if(b||null===b)this.apiQueue.push(a);else return new Promise(f=>{this.callbackMap[e]=f;this.apiQueue.push(a)})}sendApi(a){this.isCalledInCEF?ba.execute(JSON.stringify(a)):this.player?this.player.sendApi(a):
this.sendStringByWS(a)}sendApiToCS(a,b){if(!this.player)return this.logWithColor("red","This method can only be called in the Cloud environment!"),Promise.reject("This method can only be called in the Cloud environment!");a.timestamp=Date.now();a.callbackIndex=this._getCallbackIndex();let c=JSON.stringify(a);this.log("");this.logWithColor("RoyalBlue",`Request: ${a.command}`,!0);this.logWithColor("green","\uff08"+(new Date(a.timestamp)).toLocaleTimeString()+"\uff09");this.logWithColor("gray",`${c}`);
let d=a.command+"_"+a.callbackIndex;if(b&&"function"==typeof b)this.callbackMap[d]=b,this.player.sendApi(a,!0);else return new Promise(e=>{this.callbackMap[d]=e;this.player.sendApi(a,!0)})}sendStringByWS(a){if(a){a=JSON.stringify(a);var b=a.length;if(16777216>=b)this.websocket.send(a);else{var c=Math.ceil(b/16777216),d=[];for(let e=0;e<c;e++)d[e]=(0==e?"__MI_SECTION_START__":e==c-1?"__MI_SECTION_END__":"__MI_SECTION__")+a.substr(16777216*e,e==c-1?b-16777216*e:16777216);for(let e of d)this.websocket.send(e)}}}viewHome(a){switch(a){case 0:this.savedCamera?
this.camera.set(this.savedCamera,0):this.initialCameraPosition&&this.camera.set(this.initialCameraPosition,0);break;case 1:this.savedCamera=null;this.viewHome(0);break;case 2:this.camera.get(b=>this.savedCamera=b.camera)}}onReady(){this.isReady=!0;if("function"==typeof this.options.onReady)this.options.onReady();this.call({command:h.Misc_GetVersion});if(this.player)this.player.onApiReady()}log(a,b,c){if("function"==typeof this.options.onLog)this.options.onLog(a,b,c)}logWithColor(a,b,c){this.log(b,
c,a)}onConnectionOpen(){this.isConnected=!0;this.logWithColor("blue","Connected!")}onConnectionClose(a){this.isConnected=!1;this.logWithColor("red",`Connection closed! code: ${a.code||"-"}, reason: ${1006==a.code?B.getString("Disconnect"):a.reason||a.message||"-"}`);this.log("");this.logWithColor("SpringGreen","Reconnecting...");this.isDestroyed||this.player||this.connectWebSocket()}onConnectionError(a){console.error("WebSocket error observed")}onConnectionMessage(a,b){a=a.replace(/~!@~!@~!@/g,'\\"');
this.log("");let c=null;try{c=JSON.parse(a)}catch(e){this.isCalledInCEF?(document.writeln(e.message),document.writeln("<hr>"),document.writeln(a)):(console.error(e.message),this.log("Response: [Unknown]"));return}if(c.command==h.Misc_GetVersion){this.misc.apiVersionServer=c.version;if(!this.misc.isApiVersionMatched()){var d=B.getString("VersionMismatch").format(this.getVersion(),this.misc.apiVersionServer);console.warn(d);"undefined"!=typeof window&&alert(d)}if("function"==typeof this.options.onApiVersion)this.options.onApiVersion(this.misc.apiVersionServer)}if(c.command){"string"==
typeof c.command&&"undefined"==typeof c.commandIndex?d=c.command:(this.apiQueue.callNext(c.callbackIndex),d=h[c.command]||"Unknown");let e=Date.now()-c.timestamp;this.logWithColor("RoyalBlue",`Response: ${d}`,!0);this.logWithColor("green",` (${B.getString("TimeConsuming")}${e}ms)`);this.logWithColor("gray",`${B.getString("RequestTime")}${(new Date(c.timestamp)).toLocaleTimeString()}`,!0);this.logWithColor("gray",`  ${B.getString("ResponseTime")}${(new Date(Date.now())).toLocaleTimeString()}`,!0);
this.logWithColor("gray",`  ${B.getString("MessageLength")}${a.length}`);a=c.command+"_"+c.callbackIndex;(d=this.callbackMap[a])?(d(c),delete this.callbackMap[a]):null===d&&"function"===typeof tick_next&&tick_next(c,b)}else if(c.eventtype)if(this.log("Response: Event"),"CompleteInitialization"==c.eventtype)this.log("The initialization is complete, now you can call the interfaces in onReady callback function."),this.initialCameraPosition=c.InitialCamera,c.ResourcesPath&&(this.resourcesPath=c.ResourcesPath,
this.log("ResourcesPath:"+this.resourcesPath)),this.onReady();else if("NodeConfiguration"==c.eventtype)this.resourcesPath=c.ResourcesPath,this.log("ResourcesPath:"+this.resourcesPath);else if("MainThreadBusy"==c.eventtype)this.apiQueue.onMainThreadBusy(c),this.player&&this.player.onMainThreadBusy(c);else{if("function"==typeof this.options.onEvent)this.options.onEvent(c)}else this.log("Response: [Unknown]");b=JSON.stringify(c,(e,f)=>f instanceof Array?JSON.stringify(f):f,"\t").replace(/"\[/g,"[").replace(/\]"/g,
"]").replace(/\\"/g,'"').replace(/""/g,'"');this.logWithColor("gray",b)}quit(){return this.call0(h.Quit)}test(a,b,c){return this.call({command:h.SimulateTest__,type:a,int32Val:b},c)}}const M={WS_Disconnected:0,WS_Connecting:1,WS_Connected:2,RTC_Opened:3,Video_LoadedMetaData:4,OnReady:5},Q={StartingProcess:0,CheckingBusy:1,ConfirmBusy:2,ProcessStartFailed:3,ProcessStarted:4,LoadingProject:5,ProjectLoaded:6,UserAccessDenied:7,CheckingLicense:8};class Vb{constructor(){"undefined"!=typeof navigator&&
(this.isUnix="Mac68K"==navigator.platform||"MacPPC"==navigator.platform||"Macintosh"==navigator.platform||"MacIntel"==navigator.platform||-1!=navigator.platform.indexOf("Linux"),this.isChrome=-1!=navigator.userAgent.indexOf("Chrome"),this.isMobileDevice=/Android|Linux|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent),this.isIOSDevice=/iPhone|iPad|iPod/i.test(navigator.userAgent),this.isSafari=/Safari/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent),this.isAndroidDevice=
/Android/i.test(navigator.userAgent),this.isInWeixinBrowser)}fullscreen(a){a&&(a.requestFullscreen?a.requestFullscreen():a.mozRequestFullScreen?a.mozRequestFullScreen():a.msRequestFullscreen?a.msRequestFullscreen:a.webkitRequestFullscreen&&a.webkitRequestFullscreen())}exitFullscreen(){document.exitFullscreen?document.exitFullscreen():document.msExitFullscreen||(document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitCancelFullScreen&&document.webkitCancelFullScreen())}isFullscreen(){return document.fullscreen||
document.mozFullScreen||document.webkitIsFullScreen}isFunction(a){return"function"==typeof a}}var L=new Vb;const W={abnormal:1006,invalid_message:1008,out_of_control:4E3,instance_disconnected:4001,instance_not_found:4002,instance_start_failed:4003,webrtc_connection_error:4004,one_client_allowed:4005,timeout:4006,iid_required:4007,locked:4008,invalid_project:4009,no_free_instance:4010,kicked:4100,syncing_data:4101,instance_killed_by_user:4102,invalid_password:4103,nodeservice_stopped:4105,instance_is_busy:4107,
ip_no_access:4108,unknown_client:4109,num_of_instances_exceeded:4110,instance_not_auth:4111,permission_denied:4112,instance_was_preempted:4113,no_username_provided:4114,user_does_not_exist:4115,not_logged_in:4116};N.EMPTY_OBJECT=Object.freeze({});class Y{}Y.doParamsCompatibility=a=>{a.apiOptions=a.apiOptions||{};a.events=a.events||{};a.ui=a.ui||{};"undefined"!=typeof a.showMarker&&(a.showStatus=a.showMarker,delete a.showMarker);"undefined"!=typeof a.actionEventHander&&(a.onaction=a.actionEventHander,
delete a.actionEventHander);"undefined"!=typeof a.useBuiltinCursor&&(a.useBuiltinCursors=a.useBuiltinCursor,delete a.useBuiltinCursor);"undefined"!=typeof a.keyEventReceiver&&(a.keyEventTarget=a.keyEventReceiver,delete a.keyEventReceiver);"undefined"!=typeof a.ui.debugTouchPanel&&(a.ui.debugEventsPanel=a.ui.debugTouchPanel,delete a.ui.debugTouchPanel);var b=(c,d)=>{let e=a[c];e&&(a.events[d]=e,delete a[c])};b("onclose","onConnClose");b("onloaded","onVideoLoaded");b("onvideostatus","onRtcStatsReport");
b("onaction","mouseKeyListener");b=(c,d)=>{let e=a.events.mouseKeyListener[c];e&&(a.events.mouseKeyListener[d]=e,delete a.events.mouseKeyListener[c])};a.events.mouseKeyListener&&(b("onmouseenter","onMouseEnter"),b("onmouseleave","onMouseLeave"),b("onmousemove","onMouseMove"),b("onmousedown","onMouseDown"),b("onmouseup","onMouseUp"),b("onkeydown","onKeyDown"),b("onkeyup","onKeyUp"),b("onkeypress","onKeyPress"));b=(c,d)=>{"undefined"!=typeof a[c]&&(a.ui[d]=a[c],delete a[c])};b("showStartupInfo","startupInfo");
b("showStatus","statusButton");b("showFullscreenButton","fullscreenButton");b("showHomeButton","homeButton");b("showTaskList","taskListBar")};Y.setDefaultParamValues=a=>{a.enableEventSync=N(a.enableEventSync,!1);a.disableResizeObserver=N(a.disableResizeObserver,!1);a.enableApiCallLog=N(a.enableApiCallLog,!1);a.receiveRenderEvents=N(a.receiveRenderEvents,!0);a.registerEvents=N(a.registerEvents,!0);a.keyEventTarget=N(a.keyEventTarget,"video");a.useBuiltinCursors=N(a.useBuiltinCursors,!0);a.ui.startupInfo=
N(a.ui.startupInfo,!0);a.ui.statusIndicator=N(a.ui.statusIndicator,!0);a.ui.statusButton=N(a.ui.statusButton,!1);a.ui.fullscreenButton=N(a.ui.fullscreenButton,!1);a.ui.homeButton=N(a.ui.homeButton,!1);a.ui.taskListBar=N(a.ui.taskListBar,1);a.ui.debugEventsPanel=N(a.ui.debugEventsPanel,!1);a.offer=1};Y.processParams=a=>{Y.doParamsCompatibility(a);Y.setDefaultParamValues(a)};O.prototype.reset=function(){this.reg[0]=1937774191;this.reg[1]=1226093241;this.reg[2]=388252375;this.reg[3]=3666478592;this.reg[4]=
2842636476;this.reg[5]=372324522;this.reg[6]=3817729613;this.reg[7]=2969243214;this.chunk=[];this.size=0};O.prototype.strToBytes=function(a){for(var b,c,d=[],e=0;e<a.length;e++){b=a.charCodeAt(e);c=[];do c.push(b&255),b>>=8;while(b);d=d.concat(c.reverse())}return d};O.prototype.write=function(a){a="string"===typeof a?this.strToBytes(a):a;this.size+=a.length;var b=64-this.chunk.length;if(a.length<b)this.chunk=this.chunk.concat(a);else for(this.chunk=this.chunk.concat(a.slice(0,b));64<=this.chunk.length;)this._compress(this.chunk),
this.chunk=b<a.length?a.slice(b,Math.min(b+64,a.length)):[],b+=64};O.prototype.sum=function(a,b){a&&(this.reset(),this.write(a));this._fill();for(a=0;a<this.chunk.length;a+=64)this._compress(this.chunk.slice(a,a+64));if("hex"==b)for(b="",a=0;8>a;a++)b+=this.reg[a].toString(16);else for(b=Array(32),a=0;8>a;a++){var c=this.reg[a];b[4*a+3]=(c&255)>>>0;c>>>=8;b[4*a+2]=(c&255)>>>0;c>>>=8;b[4*a+1]=(c&255)>>>0;c>>>=8;b[4*a]=(c&255)>>>0}this.reset();return b};O.prototype._compress=function(a){if(64>a)console.error("compress error: not enough data");
else{var b=this._expand(a);a=this.reg.slice(0);for(var c=0;64>c;c++){var d=this._rotl(a[0],12)+a[4]+this._rotl(this._t(c),c);d=(d&4294967295)>>>0;d=this._rotl(d,7);var e=(d^this._rotl(a[0],12))>>>0,f=this._ff(c,a[0],a[1],a[2]);f=f+a[3]+e+b[c+68];f=(f&4294967295)>>>0;e=this._gg(c,a[4],a[5],a[6]);e=e+a[7]+d+b[c];e=(e&4294967295)>>>0;a[3]=a[2];a[2]=this._rotl(a[1],9);a[1]=a[0];a[0]=f;a[7]=a[6];a[6]=this._rotl(a[5],19);a[5]=a[4];a[4]=(e^this._rotl(e,9)^this._rotl(e,17))>>>0}for(b=0;8>b;b++)this.reg[b]=
(this.reg[b]^a[b])>>>0}};O.prototype._fill=function(){var a=8*this.size,b=this.chunk.push(128)%64;for(8>64-b&&(b-=64);56>b;b++)this.chunk.push(0);for(b=0;4>b;b++)this.chunk.push(Math.floor(a/4294967296)>>>8*(3-b)&255);for(b=0;4>b;b++)this.chunk.push(a>>>8*(3-b)&255)};O.prototype._expand=function(a){for(var b=Array(132),c=0;16>c;c++)b[c]=a[4*c]<<24,b[c]|=a[4*c+1]<<16,b[c]|=a[4*c+2]<<8,b[c]|=a[4*c+3],b[c]>>>=0;for(a=16;68>a;a++)c=b[a-16]^b[a-9]^this._rotl(b[a-3],15),c=c^this._rotl(c,15)^this._rotl(c,
23),b[a]=(c^this._rotl(b[a-13],7)^b[a-6])>>>0;for(a=0;64>a;a++)b[a+68]=(b[a]^b[a+4])>>>0;return b};O.prototype._rotl=function(a,b){b%=32;return(a<<b|a>>>32-b)>>>0};O.prototype._t=function(a){if(0<=a&&16>a)return 2043430169;if(16<=a&&64>a)return 2055708042;console.error("invalid j for constant Tj")};O.prototype._ff=function(a,b,c,d){if(0<=a&&16>a)return(b^c^d)>>>0;if(16<=a&&64>a)return(b&c|b&d|c&d)>>>0;console.error("invalid j for bool function FF");return 0};O.prototype._gg=function(a,b,c,d){if(0<=
a&&16>a)return(b^c^d)>>>0;if(16<=a&&64>a)return(b&c|~b&d)>>>0;console.error("invalid j for bool function GG");return 0};O.prototype.toArray=function(a,b){for(var c=[],d=0;d<a.length;d++){var e=a[d];b&&(e=b(e));c.push(e)}return c};class Wb{constructor(a){this.options=a;this.checkParamsMap=new Map}resetInteractTimestamp(a){this.timeOfLastInteraction=a}connect(a){window.WebSocket=window.WebSocket||window.MozWebSocket;window.WebSocket?(this.websocket=new WebSocket(a),this.websocket.onopen=()=>{if(this.options.onopen)this.options.onopen();
this.timerPing=setInterval(()=>{this.timeOfLastInteraction&&this.send({type:"ping",time:this.timeOfLastInteraction})},3E3);"string"==typeof this.options.customString&&this.send({type:"customString",content:this.options.customString})},this.websocket.onmessage=b=>{if(b=JSON.parse(b.data)){switch(b.type){case "checkParamsResult":this.onCheckParamsResponse(b);return;case "detectResponse":this.send(b);return}if(this.options.onmessage)this.options.onmessage(b)}},this.websocket.onerror=b=>{if(this.options.onerror)this.options.onerror(b)},
this.websocket.onclose=b=>{this.websocket=void 0;this.timerPing&&(clearInterval(this.timerPing),this.timerPing=null);if(this.options.onclose)this.options.onclose(b)}):alert("Your browser does not support WebSocket")}isOpened(){return this.websocket&&1===this.websocket.readyState}send(a){a&&this.isOpened()&&this.websocket.send(JSON.stringify(a))}close(a){console.log(`call SignallingConnection.close: ${a||" "}`);this.isOpened()&&(this.websocket.close(a),this.websocket=null)}sendReady(){this.send({type:"ready"})}sendCustomString(a){"string"==
typeof a&&this.send({type:"customString",content:a})}sendCandidate(a){a.candidate&&a.candidate.candidate&&this.send({type:"iceCandidate",candidate:a.candidate})}sendOffer(a,b){this.send({type:"offer",sdp:a,hasVideo:b})}sendApi(a){"api"==(null==a?void 0:a.type)?this.send(a):console.error("Invalid API call format!")}sendInstancePassword(a){"string"==typeof a&&0<a.length&&this.send({type:"inst_sec",p:sa(a)})}updateParams(a){this.send({type:"updateParams",data:a})}checkParams(a){a.timestamp=Date.now();
return new Promise(b=>{this.checkParamsMap[a.timestamp]=b;this.send({type:"checkParams",data:a});this.timerOfCheckParams=setTimeout(()=>{let c=this.checkParamsMap[a.timestamp];c&&(delete this.checkParamsMap[a.timestamp],c(null))},1E3)})}onCheckParamsResponse(a){let b=this.checkParamsMap[a.timestamp];b&&(clearTimeout(this.timerOfCheckParams),delete this.checkParamsMap[a.timestamp],b(a))}}(function(a){"object"===typeof v&&"undefined"!==typeof module?module.exports=a():"function"===typeof define&&define.amd?
define([],a):("undefined"!==typeof window?window:"undefined"!==typeof global?global:"undefined"!==typeof self?self:this).adapter=a()})(function(){return function(){function a(b,c,d){function e(g,t){if(!c[g]){if(!b[g]){var k="function"==typeof require&&require;if(!t&&k)return k(g,!0);if(f)return f(g,!0);t=Error("Cannot find module '"+g+"'");throw t.code="MODULE_NOT_FOUND",t;}t=c[g]={exports:{}};b[g][0].call(t.exports,function(n){return e(b[g][1][n]||n)},t,t.exports,a,b,c,d)}return c[g].exports}for(var f=
"function"==typeof require&&require,l=0;l<d.length;l++)e(d[l]);return e}return a}()({1:[function(a,b,c){a=(0,a("./adapter_factory.js").adapterFactory)({window:"undefined"===typeof window?void 0:window});b.exports=a},{"./adapter_factory.js":2}],2:[function(a,b,c){function d(p){"@babel/helpers - typeof";return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(q){return typeof q}:function(q){return q&&"function"==typeof Symbol&&q.constructor===Symbol&&q!==Symbol.prototype?"symbol":
typeof q},d(p)}function e(p){if("function"!=typeof WeakMap)return null;var q=new WeakMap,r=new WeakMap;return(e=function(u){return u?r:q})(p)}function f(p,q){if(!q&&p&&p.__esModule)return p;if(null===p||"object"!=d(p)&&"function"!=typeof p)return{"default":p};if((q=e(q))&&q.has(p))return q.get(p);var r={__proto__:null},u=Object.defineProperty&&Object.getOwnPropertyDescriptor,w;for(w in p)if("default"!==w&&{}.hasOwnProperty.call(p,w)){var C=u?Object.getOwnPropertyDescriptor(p,w):null;C&&(C.get||C.set)?
Object.defineProperty(r,w,C):r[w]=p[w]}return r["default"]=p,q&&q.set(p,r),r}Object.defineProperty(c,"__esModule",{value:!0});c.adapterFactory=function(){var p=(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).window,q=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{shimChrome:!0,shimFirefox:!0,shimSafari:!0},r=l.log,u=l.detectBrowser(p),w={browserDetails:u,commonShim:n,extractVersion:l.extractVersion,disableLog:l.disableLog,disableWarnings:l.disableWarnings,sdp:m};switch(u.browser){case "chrome":if(!g||
!g.shimPeerConnection||!q.shimChrome){r("Chrome shim is not included in this adapter release.");break}if(null===u.version){r("Chrome shim can not determine version, not shimming.");break}r("adapter.js shimming chrome.");w.browserShim=g;n.shimAddIceCandidateNullOrEmpty(p,u);n.shimParameterlessSetLocalDescription(p,u);g.shimGetUserMedia(p,u);g.shimMediaStream(p,u);g.shimPeerConnection(p,u);g.shimOnTrack(p,u);g.shimAddTrackRemoveTrack(p,u);g.shimGetSendersWithDtmf(p,u);g.shimSenderReceiverGetStats(p,
u);g.fixNegotiationNeeded(p,u);n.shimRTCIceCandidate(p,u);n.shimRTCIceCandidateRelayProtocol(p,u);n.shimConnectionState(p,u);n.shimMaxMessageSize(p,u);n.shimSendThrowTypeError(p,u);n.removeExtmapAllowMixed(p,u);break;case "firefox":if(!t||!t.shimPeerConnection||!q.shimFirefox){r("Firefox shim is not included in this adapter release.");break}r("adapter.js shimming firefox.");w.browserShim=t;n.shimAddIceCandidateNullOrEmpty(p,u);n.shimParameterlessSetLocalDescription(p,u);t.shimGetUserMedia(p,u);t.shimPeerConnection(p,
u);t.shimOnTrack(p,u);t.shimRemoveStream(p,u);t.shimSenderGetStats(p,u);t.shimReceiverGetStats(p,u);t.shimRTCDataChannel(p,u);t.shimAddTransceiver(p,u);t.shimGetParameters(p,u);t.shimCreateOffer(p,u);t.shimCreateAnswer(p,u);n.shimRTCIceCandidate(p,u);n.shimConnectionState(p,u);n.shimMaxMessageSize(p,u);n.shimSendThrowTypeError(p,u);break;case "safari":if(!k||!q.shimSafari){r("Safari shim is not included in this adapter release.");break}r("adapter.js shimming safari.");w.browserShim=k;n.shimAddIceCandidateNullOrEmpty(p,
u);n.shimParameterlessSetLocalDescription(p,u);k.shimRTCIceServerUrls(p,u);k.shimCreateOfferLegacy(p,u);k.shimCallbacksAPI(p,u);k.shimLocalStreamsAPI(p,u);k.shimRemoteStreamsAPI(p,u);k.shimTrackEventTransceiver(p,u);k.shimGetUserMedia(p,u);k.shimAudioContext(p,u);n.shimRTCIceCandidate(p,u);n.shimRTCIceCandidateRelayProtocol(p,u);n.shimMaxMessageSize(p,u);n.shimSendThrowTypeError(p,u);n.removeExtmapAllowMixed(p,u);break;default:r("Unsupported browser!")}return w};var l=f(a("./utils")),g=f(a("./chrome/chrome_shim")),
t=f(a("./firefox/firefox_shim")),k=f(a("./safari/safari_shim")),n=f(a("./common_shim")),m=f(a("sdp"))},{"./chrome/chrome_shim":3,"./common_shim":5,"./firefox/firefox_shim":6,"./safari/safari_shim":9,"./utils":10,sdp:11}],3:[function(a,b,c){function d(k){if("function"!=typeof WeakMap)return null;var n=new WeakMap,m=new WeakMap;return(d=function(p){return p?m:n})(k)}function e(k,n,m){a:if("object"==f(n)&&n){var p=n[Symbol.toPrimitive];if(void 0!==p){n=p.call(n,"string");if("object"!=f(n))break a;throw new TypeError("@@toPrimitive must return a primitive value.");
}n=String(n)}n="symbol"==f(n)?n:n+"";n in k?Object.defineProperty(k,n,{value:m,enumerable:!0,configurable:!0,writable:!0}):k[n]=m;return k}function f(k){"@babel/helpers - typeof";return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},f(k)}function l(k){k.RTCPeerConnection.prototype.getLocalStreams=function(){var r=this;this._shimmedLocalStreams=
this._shimmedLocalStreams||{};return Object.keys(this._shimmedLocalStreams).map(function(u){return r._shimmedLocalStreams[u][0]})};var n=k.RTCPeerConnection.prototype.addTrack;k.RTCPeerConnection.prototype.addTrack=function(r,u){if(!u)return n.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};var w=n.apply(this,arguments);this._shimmedLocalStreams[u.id]?-1===this._shimmedLocalStreams[u.id].indexOf(w)&&this._shimmedLocalStreams[u.id].push(w):this._shimmedLocalStreams[u.id]=
[u,w];return w};var m=k.RTCPeerConnection.prototype.addStream;k.RTCPeerConnection.prototype.addStream=function(r){var u=this;this._shimmedLocalStreams=this._shimmedLocalStreams||{};r.getTracks().forEach(function(A){if(u.getSenders().find(function(F){return F.track===A}))throw new DOMException("Track already exists.","InvalidAccessError");});var w=this.getSenders();m.apply(this,arguments);var C=this.getSenders().filter(function(A){return-1===w.indexOf(A)});this._shimmedLocalStreams[r.id]=[r].concat(C)};
var p=k.RTCPeerConnection.prototype.removeStream;k.RTCPeerConnection.prototype.removeStream=function(r){this._shimmedLocalStreams=this._shimmedLocalStreams||{};delete this._shimmedLocalStreams[r.id];return p.apply(this,arguments)};var q=k.RTCPeerConnection.prototype.removeTrack;k.RTCPeerConnection.prototype.removeTrack=function(r){var u=this;this._shimmedLocalStreams=this._shimmedLocalStreams||{};r&&Object.keys(this._shimmedLocalStreams).forEach(function(w){var C=u._shimmedLocalStreams[w].indexOf(r);
-1!==C&&u._shimmedLocalStreams[w].splice(C,1);1===u._shimmedLocalStreams[w].length&&delete u._shimmedLocalStreams[w]});return q.apply(this,arguments)}}Object.defineProperty(c,"__esModule",{value:!0});c.fixNegotiationNeeded=function(k,n){g.wrapPeerConnectionEvent(k,"negotiationneeded",function(m){var p=m.target;if(!(72>n.version||p.getConfiguration&&"plan-b"===p.getConfiguration().sdpSemantics)||"stable"===p.signalingState)return m})};c.shimAddTrackRemoveTrack=function(k,n){function m(A,F){var H=F.sdp;
Object.keys(A._reverseStreams||[]).forEach(function(J){J=A._reverseStreams[J];H=H.replace(new RegExp(A._streams[J.id].id,"g"),J.id)});return new RTCSessionDescription({type:F.type,sdp:H})}function p(A,F){var H=F.sdp;Object.keys(A._reverseStreams||[]).forEach(function(J){J=A._reverseStreams[J];H=H.replace(new RegExp(J.id,"g"),A._streams[J.id].id)});return new RTCSessionDescription({type:F.type,sdp:H})}if(k.RTCPeerConnection){if(k.RTCPeerConnection.prototype.addTrack&&65<=n.version)return l(k);var q=
k.RTCPeerConnection.prototype.getLocalStreams;k.RTCPeerConnection.prototype.getLocalStreams=function(){var A=this,F=q.apply(this);this._reverseStreams=this._reverseStreams||{};return F.map(function(H){return A._reverseStreams[H.id]})};var r=k.RTCPeerConnection.prototype.addStream;k.RTCPeerConnection.prototype.addStream=function(A){var F=this;this._streams=this._streams||{};this._reverseStreams=this._reverseStreams||{};A.getTracks().forEach(function(J){if(F.getSenders().find(function(P){return P.track===
J}))throw new DOMException("Track already exists.","InvalidAccessError");});if(!this._reverseStreams[A.id]){var H=new k.MediaStream(A.getTracks());this._streams[A.id]=H;this._reverseStreams[H.id]=A;A=H}r.apply(this,[A])};var u=k.RTCPeerConnection.prototype.removeStream;k.RTCPeerConnection.prototype.removeStream=function(A){this._streams=this._streams||{};this._reverseStreams=this._reverseStreams||{};u.apply(this,[this._streams[A.id]||A]);delete this._reverseStreams[this._streams[A.id]?this._streams[A.id].id:
A.id];delete this._streams[A.id]};k.RTCPeerConnection.prototype.addTrack=function(A,F){var H=this;if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");var J=[].slice.call(arguments,1);if(1!==J.length||!J[0].getTracks().find(function(P){return P===A}))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find(function(P){return P.track===
A}))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{};this._reverseStreams=this._reverseStreams||{};(J=this._streams[F.id])?(J.addTrack(A),Promise.resolve().then(function(){H.dispatchEvent(new Event("negotiationneeded"))})):(J=new k.MediaStream([A]),this._streams[F.id]=J,this._reverseStreams[J.id]=F,this.addStream(J));return this.getSenders().find(function(P){return P.track===A})};["createOffer","createAnswer"].forEach(function(A){var F=k.RTCPeerConnection.prototype[A],
H=e({},A,function(){var J=this,P=arguments;return arguments.length&&"function"===typeof arguments[0]?F.apply(this,[function(X){X=m(J,X);P[0].apply(null,[X])},function(X){P[1]&&P[1].apply(null,X)},arguments[2]]):F.apply(this,arguments).then(function(X){return m(J,X)})});k.RTCPeerConnection.prototype[A]=H[A]});var w=k.RTCPeerConnection.prototype.setLocalDescription;k.RTCPeerConnection.prototype.setLocalDescription=function(){if(!arguments.length||!arguments[0].type)return w.apply(this,arguments);arguments[0]=
p(this,arguments[0]);return w.apply(this,arguments)};var C=Object.getOwnPropertyDescriptor(k.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(k.RTCPeerConnection.prototype,"localDescription",{get:function(){var A=C.get.apply(this);return""===A.type?A:m(this,A)}});k.RTCPeerConnection.prototype.removeTrack=function(A){var F=this;if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!A._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.",
"TypeError");if(A._pc!==this)throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{};var H;Object.keys(this._streams).forEach(function(J){F._streams[J].getTracks().find(function(P){return A.track===P})&&(H=F._streams[J])});H&&(1===H.getTracks().length?this.removeStream(this._reverseStreams[H.id]):H.removeTrack(A.track),this.dispatchEvent(new Event("negotiationneeded")))}}};c.shimAddTrackRemoveTrackWithNative=l;c.shimGetSendersWithDtmf=
function(k){if("object"===f(k)&&k.RTCPeerConnection&&!("getSenders"in k.RTCPeerConnection.prototype)&&"createDTMFSender"in k.RTCPeerConnection.prototype){var n=function(w,C){return{track:C,get dtmf(){void 0===this._dtmf&&(this._dtmf="audio"===C.kind?w.createDTMFSender(C):null);return this._dtmf},_pc:w}};if(!k.RTCPeerConnection.prototype.getSenders){k.RTCPeerConnection.prototype.getSenders=function(){this._senders=this._senders||[];return this._senders.slice()};var m=k.RTCPeerConnection.prototype.addTrack;
k.RTCPeerConnection.prototype.addTrack=function(w,C){var A=m.apply(this,arguments);A||(A=n(this,w),this._senders.push(A));return A};var p=k.RTCPeerConnection.prototype.removeTrack;k.RTCPeerConnection.prototype.removeTrack=function(w){p.apply(this,arguments);var C=this._senders.indexOf(w);-1!==C&&this._senders.splice(C,1)}}var q=k.RTCPeerConnection.prototype.addStream;k.RTCPeerConnection.prototype.addStream=function(w){var C=this;this._senders=this._senders||[];q.apply(this,[w]);w.getTracks().forEach(function(A){C._senders.push(n(C,
A))})};var r=k.RTCPeerConnection.prototype.removeStream;k.RTCPeerConnection.prototype.removeStream=function(w){var C=this;this._senders=this._senders||[];r.apply(this,[w]);w.getTracks().forEach(function(A){var F=C._senders.find(function(H){return H.track===A});F&&C._senders.splice(C._senders.indexOf(F),1)})}}else if("object"===f(k)&&k.RTCPeerConnection&&"getSenders"in k.RTCPeerConnection.prototype&&"createDTMFSender"in k.RTCPeerConnection.prototype&&k.RTCRtpSender&&!("dtmf"in k.RTCRtpSender.prototype)){var u=
k.RTCPeerConnection.prototype.getSenders;k.RTCPeerConnection.prototype.getSenders=function(){var w=this,C=u.apply(this,[]);C.forEach(function(A){return A._pc=w});return C};Object.defineProperty(k.RTCRtpSender.prototype,"dtmf",{get:function(){void 0===this._dtmf&&(this._dtmf="audio"===this.track.kind?this._pc.createDTMFSender(this.track):null);return this._dtmf}})}};Object.defineProperty(c,"shimGetUserMedia",{enumerable:!0,get:function(){return t.shimGetUserMedia}});c.shimMediaStream=function(k){k.MediaStream=
k.MediaStream||k.webkitMediaStream};c.shimOnTrack=function(k){if("object"!==f(k)||!k.RTCPeerConnection||"ontrack"in k.RTCPeerConnection.prototype)g.wrapPeerConnectionEvent(k,"track",function(m){m.transceiver||Object.defineProperty(m,"transceiver",{value:{receiver:m.receiver}});return m});else{Object.defineProperty(k.RTCPeerConnection.prototype,"ontrack",{get:function(){return this._ontrack},set:function(m){this._ontrack&&this.removeEventListener("track",this._ontrack);this.addEventListener("track",
this._ontrack=m)},enumerable:!0,configurable:!0});var n=k.RTCPeerConnection.prototype.setRemoteDescription;k.RTCPeerConnection.prototype.setRemoteDescription=function(){var m=this;this._ontrackpoly||(this._ontrackpoly=function(p){p.stream.addEventListener("addtrack",function(q){var r=k.RTCPeerConnection.prototype.getReceivers?m.getReceivers().find(function(w){return w.track&&w.track.id===q.track.id}):{track:q.track};var u=new Event("track");u.track=q.track;u.receiver=r;u.transceiver={receiver:r};
u.streams=[p.stream];m.dispatchEvent(u)});p.stream.getTracks().forEach(function(q){var r=k.RTCPeerConnection.prototype.getReceivers?m.getReceivers().find(function(w){return w.track&&w.track.id===q.id}):{track:q};var u=new Event("track");u.track=q;u.receiver=r;u.transceiver={receiver:r};u.streams=[p.stream];m.dispatchEvent(u)})},this.addEventListener("addstream",this._ontrackpoly));return n.apply(this,arguments)}}};c.shimPeerConnection=function(k,n){!k.RTCPeerConnection&&k.webkitRTCPeerConnection&&
(k.RTCPeerConnection=k.webkitRTCPeerConnection);k.RTCPeerConnection&&53>n.version&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(m){var p=k.RTCPeerConnection.prototype[m],q=e({},m,function(){arguments[0]=new ("addIceCandidate"===m?k.RTCIceCandidate:k.RTCSessionDescription)(arguments[0]);return p.apply(this,arguments)});k.RTCPeerConnection.prototype[m]=q[m]})};c.shimSenderReceiverGetStats=function(k){if("object"===f(k)&&k.RTCPeerConnection&&k.RTCRtpSender&&k.RTCRtpReceiver){if(!("getStats"in
k.RTCRtpSender.prototype)){var n=k.RTCPeerConnection.prototype.getSenders;n&&(k.RTCPeerConnection.prototype.getSenders=function(){var r=this,u=n.apply(this,[]);u.forEach(function(w){return w._pc=r});return u});var m=k.RTCPeerConnection.prototype.addTrack;m&&(k.RTCPeerConnection.prototype.addTrack=function(){var r=m.apply(this,arguments);r._pc=this;return r});k.RTCRtpSender.prototype.getStats=function(){var r=this;return this._pc.getStats().then(function(u){return g.filterStats(u,r.track,!0)})}}if(!("getStats"in
k.RTCRtpReceiver.prototype)){var p=k.RTCPeerConnection.prototype.getReceivers;p&&(k.RTCPeerConnection.prototype.getReceivers=function(){var r=this,u=p.apply(this,[]);u.forEach(function(w){return w._pc=r});return u});g.wrapPeerConnectionEvent(k,"track",function(r){r.receiver._pc=r.srcElement;return r});k.RTCRtpReceiver.prototype.getStats=function(){var r=this;return this._pc.getStats().then(function(u){return g.filterStats(u,r.track,!1)})}}if("getStats"in k.RTCRtpSender.prototype&&"getStats"in k.RTCRtpReceiver.prototype){var q=
k.RTCPeerConnection.prototype.getStats;k.RTCPeerConnection.prototype.getStats=function(){if(0<arguments.length&&arguments[0]instanceof k.MediaStreamTrack){var r=arguments[0],u,w,C;this.getSenders().forEach(function(A){A.track===r&&(u?C=!0:u=A)});this.getReceivers().forEach(function(A){A.track===r&&(w?C=!0:w=A);return A.track===r});return C||u&&w?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):u?u.getStats():w?w.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.",
"InvalidAccessError"))}return q.apply(this,arguments)}}}};var g=function(k,n){if(!n&&k&&k.__esModule)return k;if(null===k||"object"!=f(k)&&"function"!=typeof k)return{"default":k};if((n=d(n))&&n.has(k))return n.get(k);var m={__proto__:null},p=Object.defineProperty&&Object.getOwnPropertyDescriptor,q;for(q in k)if("default"!==q&&{}.hasOwnProperty.call(k,q)){var r=p?Object.getOwnPropertyDescriptor(k,q):null;r&&(r.get||r.set)?Object.defineProperty(m,q,r):m[q]=k[q]}return m["default"]=k,n&&n.set(k,m),
m}(a("../utils.js")),t=a("./getusermedia")},{"../utils.js":10,"./getusermedia":4}],4:[function(a,b,c){function d(l){if("function"!=typeof WeakMap)return null;var g=new WeakMap,t=new WeakMap;return(d=function(k){return k?t:g})(l)}function e(l){"@babel/helpers - typeof";return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(g){return typeof g}:function(g){return g&&"function"==typeof Symbol&&g.constructor===Symbol&&g!==Symbol.prototype?"symbol":typeof g},e(l)}Object.defineProperty(c,
"__esModule",{value:!0});c.shimGetUserMedia=function(l,g){var t=l&&l.navigator;if(t.mediaDevices){var k=function(q){if("object"!==e(q)||q.mandatory||q.optional)return q;var r={};Object.keys(q).forEach(function(u){if("require"!==u&&"advanced"!==u&&"mediaSource"!==u){var w="object"===e(q[u])?q[u]:{ideal:q[u]};void 0!==w.exact&&"number"===typeof w.exact&&(w.min=w.max=w.exact);var C=function(F,H){return F?F+H.charAt(0).toUpperCase()+H.slice(1):"deviceId"===H?"sourceId":H};if(void 0!==w.ideal){r.optional=
r.optional||[];var A={};"number"===typeof w.ideal?(A[C("min",u)]=w.ideal,r.optional.push(A),A={},A[C("max",u)]=w.ideal):A[C("",u)]=w.ideal;r.optional.push(A)}void 0!==w.exact&&"number"!==typeof w.exact?(r.mandatory=r.mandatory||{},r.mandatory[C("",u)]=w.exact):["min","max"].forEach(function(F){void 0!==w[F]&&(r.mandatory=r.mandatory||{},r.mandatory[C(F,u)]=w[F])})}});q.advanced&&(r.optional=(r.optional||[]).concat(q.advanced));return r},n=function(q,r){if(61<=g.version)return r(q);if((q=JSON.parse(JSON.stringify(q)))&&
"object"===e(q.audio)){var u=function(A,F,H){F in A&&!(H in A)&&(A[H]=A[F],delete A[F])};q=JSON.parse(JSON.stringify(q));u(q.audio,"autoGainControl","googAutoGainControl");u(q.audio,"noiseSuppression","googNoiseSuppression");q.audio=k(q.audio)}if(q&&"object"===e(q.video)){var w=q.video.facingMode;w=w&&("object"===e(w)?w:{ideal:w});u=66>g.version;if(!(!w||"user"!==w.exact&&"environment"!==w.exact&&"user"!==w.ideal&&"environment"!==w.ideal||t.mediaDevices.getSupportedConstraints&&t.mediaDevices.getSupportedConstraints().facingMode&&
!u)){delete q.video.facingMode;if("environment"===w.exact||"environment"===w.ideal)var C=["back","rear"];else if("user"===w.exact||"user"===w.ideal)C=["front"];if(C)return t.mediaDevices.enumerateDevices().then(function(A){A=A.filter(function(H){return"videoinput"===H.kind});var F=A.find(function(H){return C.some(function(J){return H.label.toLowerCase().includes(J)})});!F&&A.length&&C.includes("back")&&(F=A[A.length-1]);F&&(q.video.deviceId=w.exact?{exact:F.deviceId}:{ideal:F.deviceId});q.video=k(q.video);
f("chrome: "+JSON.stringify(q));return r(q)})}q.video=k(q.video)}f("chrome: "+JSON.stringify(q));return r(q)},m=function(q){return 64<=g.version?q:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",
ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[q.name]||q.name,message:q.message,constraint:q.constraint||q.constraintName,toString:function(){return this.name+(this.message&&": ")+this.message}}};t.getUserMedia=function(q,r,u){n(q,function(w){t.webkitGetUserMedia(w,r,function(C){u&&u(m(C))})})}.bind(t);if(t.mediaDevices.getUserMedia){var p=t.mediaDevices.getUserMedia.bind(t.mediaDevices);t.mediaDevices.getUserMedia=function(q){return n(q,function(r){return p(r).then(function(u){if(r.audio&&
!u.getAudioTracks().length||r.video&&!u.getVideoTracks().length)throw u.getTracks().forEach(function(w){w.stop()}),new DOMException("","NotFoundError");return u},function(u){return Promise.reject(m(u))})})}}}};var f=function(l,g){if(!g&&l&&l.__esModule)return l;if(null===l||"object"!=e(l)&&"function"!=typeof l)return{"default":l};if((g=d(g))&&g.has(l))return g.get(l);var t={__proto__:null},k=Object.defineProperty&&Object.getOwnPropertyDescriptor,n;for(n in l)if("default"!==n&&{}.hasOwnProperty.call(l,
n)){var m=k?Object.getOwnPropertyDescriptor(l,n):null;m&&(m.get||m.set)?Object.defineProperty(t,n,m):t[n]=l[n]}return t["default"]=l,g&&g.set(l,t),t}(a("../utils.js")).log},{"../utils.js":10}],5:[function(a,b,c){function d(g){if("function"!=typeof WeakMap)return null;var t=new WeakMap,k=new WeakMap;return(d=function(n){return n?k:t})(g)}function e(g){"@babel/helpers - typeof";return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==
typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(g)}Object.defineProperty(c,"__esModule",{value:!0});c.removeExtmapAllowMixed=function(g,t){if(g.RTCPeerConnection&&!("chrome"===t.browser&&71<=t.version||"safari"===t.browser&&605<=t.version)){var k=g.RTCPeerConnection.prototype.setRemoteDescription;g.RTCPeerConnection.prototype.setRemoteDescription=function(n){if(n&&n.sdp&&-1!==n.sdp.indexOf("\na=extmap-allow-mixed")){var m=n.sdp.split("\n").filter(function(p){return"a=extmap-allow-mixed"!==
p.trim()}).join("\n");g.RTCSessionDescription&&n instanceof g.RTCSessionDescription?arguments[0]=new g.RTCSessionDescription({type:n.type,sdp:m}):n.sdp=m}return k.apply(this,arguments)}}};c.shimAddIceCandidateNullOrEmpty=function(g,t){if(g.RTCPeerConnection&&g.RTCPeerConnection.prototype){var k=g.RTCPeerConnection.prototype.addIceCandidate;k&&0!==k.length&&(g.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?("chrome"===t.browser&&78>t.version||"firefox"===t.browser&&68>t.version||
"safari"===t.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():k.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}};c.shimConnectionState=function(g){if(g.RTCPeerConnection&&!("connectionState"in g.RTCPeerConnection.prototype)){var t=g.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get:function(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0});
Object.defineProperty(t,"onconnectionstatechange",{get:function(){return this._onconnectionstatechange||null},set:function(k){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange);k&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=k)},enumerable:!0,configurable:!0});["setLocalDescription","setRemoteDescription"].forEach(function(k){var n=t[k];t[k]=function(){this._connectionstatechangepoly||
(this._connectionstatechangepoly=function(m){var p=m.target;if(p._lastConnectionState!==p.connectionState){p._lastConnectionState=p.connectionState;var q=new Event("connectionstatechange",m);p.dispatchEvent(q)}return m},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly));return n.apply(this,arguments)}})}};c.shimMaxMessageSize=function(g,t){if(g.RTCPeerConnection){"sctp"in g.RTCPeerConnection.prototype||Object.defineProperty(g.RTCPeerConnection.prototype,"sctp",{get:function(){return"undefined"===
typeof this._sctp?null:this._sctp}});var k=function(r){if(!r||!r.sdp)return!1;r=f["default"].splitSections(r.sdp);r.shift();return r.some(function(u){return(u=f["default"].parseMLine(u))&&"application"===u.kind&&-1!==u.protocol.indexOf("SCTP")})},n=function(r){r=r.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===r||2>r.length)return-1;r=parseInt(r[1],10);return r!==r?-1:r},m=function(r){var u=65536;"firefox"===t.browser&&(u=57>t.version?-1===r?16384:2147483637:60>t.version?57===t.version?65535:
65536:2147483637);return u},p=function(r,u){var w=65536;"firefox"===t.browser&&57===t.version&&(w=65535);r=f["default"].matchPrefix(r.sdp,"a=max-message-size:");0<r.length?w=parseInt(r[0].substring(19),10):"firefox"===t.browser&&-1!==u&&(w=2147483637);return w},q=g.RTCPeerConnection.prototype.setRemoteDescription;g.RTCPeerConnection.prototype.setRemoteDescription=function(){this._sctp=null;"chrome"===t.browser&&76<=t.version&&"plan-b"===this.getConfiguration().sdpSemantics&&Object.defineProperty(this,
"sctp",{get:function(){return"undefined"===typeof this._sctp?null:this._sctp},enumerable:!0,configurable:!0});if(k(arguments[0])){var r=n(arguments[0]),u=m(r);r=p(arguments[0],r);var w=0===u&&0===r?Number.POSITIVE_INFINITY:0===u||0===r?Math.max(u,r):Math.min(u,r);u={};Object.defineProperty(u,"maxMessageSize",{get:function(){return w}});this._sctp=u}return q.apply(this,arguments)}}};c.shimParameterlessSetLocalDescription=function(g,t){if(g.RTCPeerConnection&&g.RTCPeerConnection.prototype){var k=g.RTCPeerConnection.prototype.setLocalDescription;
k&&0!==k.length&&(g.RTCPeerConnection.prototype.setLocalDescription=function(){var n=this,m=arguments[0]||{};if("object"!==e(m)||m.type&&m.sdp)return k.apply(this,arguments);m={type:m.type,sdp:m.sdp};if(!m.type)switch(this.signalingState){case "stable":case "have-local-offer":case "have-remote-pranswer":m.type="offer";break;default:m.type="answer"}return m.sdp||"offer"!==m.type&&"answer"!==m.type?k.apply(this,[m]):("offer"===m.type?this.createOffer:this.createAnswer).apply(this).then(function(p){return k.apply(n,
[p])})})}};c.shimRTCIceCandidate=function(g){if(g.RTCIceCandidate&&!(g.RTCIceCandidate&&"foundation"in g.RTCIceCandidate.prototype)){var t=g.RTCIceCandidate;g.RTCIceCandidate=function(k){"object"===e(k)&&k.candidate&&0===k.candidate.indexOf("a=")&&(k=JSON.parse(JSON.stringify(k)),k.candidate=k.candidate.substring(2));if(k.candidate&&k.candidate.length){var n=new t(k);k=f["default"].parseCandidate(k.candidate);for(var m in k)m in n||Object.defineProperty(n,m,{value:k[m]});n.toJSON=function(){return{candidate:n.candidate,
sdpMid:n.sdpMid,sdpMLineIndex:n.sdpMLineIndex,usernameFragment:n.usernameFragment}};return n}return new t(k)};g.RTCIceCandidate.prototype=t.prototype;l.wrapPeerConnectionEvent(g,"icecandidate",function(k){k.candidate&&Object.defineProperty(k,"candidate",{value:new g.RTCIceCandidate(k.candidate),writable:"false"});return k})}};c.shimRTCIceCandidateRelayProtocol=function(g){!g.RTCIceCandidate||g.RTCIceCandidate&&"relayProtocol"in g.RTCIceCandidate.prototype||l.wrapPeerConnectionEvent(g,"icecandidate",
function(t){if(t.candidate){var k=f["default"].parseCandidate(t.candidate.candidate);"relay"===k.type&&(t.candidate.relayProtocol={0:"tls",1:"tcp",2:"udp"}[k.priority>>24])}return t})};c.shimSendThrowTypeError=function(g){function t(n,m){var p=n.send;n.send=function(){var q=arguments[0];q=q.length||q.size||q.byteLength;if("open"===n.readyState&&m.sctp&&q>m.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+m.sctp.maxMessageSize+" bytes)");return p.apply(n,arguments)}}
if(g.RTCPeerConnection&&"createDataChannel"in g.RTCPeerConnection.prototype){var k=g.RTCPeerConnection.prototype.createDataChannel;g.RTCPeerConnection.prototype.createDataChannel=function(){var n=k.apply(this,arguments);t(n,this);return n};l.wrapPeerConnectionEvent(g,"datachannel",function(n){t(n.channel,n.target);return n})}};var f=function(g){return g&&g.__esModule?g:{"default":g}}(a("sdp")),l=function(g,t){if(!t&&g&&g.__esModule)return g;if(null===g||"object"!=e(g)&&"function"!=typeof g)return{"default":g};
if((t=d(t))&&t.has(g))return t.get(g);var k={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor,m;for(m in g)if("default"!==m&&{}.hasOwnProperty.call(g,m)){var p=n?Object.getOwnPropertyDescriptor(g,m):null;p&&(p.get||p.set)?Object.defineProperty(k,m,p):k[m]=g[m]}return k["default"]=g,t&&t.set(g,k),k}(a("./utils"))},{"./utils":10,sdp:11}],6:[function(a,b,c){function d(m){if("function"!=typeof WeakMap)return null;var p=new WeakMap,q=new WeakMap;return(d=function(r){return r?q:
p})(m)}function e(m){var p=Array.isArray(m)?f(m):void 0;p||(p="undefined"!==typeof Symbol&&null!=m[Symbol.iterator]||null!=m["@@iterator"]?Array.from(m):void 0);if(!p)a:{if(m){if("string"===typeof m){p=f(m,void 0);break a}p=Object.prototype.toString.call(m).slice(8,-1);"Object"===p&&m.constructor&&(p=m.constructor.name);if("Map"===p||"Set"===p){p=Array.from(m);break a}if("Arguments"===p||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(p)){p=f(m,void 0);break a}}p=void 0}if(!(m=p))throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
return m}function f(m,p){if(null==p||p>m.length)p=m.length;for(var q=0,r=Array(p);q<p;q++)r[q]=m[q];return r}function l(m,p,q){a:if("object"==g(p)&&p){var r=p[Symbol.toPrimitive];if(void 0!==r){p=r.call(p,"string");if("object"!=g(p))break a;throw new TypeError("@@toPrimitive must return a primitive value.");}p=String(p)}p="symbol"==g(p)?p:p+"";p in m?Object.defineProperty(m,p,{value:q,enumerable:!0,configurable:!0,writable:!0}):m[p]=q;return m}function g(m){"@babel/helpers - typeof";return g="function"==
typeof Symbol&&"symbol"==typeof Symbol.iterator?function(p){return typeof p}:function(p){return p&&"function"==typeof Symbol&&p.constructor===Symbol&&p!==Symbol.prototype?"symbol":typeof p},g(m)}Object.defineProperty(c,"__esModule",{value:!0});c.shimAddTransceiver=function(m){if("object"===g(m)&&m.RTCPeerConnection){var p=m.RTCPeerConnection.prototype.addTransceiver;p&&(m.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];var q=arguments[1]&&arguments[1].sendEncodings;
void 0===q&&(q=[]);q=e(q);var r=0<q.length;r&&q.forEach(function(C){if("rid"in C&&!/^[a-z0-9]{0,16}$/i.test(C.rid))throw new TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in C&&!(1<=parseFloat(C.scaleResolutionDownBy)))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in C&&!(0<=parseFloat(C.maxFramerate)))throw new RangeError("max_framerate must be >= 0.0");});var u=p.apply(this,arguments);if(r){var w=u.sender;r=w.getParameters();"encodings"in r&&
(1!==r.encodings.length||0!==Object.keys(r.encodings[0]).length)||(r.encodings=q,w.sendEncodings=q,this.setParametersPromises.push(w.setParameters(r).then(function(){delete w.sendEncodings})["catch"](function(){delete w.sendEncodings})))}return u})}};c.shimCreateAnswer=function(m){if("object"===g(m)&&m.RTCPeerConnection){var p=m.RTCPeerConnection.prototype.createAnswer;m.RTCPeerConnection.prototype.createAnswer=function(){var q=arguments,r=this;return this.setParametersPromises&&this.setParametersPromises.length?
Promise.all(this.setParametersPromises).then(function(){return p.apply(r,q)})["finally"](function(){r.setParametersPromises=[]}):p.apply(this,arguments)}}};c.shimCreateOffer=function(m){if("object"===g(m)&&m.RTCPeerConnection){var p=m.RTCPeerConnection.prototype.createOffer;m.RTCPeerConnection.prototype.createOffer=function(){var q=arguments,r=this;return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(function(){return p.apply(r,q)})["finally"](function(){r.setParametersPromises=
[]}):p.apply(this,arguments)}}};Object.defineProperty(c,"shimGetDisplayMedia",{enumerable:!0,get:function(){return n.shimGetDisplayMedia}});c.shimGetParameters=function(m){if("object"===g(m)&&m.RTCRtpSender){var p=m.RTCRtpSender.prototype.getParameters;p&&(m.RTCRtpSender.prototype.getParameters=function(){var q=p.apply(this,arguments);"encodings"in q||(q.encodings=[].concat(this.sendEncodings||[{}]));return q})}};Object.defineProperty(c,"shimGetUserMedia",{enumerable:!0,get:function(){return k.shimGetUserMedia}});
c.shimOnTrack=function(m){"object"===g(m)&&m.RTCTrackEvent&&"receiver"in m.RTCTrackEvent.prototype&&!("transceiver"in m.RTCTrackEvent.prototype)&&Object.defineProperty(m.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})};c.shimPeerConnection=function(m,p){if("object"===g(m)&&(m.RTCPeerConnection||m.mozRTCPeerConnection)){!m.RTCPeerConnection&&m.mozRTCPeerConnection&&(m.RTCPeerConnection=m.mozRTCPeerConnection);53>p.version&&["setLocalDescription","setRemoteDescription",
"addIceCandidate"].forEach(function(u){var w=m.RTCPeerConnection.prototype[u],C=l({},u,function(){arguments[0]=new ("addIceCandidate"===u?m.RTCIceCandidate:m.RTCSessionDescription)(arguments[0]);return w.apply(this,arguments)});m.RTCPeerConnection.prototype[u]=C[u]});var q={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},r=m.RTCPeerConnection.prototype.getStats;m.RTCPeerConnection.prototype.getStats=
function(){var u=Array.prototype.slice.call(arguments),w=u[1],C=u[2];return r.apply(this,[u[0]||null]).then(function(A){if(53>p.version&&!w)try{A.forEach(function(F){F.type=q[F.type]||F.type})}catch(F){if("TypeError"!==F.name)throw F;A.forEach(function(H,J){A.set(J,Object.assign({},H,{type:q[H.type]||H.type}))})}return A}).then(w,C)}}};c.shimRTCDataChannel=function(m){m.DataChannel&&!m.RTCDataChannel&&(m.RTCDataChannel=m.DataChannel)};c.shimReceiverGetStats=function(m){if("object"===g(m)&&m.RTCPeerConnection&&
m.RTCRtpSender&&!(m.RTCRtpSender&&"getStats"in m.RTCRtpReceiver.prototype)){var p=m.RTCPeerConnection.prototype.getReceivers;p&&(m.RTCPeerConnection.prototype.getReceivers=function(){var q=this,r=p.apply(this,[]);r.forEach(function(u){return u._pc=q});return r});t.wrapPeerConnectionEvent(m,"track",function(q){q.receiver._pc=q.srcElement;return q});m.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}};c.shimRemoveStream=function(m){!m.RTCPeerConnection||"removeStream"in
m.RTCPeerConnection.prototype||(m.RTCPeerConnection.prototype.removeStream=function(p){var q=this;t.deprecated("removeStream","removeTrack");this.getSenders().forEach(function(r){r.track&&p.getTracks().includes(r.track)&&q.removeTrack(r)})})};c.shimSenderGetStats=function(m){if("object"===g(m)&&m.RTCPeerConnection&&m.RTCRtpSender&&!(m.RTCRtpSender&&"getStats"in m.RTCRtpSender.prototype)){var p=m.RTCPeerConnection.prototype.getSenders;p&&(m.RTCPeerConnection.prototype.getSenders=function(){var r=this,
u=p.apply(this,[]);u.forEach(function(w){return w._pc=r});return u});var q=m.RTCPeerConnection.prototype.addTrack;q&&(m.RTCPeerConnection.prototype.addTrack=function(){var r=q.apply(this,arguments);r._pc=this;return r});m.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}};var t=function(m,p){if(!p&&m&&m.__esModule)return m;if(null===m||"object"!=g(m)&&"function"!=typeof m)return{"default":m};if((p=d(p))&&p.has(m))return p.get(m);
var q={__proto__:null},r=Object.defineProperty&&Object.getOwnPropertyDescriptor,u;for(u in m)if("default"!==u&&{}.hasOwnProperty.call(m,u)){var w=r?Object.getOwnPropertyDescriptor(m,u):null;w&&(w.get||w.set)?Object.defineProperty(q,u,w):q[u]=m[u]}return q["default"]=m,p&&p.set(m,q),q}(a("../utils")),k=a("./getusermedia"),n=a("./getdisplaymedia")},{"../utils":10,"./getdisplaymedia":7,"./getusermedia":8}],7:[function(a,b,c){Object.defineProperty(c,"__esModule",{value:!0});c.shimGetDisplayMedia=function(d,
e){d.navigator.mediaDevices&&"getDisplayMedia"in d.navigator.mediaDevices||!d.navigator.mediaDevices||(d.navigator.mediaDevices.getDisplayMedia=function(f){if(!f||!f.video)return f=new DOMException("getDisplayMedia without video constraints is undefined"),f.name="NotFoundError",f.code=8,Promise.reject(f);!0===f.video?f.video={mediaSource:e}:f.video.mediaSource=e;return d.navigator.mediaDevices.getUserMedia(f)})}},{}],8:[function(a,b,c){function d(l){if("function"!=typeof WeakMap)return null;var g=
new WeakMap,t=new WeakMap;return(d=function(k){return k?t:g})(l)}function e(l){"@babel/helpers - typeof";return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(g){return typeof g}:function(g){return g&&"function"==typeof Symbol&&g.constructor===Symbol&&g!==Symbol.prototype?"symbol":typeof g},e(l)}Object.defineProperty(c,"__esModule",{value:!0});c.shimGetUserMedia=function(l,g){var t=l&&l.navigator;l=l&&l.MediaStreamTrack;t.getUserMedia=function(q,r,u){f.deprecated("navigator.getUserMedia",
"navigator.mediaDevices.getUserMedia");t.mediaDevices.getUserMedia(q).then(r,u)};if(!(55<g.version&&"autoGainControl"in t.mediaDevices.getSupportedConstraints())){var k=function(q,r,u){r in q&&!(u in q)&&(q[u]=q[r],delete q[r])},n=t.mediaDevices.getUserMedia.bind(t.mediaDevices);t.mediaDevices.getUserMedia=function(q){"object"===e(q)&&"object"===e(q.audio)&&(q=JSON.parse(JSON.stringify(q)),k(q.audio,"autoGainControl","mozAutoGainControl"),k(q.audio,"noiseSuppression","mozNoiseSuppression"));return n(q)};
if(l&&l.prototype.getSettings){var m=l.prototype.getSettings;l.prototype.getSettings=function(){var q=m.apply(this,arguments);k(q,"mozAutoGainControl","autoGainControl");k(q,"mozNoiseSuppression","noiseSuppression");return q}}if(l&&l.prototype.applyConstraints){var p=l.prototype.applyConstraints;l.prototype.applyConstraints=function(q){"audio"===this.kind&&"object"===e(q)&&(q=JSON.parse(JSON.stringify(q)),k(q,"autoGainControl","mozAutoGainControl"),k(q,"noiseSuppression","mozNoiseSuppression"));return p.apply(this,
[q])}}}};var f=function(l,g){if(!g&&l&&l.__esModule)return l;if(null===l||"object"!=e(l)&&"function"!=typeof l)return{"default":l};if((g=d(g))&&g.has(l))return g.get(l);var t={__proto__:null},k=Object.defineProperty&&Object.getOwnPropertyDescriptor,n;for(n in l)if("default"!==n&&{}.hasOwnProperty.call(l,n)){var m=k?Object.getOwnPropertyDescriptor(l,n):null;m&&(m.get||m.set)?Object.defineProperty(t,n,m):t[n]=l[n]}return t["default"]=l,g&&g.set(l,t),t}(a("../utils"))},{"../utils":10}],9:[function(a,
b,c){function d(g){if("function"!=typeof WeakMap)return null;var t=new WeakMap,k=new WeakMap;return(d=function(n){return n?k:t})(g)}function e(g){"@babel/helpers - typeof";return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(g)}function f(g){return g&&void 0!==g.video?Object.assign({},g,{video:l.compactObject(g.video)}):g}Object.defineProperty(c,
"__esModule",{value:!0});c.shimAudioContext=function(g){"object"!==e(g)||g.AudioContext||(g.AudioContext=g.webkitAudioContext)};c.shimCallbacksAPI=function(g){if("object"===e(g)&&g.RTCPeerConnection){g=g.RTCPeerConnection.prototype;var t=g.createOffer,k=g.createAnswer,n=g.setLocalDescription,m=g.setRemoteDescription,p=g.addIceCandidate;g.createOffer=function(r,u){var w=t.apply(this,[2<=arguments.length?arguments[2]:arguments[0]]);if(!u)return w;w.then(r,u);return Promise.resolve()};g.createAnswer=
function(r,u){var w=k.apply(this,[2<=arguments.length?arguments[2]:arguments[0]]);if(!u)return w;w.then(r,u);return Promise.resolve()};var q=function(r,u,w){r=n.apply(this,[r]);if(!w)return r;r.then(u,w);return Promise.resolve()};g.setLocalDescription=q;q=function(r,u,w){r=m.apply(this,[r]);if(!w)return r;r.then(u,w);return Promise.resolve()};g.setRemoteDescription=q;q=function(r,u,w){r=p.apply(this,[r]);if(!w)return r;r.then(u,w);return Promise.resolve()};g.addIceCandidate=q}};c.shimConstraints=
f;c.shimCreateOfferLegacy=function(g){var t=g.RTCPeerConnection.prototype.createOffer;g.RTCPeerConnection.prototype.createOffer=function(k){if(k){"undefined"!==typeof k.offerToReceiveAudio&&(k.offerToReceiveAudio=!!k.offerToReceiveAudio);var n=this.getTransceivers().find(function(m){return"audio"===m.receiver.track.kind});!1===k.offerToReceiveAudio&&n?"sendrecv"===n.direction?n.setDirection?n.setDirection("sendonly"):n.direction="sendonly":"recvonly"===n.direction&&(n.setDirection?n.setDirection("inactive"):
n.direction="inactive"):!0!==k.offerToReceiveAudio||n||this.addTransceiver("audio",{direction:"recvonly"});"undefined"!==typeof k.offerToReceiveVideo&&(k.offerToReceiveVideo=!!k.offerToReceiveVideo);n=this.getTransceivers().find(function(m){return"video"===m.receiver.track.kind});!1===k.offerToReceiveVideo&&n?"sendrecv"===n.direction?n.setDirection?n.setDirection("sendonly"):n.direction="sendonly":"recvonly"===n.direction&&(n.setDirection?n.setDirection("inactive"):n.direction="inactive"):!0!==k.offerToReceiveVideo||
n||this.addTransceiver("video",{direction:"recvonly"})}return t.apply(this,arguments)}};c.shimGetUserMedia=function(g){var t=g&&g.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){g=t.mediaDevices;var k=g.getUserMedia.bind(g);t.mediaDevices.getUserMedia=function(n){return k(f(n))}}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(n,m,p){t.mediaDevices.getUserMedia(n).then(m,p)}.bind(t))};c.shimLocalStreamsAPI=function(g){if("object"===e(g)&&g.RTCPeerConnection){"getLocalStreams"in
g.RTCPeerConnection.prototype||(g.RTCPeerConnection.prototype.getLocalStreams=function(){this._localStreams||(this._localStreams=[]);return this._localStreams});if(!("addStream"in g.RTCPeerConnection.prototype)){var t=g.RTCPeerConnection.prototype.addTrack;g.RTCPeerConnection.prototype.addStream=function(k){var n=this;this._localStreams||(this._localStreams=[]);this._localStreams.includes(k)||this._localStreams.push(k);k.getAudioTracks().forEach(function(m){return t.call(n,m,k)});k.getVideoTracks().forEach(function(m){return t.call(n,
m,k)})};g.RTCPeerConnection.prototype.addTrack=function(k){for(var n=this,m=arguments.length,p=Array(1<m?m-1:0),q=1;q<m;q++)p[q-1]=arguments[q];p&&p.forEach(function(r){n._localStreams?n._localStreams.includes(r)||n._localStreams.push(r):n._localStreams=[r]});return t.apply(this,arguments)}}"removeStream"in g.RTCPeerConnection.prototype||(g.RTCPeerConnection.prototype.removeStream=function(k){var n=this;this._localStreams||(this._localStreams=[]);var m=this._localStreams.indexOf(k);if(-1!==m){this._localStreams.splice(m,
1);var p=k.getTracks();this.getSenders().forEach(function(q){p.includes(q.track)&&n.removeTrack(q)})}})}};c.shimRTCIceServerUrls=function(g){if(g.RTCPeerConnection){var t=g.RTCPeerConnection;g.RTCPeerConnection=function(k,n){if(k&&k.iceServers){for(var m=[],p=0;p<k.iceServers.length;p++){var q=k.iceServers[p];void 0===q.urls&&q.url?(l.deprecated("RTCIceServer.url","RTCIceServer.urls"),q=JSON.parse(JSON.stringify(q)),q.urls=q.url,delete q.url,m.push(q)):m.push(k.iceServers[p])}k.iceServers=m}return new t(k,
n)};g.RTCPeerConnection.prototype=t.prototype;"generateCertificate"in t&&Object.defineProperty(g.RTCPeerConnection,"generateCertificate",{get:function(){return t.generateCertificate}})}};c.shimRemoteStreamsAPI=function(g){if("object"===e(g)&&g.RTCPeerConnection&&("getRemoteStreams"in g.RTCPeerConnection.prototype||(g.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in g.RTCPeerConnection.prototype))){Object.defineProperty(g.RTCPeerConnection.prototype,
"onaddstream",{get:function(){return this._onaddstream},set:function(k){var n=this;this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly));this.addEventListener("addstream",this._onaddstream=k);this.addEventListener("track",this._onaddstreampoly=function(m){m.streams.forEach(function(p){n._remoteStreams||(n._remoteStreams=[]);if(!n._remoteStreams.includes(p)){n._remoteStreams.push(p);var q=new Event("addstream");q.stream=
p;n.dispatchEvent(q)}})})}});var t=g.RTCPeerConnection.prototype.setRemoteDescription;g.RTCPeerConnection.prototype.setRemoteDescription=function(){var k=this;this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(n){n.streams.forEach(function(m){k._remoteStreams||(k._remoteStreams=[]);if(!(0<=k._remoteStreams.indexOf(m))){k._remoteStreams.push(m);var p=new Event("addstream");p.stream=m;k.dispatchEvent(p)}})});return t.apply(k,arguments)}}};c.shimTrackEventTransceiver=
function(g){"object"===e(g)&&g.RTCTrackEvent&&"receiver"in g.RTCTrackEvent.prototype&&!("transceiver"in g.RTCTrackEvent.prototype)&&Object.defineProperty(g.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})};var l=function(g,t){if(!t&&g&&g.__esModule)return g;if(null===g||"object"!=e(g)&&"function"!=typeof g)return{"default":g};if((t=d(t))&&t.has(g))return t.get(g);var k={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor,m;for(m in g)if("default"!==
m&&{}.hasOwnProperty.call(g,m)){var p=n?Object.getOwnPropertyDescriptor(g,m):null;p&&(p.get||p.set)?Object.defineProperty(k,m,p):k[m]=g[m]}return k["default"]=g,t&&t.set(g,k),k}(a("../utils"))},{"../utils":10}],10:[function(a,b,c){function d(k){"@babel/helpers - typeof";return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},d(k)}function e(k,n,
m){return(k=k.match(n))&&k.length>=m&&parseInt(k[m],10)}function f(k){return"[object Object]"!==Object.prototype.toString.call(k)?k:Object.keys(k).reduce(function(n,m){var p="[object Object]"===Object.prototype.toString.call(k[m]),q=p?f(k[m]):k[m];p=p&&!Object.keys(q).length;if(void 0===q||p)return n;p=Object;var r=p.assign,u={};a:if("object"==d(m)&&m){var w=m[Symbol.toPrimitive];if(void 0!==w){m=w.call(m,"string");if("object"!=d(m))break a;throw new TypeError("@@toPrimitive must return a primitive value.");
}m=String(m)}m="symbol"==d(m)?m:m+"";m in u?Object.defineProperty(u,m,{value:q,enumerable:!0,configurable:!0,writable:!0}):u[m]=q;return r.call(p,n,u)},{})}function l(k,n,m){n&&!m.has(n.id)&&(m.set(n.id,n),Object.keys(n).forEach(function(p){p.endsWith("Id")?l(k,k.get(n[p]),m):p.endsWith("Ids")&&n[p].forEach(function(q){l(k,k.get(q),m)})}))}Object.defineProperty(c,"__esModule",{value:!0});c.compactObject=f;c.deprecated=function(k,n){t&&console.warn(k+" is deprecated, please use "+n+" instead.")};c.detectBrowser=
function(k){var n={browser:null,version:null};if("undefined"===typeof k||!k.navigator||!k.navigator.userAgent)return n.browser="Not a browser.",n;var m=k.navigator;if(m.userAgentData&&m.userAgentData.brands){var p=m.userAgentData.brands.find(function(q){return"Chromium"===q.brand});if(p)return{browser:"chrome",version:parseInt(p.version,10)}}m.mozGetUserMedia?(n.browser="firefox",n.version=e(m.userAgent,/Firefox\/(\d+)\./,1)):m.webkitGetUserMedia||!1===k.isSecureContext&&k.webkitRTCPeerConnection?
(n.browser="chrome",n.version=e(m.userAgent,/Chrom(e|ium)\/(\d+)\./,2)):k.RTCPeerConnection&&m.userAgent.match(/AppleWebKit\/(\d+)\./)?(n.browser="safari",n.version=e(m.userAgent,/AppleWebKit\/(\d+)\./,1),n.supportsUnifiedPlan=k.RTCRtpTransceiver&&"currentDirection"in k.RTCRtpTransceiver.prototype):n.browser="Not a supported browser.";return n};c.disableLog=function(k){return"boolean"!==typeof k?Error("Argument type: "+d(k)+". Please use a boolean."):(g=k)?"adapter.js logging disabled":"adapter.js logging enabled"};
c.disableWarnings=function(k){if("boolean"!==typeof k)return Error("Argument type: "+d(k)+". Please use a boolean.");t=!k;return"adapter.js deprecation warnings "+(k?"disabled":"enabled")};c.extractVersion=e;c.filterStats=function(k,n,m){var p=m?"outbound-rtp":"inbound-rtp",q=new Map;if(null===n)return q;var r=[];k.forEach(function(u){"track"===u.type&&u.trackIdentifier===n.id&&r.push(u)});r.forEach(function(u){k.forEach(function(w){w.type===p&&w.trackId===u.id&&l(k,w,q)})});return q};c.log=function(){"object"!==
("undefined"===typeof window?"undefined":d(window))||g||"undefined"!==typeof console&&"function"===typeof console.log&&console.log.apply(console,arguments)};c.walkStats=l;c.wrapPeerConnectionEvent=function(k,n,m){if(k.RTCPeerConnection){k=k.RTCPeerConnection.prototype;var p=k.addEventListener;k.addEventListener=function(r,u){if(r!==n)return p.apply(this,arguments);var w=function(C){(C=m(C))&&(u.handleEvent?u.handleEvent(C):u(C))};this._eventMap=this._eventMap||{};this._eventMap[n]||(this._eventMap[n]=
new Map);this._eventMap[n].set(u,w);return p.apply(this,[r,w])};var q=k.removeEventListener;k.removeEventListener=function(r,u){if(r!==n||!this._eventMap||!this._eventMap[n]||!this._eventMap[n].has(u))return q.apply(this,arguments);var w=this._eventMap[n].get(u);this._eventMap[n]["delete"](u);0===this._eventMap[n].size&&delete this._eventMap[n];0===Object.keys(this._eventMap).length&&delete this._eventMap;return q.apply(this,[r,w])};Object.defineProperty(k,"on"+n,{get:function(){return this["_on"+
n]},set:function(r){this["_on"+n]&&(this.removeEventListener(n,this["_on"+n]),delete this["_on"+n]);r&&this.addEventListener(n,this["_on"+n]=r)},enumerable:!0,configurable:!0})}};var g=!0,t=!0},{}],11:[function(a,b,c){var d="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(f){return typeof f}:function(f){return f&&"function"===typeof Symbol&&f.constructor===Symbol&&f!==Symbol.prototype?"symbol":typeof f},e={generateIdentifier:function(){const f=new Uint8Array(8);crypto.getRandomValues(f);
return Array.from(f).map(l=>l.toString(36).padStart(2,"0")).join("").substring(2,12)}};e.localCName=e.generateIdentifier();e.splitLines=function(f){return f.trim().split("\n").map(function(l){return l.trim()})};e.splitSections=function(f){return f.split("\nm=").map(function(l,g){return(0<g?"m="+l:l).trim()+"\r\n"})};e.getDescription=function(f){return(f=e.splitSections(f))&&f[0]};e.getMediaSections=function(f){f=e.splitSections(f);f.shift();return f};e.matchPrefix=function(f,l){return e.splitLines(f).filter(function(g){return 0===
g.indexOf(l)})};e.parseCandidate=function(f){f=0===f.indexOf("a=candidate:")?f.substring(12).split(" "):f.substring(10).split(" ");for(var l={foundation:f[0],component:{1:"rtp",2:"rtcp"}[f[1]]||f[1],protocol:f[2].toLowerCase(),priority:parseInt(f[3],10),ip:f[4],address:f[4],port:parseInt(f[5],10),type:f[7]},g=8;g<f.length;g+=2)switch(f[g]){case "raddr":l.relatedAddress=f[g+1];break;case "rport":l.relatedPort=parseInt(f[g+1],10);break;case "tcptype":l.tcpType=f[g+1];break;case "ufrag":l.ufrag=f[g+
1];l.usernameFragment=f[g+1];break;default:void 0===l[f[g]]&&(l[f[g]]=f[g+1])}return l};e.writeCandidate=function(f){var l=[];l.push(f.foundation);var g=f.component;"rtp"===g?l.push(1):"rtcp"===g?l.push(2):l.push(g);l.push(f.protocol.toUpperCase());l.push(f.priority);l.push(f.address||f.ip);l.push(f.port);g=f.type;l.push("typ");l.push(g);"host"!==g&&f.relatedAddress&&f.relatedPort&&(l.push("raddr"),l.push(f.relatedAddress),l.push("rport"),l.push(f.relatedPort));f.tcpType&&"tcp"===f.protocol.toLowerCase()&&
(l.push("tcptype"),l.push(f.tcpType));if(f.usernameFragment||f.ufrag)l.push("ufrag"),l.push(f.usernameFragment||f.ufrag);return"candidate:"+l.join(" ")};e.parseIceOptions=function(f){return f.substring(14).split(" ")};e.parseRtpMap=function(f){f=f.substring(9).split(" ");var l={payloadType:parseInt(f.shift(),10)};f=f[0].split("/");l.name=f[0];l.clockRate=parseInt(f[1],10);l.channels=3===f.length?parseInt(f[2],10):1;l.numChannels=l.channels;return l};e.writeRtpMap=function(f){var l=f.payloadType;void 0!==
f.preferredPayloadType&&(l=f.preferredPayloadType);var g=f.channels||f.numChannels||1;return"a=rtpmap:"+l+" "+f.name+"/"+f.clockRate+(1!==g?"/"+g:"")+"\r\n"};e.parseExtmap=function(f){f=f.substring(9).split(" ");return{id:parseInt(f[0],10),direction:0<f[0].indexOf("/")?f[0].split("/")[1]:"sendrecv",uri:f[1],attributes:f.slice(2).join(" ")}};e.writeExtmap=function(f){return"a=extmap:"+(f.id||f.preferredId)+(f.direction&&"sendrecv"!==f.direction?"/"+f.direction:"")+" "+f.uri+(f.attributes?" "+f.attributes:
"")+"\r\n"};e.parseFmtp=function(f){for(var l={},g=f.substring(f.indexOf(" ")+1).split(";"),t=0;t<g.length;t++)f=g[t].trim().split("="),l[f[0].trim()]=f[1];return l};e.writeFmtp=function(f){var l="",g=f.payloadType;void 0!==f.preferredPayloadType&&(g=f.preferredPayloadType);if(f.parameters&&Object.keys(f.parameters).length){var t=[];Object.keys(f.parameters).forEach(function(k){void 0!==f.parameters[k]?t.push(k+"="+f.parameters[k]):t.push(k)});l+="a=fmtp:"+g+" "+t.join(";")+"\r\n"}return l};e.parseRtcpFb=
function(f){f=f.substring(f.indexOf(" ")+1).split(" ");return{type:f.shift(),parameter:f.join(" ")}};e.writeRtcpFb=function(f){var l="",g=f.payloadType;void 0!==f.preferredPayloadType&&(g=f.preferredPayloadType);f.rtcpFeedback&&f.rtcpFeedback.length&&f.rtcpFeedback.forEach(function(t){l+="a=rtcp-fb:"+g+" "+t.type+(t.parameter&&t.parameter.length?" "+t.parameter:"")+"\r\n"});return l};e.parseSsrcMedia=function(f){var l=f.indexOf(" "),g={ssrc:parseInt(f.substring(7,l),10)},t=f.indexOf(":",l);-1<t?(g.attribute=
f.substring(l+1,t),g.value=f.substring(t+1)):g.attribute=f.substring(l+1);return g};e.parseSsrcGroup=function(f){f=f.substring(13).split(" ");return{semantics:f.shift(),ssrcs:f.map(function(l){return parseInt(l,10)})}};e.getMid=function(f){if(f=e.matchPrefix(f,"a=mid:")[0])return f.substring(6)};e.parseFingerprint=function(f){f=f.substring(14).split(" ");return{algorithm:f[0].toLowerCase(),value:f[1].toUpperCase()}};e.getDtlsParameters=function(f,l){return{role:"auto",fingerprints:e.matchPrefix(f+
l,"a=fingerprint:").map(e.parseFingerprint)}};e.writeDtlsParameters=function(f,l){var g="a=setup:"+l+"\r\n";f.fingerprints.forEach(function(t){g+="a=fingerprint:"+t.algorithm+" "+t.value+"\r\n"});return g};e.parseCryptoLine=function(f){f=f.substring(9).split(" ");return{tag:parseInt(f[0],10),cryptoSuite:f[1],keyParams:f[2],sessionParams:f.slice(3)}};e.writeCryptoLine=function(f){return"a=crypto:"+f.tag+" "+f.cryptoSuite+" "+("object"===d(f.keyParams)?e.writeCryptoKeyParams(f.keyParams):f.keyParams)+
(f.sessionParams?" "+f.sessionParams.join(" "):"")+"\r\n"};e.parseCryptoKeyParams=function(f){if(0!==f.indexOf("inline:"))return null;f=f.substring(7).split("|");return{keyMethod:"inline",keySalt:f[0],lifeTime:f[1],mkiValue:f[2]?f[2].split(":")[0]:void 0,mkiLength:f[2]?f[2].split(":")[1]:void 0}};e.writeCryptoKeyParams=function(f){return f.keyMethod+":"+f.keySalt+(f.lifeTime?"|"+f.lifeTime:"")+(f.mkiValue&&f.mkiLength?"|"+f.mkiValue+":"+f.mkiLength:"")};e.getCryptoParameters=function(f,l){return e.matchPrefix(f+
l,"a=crypto:").map(e.parseCryptoLine)};e.getIceParameters=function(f,l){var g=e.matchPrefix(f+l,"a=ice-ufrag:")[0];f=e.matchPrefix(f+l,"a=ice-pwd:")[0];return g&&f?{usernameFragment:g.substring(12),password:f.substring(10)}:null};e.writeIceParameters=function(f){var l="a=ice-ufrag:"+f.usernameFragment+"\r\na=ice-pwd:"+f.password+"\r\n";f.iceLite&&(l+="a=ice-lite\r\n");return l};e.parseRtpParameters=function(f){var l={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},g=e.splitLines(f)[0].split(" ");
l.profile=g[2];for(var t=3;t<g.length;t++){var k=g[t],n=e.matchPrefix(f,"a=rtpmap:"+k+" ")[0];if(n){n=e.parseRtpMap(n);var m=e.matchPrefix(f,"a=fmtp:"+k+" ");n.parameters=m.length?e.parseFmtp(m[0]):{};n.rtcpFeedback=e.matchPrefix(f,"a=rtcp-fb:"+k+" ").map(e.parseRtcpFb);l.codecs.push(n);switch(n.name.toUpperCase()){case "RED":case "ULPFEC":l.fecMechanisms.push(n.name.toUpperCase())}}}e.matchPrefix(f,"a=extmap:").forEach(function(q){l.headerExtensions.push(e.parseExtmap(q))});var p=e.matchPrefix(f,
"a=rtcp-fb:* ").map(e.parseRtcpFb);l.codecs.forEach(function(q){p.forEach(function(r){q.rtcpFeedback.find(function(u){return u.type===r.type&&u.parameter===r.parameter})||q.rtcpFeedback.push(r)})});return l};e.writeRtpDescription=function(f,l){var g="";g+="m="+f+" ";g+=0<l.codecs.length?"9":"0";g+=" "+(l.profile||"UDP/TLS/RTP/SAVPF")+" ";g+=l.codecs.map(function(k){return void 0!==k.preferredPayloadType?k.preferredPayloadType:k.payloadType}).join(" ")+"\r\n";g+="c=IN IP4 0.0.0.0\r\n";g+="a=rtcp:9 IN IP4 0.0.0.0\r\n";
l.codecs.forEach(function(k){g+=e.writeRtpMap(k);g+=e.writeFmtp(k);g+=e.writeRtcpFb(k)});var t=0;l.codecs.forEach(function(k){k.maxptime>t&&(t=k.maxptime)});0<t&&(g+="a=maxptime:"+t+"\r\n");l.headerExtensions&&l.headerExtensions.forEach(function(k){g+=e.writeExtmap(k)});return g};e.parseRtpEncodingParameters=function(f){var l=[],g=e.parseRtpParameters(f),t=-1!==g.fecMechanisms.indexOf("RED"),k=-1!==g.fecMechanisms.indexOf("ULPFEC"),n=e.matchPrefix(f,"a=ssrc:").map(function(r){return e.parseSsrcMedia(r)}).filter(function(r){return"cname"===
r.attribute}),m=0<n.length&&n[0].ssrc,p=void 0;n=e.matchPrefix(f,"a=ssrc-group:FID").map(function(r){return r.substring(17).split(" ").map(function(u){return parseInt(u,10)})});0<n.length&&1<n[0].length&&n[0][0]===m&&(p=n[0][1]);g.codecs.forEach(function(r){"RTX"===r.name.toUpperCase()&&r.parameters.apt&&(r={ssrc:m,codecPayloadType:parseInt(r.parameters.apt,10)},m&&p&&(r.rtx={ssrc:p}),l.push(r),t&&(r=JSON.parse(JSON.stringify(r)),r.fec={ssrc:m,mechanism:k?"red+ulpfec":"red"},l.push(r)))});0===l.length&&
m&&l.push({ssrc:m});var q=e.matchPrefix(f,"b=");q.length&&(q=0===q[0].indexOf("b=TIAS:")?parseInt(q[0].substring(7),10):0===q[0].indexOf("b=AS:")?950*parseInt(q[0].substring(5),10)-16E3:void 0,l.forEach(function(r){r.maxBitrate=q}));return l};e.parseRtcpParameters=function(f){var l={},g=e.matchPrefix(f,"a=ssrc:").map(function(t){return e.parseSsrcMedia(t)}).filter(function(t){return"cname"===t.attribute})[0];g&&(l.cname=g.value,l.ssrc=g.ssrc);g=e.matchPrefix(f,"a=rtcp-rsize");l.reducedSize=0<g.length;
l.compound=0===g.length;f=e.matchPrefix(f,"a=rtcp-mux");l.mux=0<f.length;return l};e.writeRtcpParameters=function(f){var l="";f.reducedSize&&(l+="a=rtcp-rsize\r\n");f.mux&&(l+="a=rtcp-mux\r\n");void 0!==f.ssrc&&f.cname&&(l+="a=ssrc:"+f.ssrc+" cname:"+f.cname+"\r\n");return l};e.parseMsid=function(f){var l=void 0;l=e.matchPrefix(f,"a=msid:");if(1===l.length)return l=l[0].substring(7).split(" "),{stream:l[0],track:l[1]};f=e.matchPrefix(f,"a=ssrc:").map(function(g){return e.parseSsrcMedia(g)}).filter(function(g){return"msid"===
g.attribute});if(0<f.length)return l=f[0].value.split(" "),{stream:l[0],track:l[1]}};e.parseSctpDescription=function(f){var l=e.parseMLine(f),g=e.matchPrefix(f,"a=max-message-size:"),t=void 0;0<g.length&&(t=parseInt(g[0].substring(19),10));isNaN(t)&&(t=65536);g=e.matchPrefix(f,"a=sctp-port:");if(0<g.length)return{port:parseInt(g[0].substring(12),10),protocol:l.fmt,maxMessageSize:t};f=e.matchPrefix(f,"a=sctpmap:");if(0<f.length)return f=f[0].substring(10).split(" "),{port:parseInt(f[0],10),protocol:f[1],
maxMessageSize:t}};e.writeSctpDescription=function(f,l){f="DTLS/SCTP"!==f.protocol?["m="+f.kind+" 9 "+f.protocol+" "+l.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+l.port+"\r\n"]:["m="+f.kind+" 9 "+f.protocol+" "+l.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+l.port+" "+l.protocol+" 65535\r\n"];void 0!==l.maxMessageSize&&f.push("a=max-message-size:"+l.maxMessageSize+"\r\n");return f.join("")};e.generateSessionId=function(){const f=new Uint8Array(12);crypto.getRandomValues(f);return Array.from(f).map(l=>
l.toString(10).padStart(3,"0")).join("").substr(0,22)};e.writeSessionBoilerplate=function(f,l,g){l=void 0!==l?l:2;f=f?f:e.generateSessionId();return"v=0\r\no="+(g||"thisisadapterortc")+" "+f+" "+l+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"};e.getDirection=function(f,l){f=e.splitLines(f);for(var g=0;g<f.length;g++)switch(f[g]){case "a=sendrecv":case "a=sendonly":case "a=recvonly":case "a=inactive":return f[g].substring(2)}return l?e.getDirection(l):"sendrecv"};e.getKind=function(f){return e.splitLines(f)[0].split(" ")[0].substring(2)};
e.isRejected=function(f){return"0"===f.split(" ",2)[1]};e.parseMLine=function(f){f=e.splitLines(f)[0].substring(2).split(" ");return{kind:f[0],port:parseInt(f[1],10),protocol:f[2],fmt:f.slice(3).join(" ")}};e.parseOLine=function(f){f=e.matchPrefix(f,"o=")[0].substring(2).split(" ");return{username:f[0],sessionId:f[1],sessionVersion:parseInt(f[2],10),netType:f[3],addressType:f[4],address:f[5]}};e.isValidSDP=function(f){if("string"!==typeof f||0===f.length)return!1;f=e.splitLines(f);for(var l=0;l<f.length;l++)if(2>
f[l].length||"="!==f[l].charAt(1))return!1;return!0};"object"===("undefined"===typeof b?"undefined":d(b))&&(b.exports=e)},{}]},{},[1])(1)});class Xb{constructor(a){this.FrameDisplayDeltaTimeMs=this.BrowserReceiptTimeMs=this.UETransmissionTimeMs=this.UECaptureToSendMs=this.UEEncodeMs=this.UEReceiptTimeMs=this.TestStartTimeMs=null;this.onReady=a}reset(){this.FrameDisplayDeltaTimeMs=this.BrowserReceiptTimeMs=this.UETransmissionTimeMs=this.UEEncodeMs=this.UECaptureToSendMs=this.UEReceiptTimeMs=this.TestStartTimeMs=
null}start(){this.reset();this.TestStartTimeMs=Date.now();if(this.onReady)this.onReady()}setUETimings(a){this.UEReceiptTimeMs=a.ReceiptTimeMs;this.UEEncodeMs=a.EncodeMs;this.UECaptureToSendMs=a.CaptureToSendMs;this.UETransmissionTimeMs=a.TransmissionTimeMs;this.BrowserReceiptTimeMs=Date.now();this.latencyTimingsReady()}setFrameDisplayDeltaTime(a){null==this.FrameDisplayDeltaTimeMs&&(this.FrameDisplayDeltaTimeMs=Math.round(a),this.latencyTimingsReady())}latencyTimingsReady(){if(this.BrowserReceiptTimeMs){var a=
this.BrowserReceiptTimeMs-this.TestStartTimeMs,b=this.UECaptureToSendMs,c=this.UETransmissionTimeMs-this.UEReceiptTimeMs,d=a-c,e={latencyExcludingDecode:a,encodeLatency:this.UEEncodeMs.toFixed(1),uePixelStreamLatency:b.toFixed(1),ueTestDuration:c.toFixed(1),networkLatency:d.toFixed(1)};this.FrameDisplayDeltaTimeMs&&this.BrowserReceiptTimeMs&&(e.endToEndLatency=this.FrameDisplayDeltaTimeMs+d+("string"===typeof b?0:b),e.browserSideLatency=this.FrameDisplayDeltaTimeMs+(a-d-c),e.endToEndLatency=e.endToEndLatency.toFixed(1),
e.browserSideLatency=e.browserSideLatency.toFixed(1));if(this.onReady)this.onReady(e)}}}class Yb{constructor(a,b){this.useStats=a.useStats;this.events=a.events;this.webRtcConfig=a.peerConnectionOptions||{};this.webRtcConfig.sdpSemantics="unified-plan";this.tnClient=this.dcClient=this.pcClient=null;this.sdpConstraints={offerToReceiveAudio:0,offerToReceiveVideo:1,voiceActivityDetection:!1};this.dataChannelOptions={ordered:!0};this.lastStats={};this.playerId=0;this.latencyTestTimings=new Xb(this.events.onLatencyTimingsReady);
this.api_isSending=!1;this.api_queue=[];this.player=b;this.api_enableLog=a.enableApiCallLog;this.log=(c,d,e)=>this.player.getAPI().log(c,d,e)}setVideo(a){this.video=a}logStartupInfo(a){if(this.events.onlog)this.events.onlog(a)}logApiCall(a,b,c){this.api_enableLog&&(a&&""!=a?(a=`[API] ${da()} ${a}`,this.log(a,b,c)):this.log(""))}logPerfTest(a,b,c){a&&""!=a?this.log(`[PerfTest] ${da()} ${a}`,b,c):this.log("")}setupPeerConnection(){this.pcClient||(this.pcClient=new RTCPeerConnection(this.webRtcConfig),
this.pcClient.addTransceiver("video",{direction:"recvonly"}),this.pcClient.onsignalingstatechange=a=>this.onStateChanged(a),this.pcClient.oniceconnectionstatechange=a=>this.onStateChanged(a),this.pcClient.onicegatheringstatechange=a=>this.onStateChanged(a),this.pcClient.ontrack=a=>this.events.ontrack(a),this.pcClient.onicecandidate=a=>this.events.onicecandidate(a),this.pcClient.ondatachannel=a=>{this.dcClient=a.channel;this.dcClient.bufferedAmountLowThreshold=this.lowWaterMark;this.setupDataChannelCallbacks()},
this.lowWaterMark=this.chunkSize=this.pcClient.sctp?Math.min(this.pcClient.sctp.maxMessageSize,65536):65536,this.highWaterMark=Math.max(8*this.chunkSize,1048576),this.headerOffset=4,this.lenPerChunk=Math.floor((this.chunkSize-this.headerOffset)/2),this.log("Chunk Size: "+this.chunkSize),this.log("Buffer Threshold Low: "+this.lowWaterMark),this.log("Buffer Threshold High: "+this.highWaterMark))}close(){this.dcClient&&"open"==this.dcClient.readyState&&(this.dcClient.close(),this.dcClient=null);this.pcClient&&
(this.pcClient.close(),this.pcClient=null);this.clearStatsTimer()}setupDataChannelCallbacks(){this.dcClient.binaryType="arraybuffer";this.dcClient.onopen=a=>{this.isDatachannelOpened=!0;if(this.events&&this.events.ondatachannelopen)this.events.ondatachannelopen(a);this.logStartupInfo("video: loading...");for(let b of[81,87,69,82,84,65,83,68,70])this.send((new Uint8Array([61,b])).buffer)};this.dcClient.onclose=a=>{this.isDatachannelOpened=!1;this.clearStatsTimer();if(this.events&&this.events.ondatachannelclose)this.events.ondatachannelclose(a)};
this.dcClient.onerror=a=>{this.isDatachannelOpened=!1;this.logStartupInfo("channel error");if(this.events&&this.events.ondatachannelerror)this.events.ondatachannelerror(a)};this.dcClient.onmessage=a=>this.onDatachannelMessage(a)}createOffer(){this.close();this.setupPeerConnection();try{this.dcClient=this.pcClient.createDataChannel("cirrus",this.dataChannelOptions),this.dcClient.bufferedAmountLowThreshold=this.lowWaterMark,this.setupDataChannelCallbacks(),this.logStartupInfo("channel created")}catch(a){this.logStartupInfo("no channel"),
this.dcClient=null}this.pcClient.createOffer(this.sdpConstraints).then(a=>{a.sdp=a.sdp.replace("useinbandfec=1","useinbandfec=1;stereo=1;sprop-maxcapturerate=48000");this.pcClient.setLocalDescription(a);this.events.onOfferCreated(a)},()=>{this.logStartupInfo("couldn't create offer")})}onReceiveOffer(a){a=new RTCSessionDescription(a);this.setupPeerConnection();this.pcClient.setRemoteDescription(a).then(()=>{this.pcClient.createAnswer().then(b=>this.pcClient.setLocalDescription(b)).then(()=>{if(this.events.onAnswerCreated)this.events.onAnswerCreated(this.pcClient.currentLocalDescription)}).then(()=>
{let b=this.pcClient.getReceivers();for(let c of b)c.playoutDelayHint=0}).catch(b=>console.error("createAnswer() failed:",b))});this.setupLiveStats()}onIceCandidate(a){a=new RTCIceCandidate(a);this.pcClient.addIceCandidate(a).then(b=>{this.logStartupInfo("candidate added")})}onReceiveAnswer(a){this.playerId=a.playerId;this.logStartupInfo(`received answer: ${this.playerId}`);a=new RTCSessionDescription(a);this.pcClient.setRemoteDescription(a);a=this.pcClient.getReceivers();for(let b of a)b.playoutDelayHint=
0;this.setupLiveStats()}setupLiveStats(){this.clearStatsTimer();this.useStats&&!this.aggregateStatsIntervalId&&(this.aggregateStatsIntervalId=setInterval(()=>{this.pcClient&&this.pcClient.getStats(null).then(a=>this.processLiveStats(a))},1E3))}onStateChanged(a){this.signalingState!=this.pcClient.signalingState&&(this.signalingState=this.pcClient.signalingState,this.logStartupInfo(`__signaling: ${this.signalingState}`));this.iceConnectionState!=this.pcClient.iceConnectionState&&(this.iceConnectionState=
this.pcClient.iceConnectionState,this.logStartupInfo(`__ice_connection: ${this.iceConnectionState}`),"disconnected"!=this.iceConnectionState||this.isDatachannelOpened||this.logStartupInfo("\u8fde\u63a5\u5efa\u7acb\u5931\u8d25\uff0c\u8bf7\u68c0\u67e5\u670d\u52a1\u5668\u7aef\u53e3\u8bbe\u7f6e"),"failed"!=this.iceConnectionState||this.isDatachannelOpened||this.logStartupInfo("\u8fde\u63a5\u5efa\u7acb\u5931\u8d25\uff0c\u65e0\u53ef\u7528\u7684\u5019\u9009\u8fde\u63a5"));this.iceGatheringState!=this.pcClient.iceGatheringState&&
(this.iceGatheringState=this.pcClient.iceGatheringState,this.logStartupInfo(`__ice_gathering: ${this.iceGatheringState}`))}clearStatsTimer(){this.aggregateStatsIntervalId&&(clearInterval(this.aggregateStatsIntervalId),this.aggregateStatsIntervalId=void 0)}send(a){return this.dcClient&&"open"==this.dcClient.readyState?(this.dcClient.send(a),!0):!1}requestInitialSettings(){this.send((new Uint8Array([7])).buffer)}requestQualityControl(){this.send((new Uint8Array([1])).buffer)}sendEnableIntervalSendQP(a){this.send((new Uint8Array([11,
a?1:0])).buffer)}sendEnableReceiveEvents(a){this.send((new Uint8Array([12,a?1:0])).buffer)}_createApiBinBuffer(a,b,c){let d=new DataView(new ArrayBuffer(this.headerOffset+2*b)),e=0;d.setUint8(e,52);e++;d.setUint8(e,a);e++;d.setUint16(e,b,!0);e+=2;for(a=0;a<c.length;a++)d.setUint16(e,c.charCodeAt(a),!0),e+=2;return d.buffer}_pushApiToSendingQueue(a){if("object"==typeof a){a.__playerId=this.playerId;var b=JSON.stringify(a);a=b.length;if(a>this.lenPerChunk){let d=Math.ceil(a/this.lenPerChunk);for(let e=
0;e<d;e++){var c=0==e?1:e==d-1?3:2;let f=e==d-1?a-e*this.lenPerChunk:this.lenPerChunk,l=b.substr(e*this.lenPerChunk,f);c=this._createApiBinBuffer(c,f,l);this.api_queue.push(c)}this.logApiCall(`Push ${d} chunks, length: ${a}`)}else b=this._createApiBinBuffer(0,a,b),this.api_queue.push(b),this.logApiCall(`Push 1 chunk, length: ${a}`)}}_sendApiFromQueue(){this.api_isSending=!0;this.api_timeoutHandle&&(clearTimeout(this.api_timeoutHandle),this.api_timeoutHandle=null);let a=this.dcClient.bufferedAmount;
for(;0<this.api_queue.length;){let b=this.api_queue.shift();this.logApiCall(`Sending data begin, size: ${b.byteLength}, queue: ${this.api_queue.length}`,!1,"gray");this.dcClient.send(b);a+=this.chunkSize;if(a>=this.highWaterMark){this.dcClient.bufferedAmount<this.lowWaterMark&&(this.api_timeoutHandle=setTimeout(()=>{0==this.api_queue.length?(this.api_isSending=!1,this.logApiCall("Finished!")):(this.logApiCall("continue send after timeout."),this._sendApiFromQueue())},0));this.logApiCall(`-------paused------- queue: ${this.api_queue.length}, bufferedAmount: ${a}, highWaterMark: ${this.highWaterMark}`);
return}}this.api_isSending=!1;this.logApiCall("Finished!")}sendApi(a){this.logApiCall();this.logApiCall(`START: ${a?a.command:"Unkown"}`);this.dcClient&&"open"==this.dcClient.readyState?(this._pushApiToSendingQueue(a),this.dcClient.onbufferedamountlow=b=>{this.logApiCall(`-------bufferedamountlow------- queue: ${this.api_queue.length}`);0==this.api_queue.length?(this.api_isSending=!1,this.logApiCall("Finished!")):(this.logApiCall("continue send after bufferedamountlow."),this._sendApiFromQueue())},
this.api_isSending?this.logApiCall("Waiting..."):this._sendApiFromQueue()):this.logApiCall("Data channel is invalid!")}perfTest(a){this.api_isSending?this.logPerfTest("Please wait until the API call is completed before testing!",!1,"red"):(this.pt_finished=!1,this.pt_bytesToSend=1048576*a,this.pt_timeoutHandle=null,this.pt_sended=this.pt_sendStartTime=this.pt_maxTimeUsedInSend=this.pt_numberOfSendCalls=this.pt_totalTimeUsedInSend=0,this.logPerfTest(),this.logPerfTest(`Performence Testing Starting: ${a}MB (${1048576*
a})`,!1,"blue"),this.logPerfTest("Chunk Size: "+this.chunkSize),this.logPerfTest("Buffer Threshold Low: "+this.lowWaterMark),this.logPerfTest("Buffer Threshold High: "+this.highWaterMark),this.dcClient.onbufferedamountlow=b=>{this.logPerfTest(`[bufferedamountlow] sendCalls: ${this.pt_numberOfSendCalls}, sended: ${this.pt_sended}, bufferedAmount: ${this.dcClient.bufferedAmount}`,!1,"gray");this.pt_finished||this.pt_send()},this.pt_create_data=()=>{var b=new Date;b=b.toLocaleString()+"."+b.getMilliseconds();
let c=new DataView(new ArrayBuffer(this.chunkSize)),d=0;c.setUint8(d,53);d++;c.setUint16(d,b.length,!0);d+=2;for(let e=0;e<b.length;e++)c.setUint16(d,b.charCodeAt(e),!0),d+=2;return c.buffer},this.pt_send=()=>{null!==this.pt_timeoutHandle&&(clearTimeout(this.pt_timeoutHandle),this.pt_timeoutHandle=null);for(var b=this.dcClient.bufferedAmount;this.pt_sended<this.pt_bytesToSend;){var c=performance.now();this.dcClient.send(this.pt_create_data());c=performance.now()-c;this.pt_totalTimeUsedInSend+=c;c>
this.pt_maxTimeUsedInSend&&(this.pt_maxTimeUsedInSend=c);this.pt_numberOfSendCalls+=1;b+=this.chunkSize;this.pt_sended+=this.chunkSize;if(b>=this.highWaterMark){this.dcClient.bufferedAmount<this.lowWaterMark&&(this.pt_timeoutHandle=setTimeout(()=>sendData(),0));this.logPerfTest(`[-------paused-------] sendCalls: ${this.pt_numberOfSendCalls}, sended: ${this.pt_sended}, ${b} / ${this.dcClient.bufferedAmount}`,!1,"darkgray");break}}this.pt_sended===this.pt_bytesToSend&&(this.pt_finished=!0,this.logPerfTest("Data transfer completed successfully!",
!1,"blue"),this.logPerfTest("      Total sended: "+this.pt_sended),this.logPerfTest("      Average time spent in send() (ms): "+this.pt_totalTimeUsedInSend/this.pt_numberOfSendCalls),this.logPerfTest("      Max time spent in send() (ms): "+this.pt_maxTimeUsedInSend),b=performance.now()-this.pt_sendStartTime,this.logPerfTest("      Total time spent: "+b),this.logPerfTest("      MBytes/Sec: "+this.pt_bytesToSend/1E3/b))},this.logPerfTest("Start sending data..."),this.pt_sendStartTime=performance.now(),
this.pt_sended=this.pt_numberOfSendCalls=this.pt_totalTimeUsedInSend=this.pt_maxTimeUsedInSend=0,this.pt_send())}sendDescriptor(a,b){b=JSON.stringify(b);let c=new DataView(new ArrayBuffer(3+2*b.length)),d=0;c.setUint8(d,a);d++;c.setUint16(d,b.length,!0);d+=2;for(a=0;a<b.length;a++)c.setUint16(d,b.charCodeAt(a),!0),d+=2;this.send(c.buffer)}updateRenderResolution(a,b){a={Console:"setres "+a+"x"+b+"w"};b=JSON.stringify(a);console.log(b);let c,d;null==(c=this.player)||null==(d=c.eventsPanel)||d.appendText(b);
this.sendDescriptor(50,a)}updateParams(a){a.encodeMaxQP&&this.sendDescriptor(50,{Console:"PixelStreaming.Encoder.MaxQP "+a.encodeMaxQP});a.keyframeInterval&&this.sendDescriptor(50,{Console:"PixelStreaming.Encoder.KeyframeInterval "+a.keyframeInterval});a.maxBitrate&&this.sendDescriptor(50,{Console:"PixelStreaming.WebRTC.MaxBitrate "+a.maxBitrate})}processLiveStats(a){let b={};a.forEach(c=>{"inbound-rtp"!=c.type||c.isRemote||"video"!=c.mediaType&&"video"!=c.kind&&!c.id.toLowerCase().includes("video")||
(b.timestamp=c.timestamp,b.timestampStart=this.lastStats&&this.lastStats.timestampStart?this.lastStats.timestampStart:c.timestamp,b.bytesReceived=c.bytesReceived,b.packetsLost=c.packetsLost,b.framesDropped=c.framesDropped,b.framesPerSecond=c.framesPerSecond,b.framesReceived=c.framesReceived,b.framesDecoded=c.framesDecoded,b.keyFramesDecoded=c.keyFramesDecoded,b.frameWidth=c.frameWidth,b.frameHeight=c.frameHeight,this.lastStats&&(b.bitrate=8*(b.bytesReceived-this.lastStats.bytesReceived)/(b.timestamp-
this.lastStats.timestamp)));"track"==c.type&&c.frameWidth&&(c.framesDropped&&(b.framesDropped=c.framesDropped),c.framesReceived&&(b.framesReceived=c.framesReceived),b.frameWidth=c.frameWidth,b.frameHeight=c.frameHeight);"candidate-pair"==c.type&&c.hasOwnProperty("currentRoundTripTime")&&0!=c.currentRoundTripTime&&(b.currentRoundTripTime=c.currentRoundTripTime)});this.lastStats.receiveToCompositeMs&&(b.receiveToCompositeMs=this.lastStats.receiveToCompositeMs,this.latencyTestTimings.setFrameDisplayDeltaTime(this.lastStats.receiveToCompositeMs));
this.lastStats=b;this.events.onstats(this.lastStats)}onDatachannelMessage(a){var b=new Uint8Array(a.data);switch(b[0]){case 0:this.events.ondatachannelmessage("QualityControlOwnership",1==b[1]);break;case 1:if((a=(new TextDecoder("utf-16")).decode(a.data.slice(1)))&&""!=a)this.events.ondatachannelmessage("ResponseAPI",a);break;case 101:b=b[1];a=(new TextDecoder("utf-8")).decode(a.data.slice(2));switch(b){case 0:this.events.ondatachannelmessage("ResponseAPI",a);break;case 1:this.reponse_api_json_string=
a;break;case 2:this.reponse_api_json_string+=a;break;case 3:this.reponse_api_json_string+=a,this.events.ondatachannelmessage("ResponseAPI",this.reponse_api_json_string)}break;case 6:a=(new TextDecoder("utf-16")).decode(a.data.slice(1));a=JSON.parse(a);this.latencyTestTimings.setUETimings(a);break;case 7:a=(new TextDecoder("utf-16")).decode(a.data.slice(1));a=JSON.parse(a);if(a.Encoder)this.events.ondatachannelmessage("InitialSettings",a);break;case 5:this.events.ondatachannelmessage("VideoEncoderAvgQP",
a)}}startLatencyTest(){this.video&&(this.latencyTestTimings.start(),this.sendDescriptor(6,{StartTime:this.latencyTestTimings.TestStartTimeMs}))}}class V{constructor(a){this.player=a;this.uniqueId=a.uniqueId;this.container=a.container}appendElement(a){this.container.appendChild(a);this.setNeedReposUI()}removeElement(a){this.container.removeChild(a)}get(a){return ka(a+"_"+this.uniqueId)}getValue(a){let b;return null==(b=this.get(a))?void 0:b.value}setValue(a,b){(a=this.get(a))&&(a.value=b)}setText(a,
b){(a=this.get(a))&&(a.innerText=b)}setHtml(a,b){(a=this.get(a))&&(a.innerHTML=b)}resize(){this.player.resize()}setNeedReposUI(){this.player.bNeedReposUI=!0}}class Zb extends V{constructor(a){super(a);this.el=document.createElement("pre");this.el.id="eventsPanel_"+this.uniqueId;this.el.className="__eventsPanel";this.el.oncontextmenu=new Function("return false");this.el.onselectstart=new Function("return false");this.appendElement(this.el);this.btnClear=document.createElement("a");this.btnClear.id=
"eventsPanelClear_"+this.uniqueId;this.btnClear.innerText="Clear";this.btnClear.className="__eventsPanelClear";this.btnClear.style.position="absolute";this.btnClear.setAttribute("href","javascript:");this.btnClear.addEventListener("click",b=>this.el.innerText="",!1);this.appendElement(this.btnClear)}destroy(){this.el&&(this.removeElement(this.el),this.el=null)}appendText(a){if(this.el){let b=this.el.innerText;b+=a+"\n";this.el.innerText=b;this.el.scrollTop=this.el.scrollHeight+100}}setPosition(a){a||
(a=this.container.getBoundingClientRect());this.el&&(this.el.style.width=a.width+"px",this.el.style.height=a.height/3+"px");this.btnClear&&(this.btnClear.style.left=a.width-50+"px",this.btnClear.style.top="20px")}}class S{}S.Information='<svg width="22" height="22" viewBox="0 0 32 32"><g transform="translate(1878 2755)"><rect width="32" height="32" transform="translate(-1878 -2755)" fill="none"/><path d="M11,22A11,11,0,0,1,3.222,3.222,11,11,0,1,1,18.779,18.778,10.929,10.929,0,0,1,11,22ZM8.5,10a.5.5,0,0,0-.5.5v2a.5.5,0,0,0,.5.5H10v5.5a.5.5,0,0,0,.5.5h2a.5.5,0,0,0,.5-.5v-8a.5.5,0,0,0-.5-.5ZM11,4a2,2,0,1,0,2,2A2,2,0,0,0,11,4Z" transform="translate(-1873 -2750)" fill="#3c3b3c"/></g></svg>';
S.FullScreenEnter='<svg width="22" height="22" viewBox="0 0 32 32"><g id="cloud_icon_fullscreen_def" transform="translate(1787 2755)"><rect width="32" height="32" transform="translate(-1787 -2755)" fill="none"/><g transform="translate(0 -0.5)"><g transform="translate(-1780 -2747.5)"><rect width="10" height="10" transform="translate(-2 -2)" fill="none"/><path d="M12,0H7A1,1,0,0,0,6,1V6" transform="translate(-6)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M6,6.5,0,1" transform="translate(0.5 -0.5)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g><g transform="translate(-1782 -2737.5)"><rect width="10" height="10" fill="none"/><path d="M12,6H7A1,1,0,0,1,6,5V0" transform="translate(-4 2)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M6,1,0,6.5" transform="translate(2.5 1)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g><g transform="translate(-1770 -2749.5)"><rect width="10" height="10" fill="none"/><path d="M6,0h5a1,1,0,0,1,1,1V6" transform="translate(-4 2)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M0,6.5,6,1" transform="translate(1.5 1.5)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g><g transform="translate(-1770 -2737.5)"><rect width="10" height="10" fill="none"/><path d="M6,6h5a1,1,0,0,0,1-1V0" transform="translate(-4 2)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M0,1,6,6.5" transform="translate(1.5 1)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g></g></g></svg>';
S.FullScreenExit='<svg width="22" height="22" viewBox="0 0 32 32"><g id="cloud_icon_exotfullsc_def" transform="translate(1744 2755)"><rect width="32" height="32" transform="translate(-1744 -2755)" fill="none"/><g transform="translate(0 -1)"><g transform="translate(-1739 -2749)"><rect width="10" height="10" fill="none"/><path d="M6,6h5a1,1,0,0,0,1-1V0" transform="translate(-4 2)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M0,1,6,6.5" transform="translate(1.5 1)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g><g transform="translate(-1739 -2737)"><rect width="10" height="10" fill="none"/><path d="M6,0h5a1,1,0,0,1,1,1V6" transform="translate(-4 2)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M0,6.5,6,1" transform="translate(1.5 1.5)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g><g transform="translate(-1727 -2749)"><rect width="10" height="10" fill="none"/><path d="M12,6H7A1,1,0,0,1,6,5V0" transform="translate(-4 2)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M6,1,0,6.5" transform="translate(2.5 1)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g><g transform="translate(-1727 -2737)"><rect width="10" height="10" fill="none"/><path d="M12,0H7A1,1,0,0,0,6,1V6" transform="translate(-4 2)" fill="none" stroke="#3c3b3c" stroke-width="2"/><path d="M6,6.5,0,1" transform="translate(2.5 1.5)" fill="none" stroke="#3c3b3c" stroke-width="2"/></g></g></g></svg>';
S.InitialCamera='<svg width="22" height="22" viewBox="0 0 32 32"><g id="cloud_icon_initialpos_def" transform="translate(1831 2755)"><rect width="32" height="32" transform="translate(-1831 -2755)" fill="none"/><path d="M18.667,22.222H1.333A1.333,1.333,0,0,1,0,20.893V8.24A1.319,1.319,0,0,1,.5,7.2L9.167.292a1.335,1.335,0,0,1,1.666,0L19.5,7.2A1.319,1.319,0,0,1,20,8.24V20.893A1.333,1.333,0,0,1,18.667,22.222ZM9,12.11a1,1,0,0,0-1,1v6a1,1,0,0,0,1,1h2a1,1,0,0,0,1-1v-6a1,1,0,0,0-1-1Z" transform="translate(-1825 -2751.11)" fill="#3c3b3c" stroke="rgba(0,0,0,0)" stroke-width="1"/></g></svg>';
class $b extends V{constructor(a){super(a);this.create()}create(){if(!this.el){this.el=document.createElement("button");this.el.id=`fullscreeButton_${this.uniqueId}`;this.el.className="__fullscreeButton";if(L.isIOSDevice||L.isSafari)this.el.style.backgroundSize="cover";this.el.innerHTML=S.FullScreenEnter;this.el.title=B.getString("EnterFullscreen");this.el.oncontextmenu=new Function("return false");this.el.onselectstart=new Function("return false");this.el.onclick=()=>this.player.fullscreen=!this.player.fullscreen;
this.el.onmousedown=a=>{1==a.button&&a.preventDefault()};this.appendElement(this.el);document.addEventListener("fullscreenchange",()=>{this.el.title=B.getString("ExitFullscreen");this.el.innerHTML=L.isFullscreen()?S.FullScreenExit:S.FullScreenEnter;this.player.resize()})}}destroy(){this.el&&(this.removeElement(this.el),this.el=null)}setPosition(a,b){a||(a=this.container.getBoundingClientRect());this.el&&(this.el.style.left=`${b?38:5}px`,this.el.style.top=`${a.height-32}px`)}}const T={BackSpace:8,
Shift:16,Control:17,Alt:18,RightShift:253,RightControl:254,RightAlt:255};class ac extends V{constructor(a,b){super(a);this.video=null;this.useBuiltinCursors=b.useBuiltinCursors;this.keyEventTargetStr=b.keyEventTarget;this.keyEventTarget="document"==b.keyEventTarget?document:"none"==b.keyEventTarget?null:"video";this.callOnUserInteraction=()=>this.player.resetInteractTimestamp();this.fakeMouseWithTouches=!1;this.onMButtonDoubleClick=b.onMButtonDoubleClick;this.mouseKeyListener=b.mouseKeyListener;this.touchListener=
b.touchListener;this.onActionEnable=!0;this.onEvents=b.onEvents;this.onVideoLoaded=b.onVideoLoaded;this.orientationChangeTimeout=void 0;this.keyMap=new Map;document.addEventListener("WeixinJSBridgeReady",()=>{L.isInWeixinBrowser=!0;this.video.play()});this.create()}registerEvents(){window.addEventListener("orientationchange",()=>{clearTimeout(this.orientationChangeTimeout);this.orientationChangeTimeout=setTimeout(()=>this.player.resize(),500)});this.registerKeyboardEvents();this.registerMouseEvents();
this.registerTouchEvents()}isVideoLoaded(){return!!this._isVideoLoaded}create(){this.video&&this.destroy();this.video||(this.video=document.createElement("video"),this.video.id=`streamingVideo_${this.uniqueId}`,this.video.style="position:absolute;",this.video.playsInline=!0,this.video.muted=!0,this.video.autoplay=!0,this.video.disablepictureinpicture=!0,"video"===this.keyEventTarget&&(this.video.tabIndex=1,this.keyEventTarget=this.video),this.appendElement(this.video),this.player.logStartupInfo("video element created"),
this._isVideoLoaded=!1,this.video.addEventListener("loadedmetadata",()=>{this._isVideoLoaded=!0;this.video.onmouseenter({button:0,x:0,y:0});this.onVideoLoaded()},!0),this.setCursor());if("requestVideoFrameCallback"in HTMLVideoElement.prototype){const a=(b,c)=>{c.receiveTime&&c.expectedDisplayTime&&(this.rtc.lastStats.receiveToCompositeMs=c.presentationTime-c.receiveTime);this.video&&this.video.requestVideoFrameCallback(a)};this.video.requestVideoFrameCallback(a)}}setWebRtcPlayer(a){(!this.video||
L.isAndroidDevice||L.isIOSDevice&&!L.isInWeixinBrowser)&&this.create();this.rtc=a;this.rtc.setVideo(this.video)}setActionEventEnabled(a){this.onActionEnable=a}setEnableInteract(a){this.disableInteract=!a}destroy(){this.video&&(this.removeElement(this.video),this.video=null)}valid(){return!!this.video}videoWidth(){let a;return null==(a=this.video)?void 0:a.videoWidth}videoHeight(){let a;return null==(a=this.video)?void 0:a.videoHeight}clientWidth(){let a;return null==(a=this.video)?void 0:a.clientWidth}clientHeight(){let a;
return null==(a=this.video)?void 0:a.clientHeight}resize(a){this.playerAspectRatio=this.container.clientHeight/this.container.clientWidth;this.videoAspectRatio=a?this.playerAspectRatio:this.videoHeight()/this.videoWidth()}setTrack(a){let b=a.streams[0];this.player.logStartupInfo("track: "+a.track.kind);"audio"==a.track.kind?(a=document.createElement("Audio"),a.autoplay=!0,a.srcObject=b,a.play(),this.player.logStartupInfo("audio element created")):"video"==a.track.kind&&this.video.srcObject!==b&&(this.video.srcObject=
b)}setKeyEventTarget(a){this.keyEventTargetStr!=a&&(this.clearKeyboardEvents(),"video"==a?(this.video&&(this.video.tabIndex=1),this.keyEventTarget=this.video,this.video.focus()):"document"==a?(this.keyEventTarget=document,this.video.focus()):this.keyEventTarget&&(this.keyEventTarget=null),this.keyEventTargetStr=a,this.registerKeyboardEvents())}clearKeyboardEvents(){this.keyEventTarget&&(this.keyEventTarget.onkeydown=null,this.keyEventTarget.onkeyup=null,this.keyEventTarget.onkeypress=null)}registerKeyboardEvents(){this.keyEventTarget&&
(this.keyEventTarget.onkeydown=a=>{if(this.disableInteract)a.preventDefault();else{ea(a)||a.ctrlKey||(this.keyMap.set(a.keyCode,a),this.sendKeydown(a));if(a.keyCode===T.BackSpace)this.keyEventTarget.onkeypress({charCode:T.BackSpace});this.callActionEventHander(5,a)}},this.keyEventTarget.onkeyup=a=>{this.disableInteract?a.preventDefault():(ea(a)||a.ctrlKey||(this.keyMap.has(a.keyCode)&&this.keyMap.delete(a.keyCode),this.sendKeyup(a)),this.callActionEventHander(6,a))},this.keyEventTarget.onkeypress=
a=>{this.disableInteract?a.preventDefault():(ea(a)||this.sendKeypress(a),this.callActionEventHander(7,a))})}setCursor(){if(this.video&&!this.disableInteract)if(this.useBuiltinCursors)switch(this.mouseDownButton){case 0:this.video.setAttribute("class","streamingVideoCursorPan");break;case 1:this.video.setAttribute("class","streamingVideoCursorMove");break;case 2:this.video.setAttribute("class","streamingVideoCursorRotate");break;default:this.video.setAttribute("class","streamingVideoCursorPointer")}else this.video.setAttribute("class",
"streamingVideoNoCursor")}callActionEventHander(a,b){if(this.mouseKeyListener&&this.onActionEnable)switch(a){case 0:if(L.isFunction(this.mouseKeyListener.onMouseEnter))this.mouseKeyListener.onMouseEnter(b);break;case 1:if(L.isFunction(this.mouseKeyListener.onMouseLeave))this.mouseKeyListener.onMouseLeave(b);break;case 2:if(L.isFunction(this.mouseKeyListener.onMouseMove))this.mouseKeyListener.onMouseMove(b);break;case 3:if(L.isFunction(this.mouseKeyListener.onMouseDown))this.mouseKeyListener.onMouseDown(b);
break;case 4:if(L.isFunction(this.mouseKeyListener.onMouseUp))this.mouseKeyListener.onMouseUp(b);break;case 5:if(L.isFunction(this.mouseKeyListener.onKeyDown))this.mouseKeyListener.onKeyDown(b);break;case 6:if(L.isFunction(this.mouseKeyListener.onKeyUp))this.mouseKeyListener.onKeyUp(b);break;case 7:if(L.isFunction(this.mouseKeyListener.onKeyPress))this.mouseKeyListener.onKeyPress(b)}}isMiddleDoubleClick(a){if(1==a.button){if(this.lastMClickTimestamp&&200>a.timeStamp-this.lastMClickTimestamp)return!0;
this.lastMClickTimestamp=a.timeStamp}else this.lastMClickTimestamp=void 0;return!1}normalizeAndQuantizeUnsigned(a,b){if(this.playerAspectRatio>this.videoAspectRatio){let c=this.playerAspectRatio/this.videoAspectRatio;a/=this.container.clientWidth;b=c*(b/this.container.clientHeight-.5)+.5}else a=this.videoAspectRatio/this.playerAspectRatio*(a/this.container.clientWidth-.5)+.5,b/=this.container.clientHeight;return 0>a||1<a||0>b||1<b?{inRange:!1,x:65535,y:65535}:{inRange:!0,x:65536*a,y:65536*b}}normalizeAndQuantizeSigned(a,
b){if(this.playerAspectRatio>this.videoAspectRatio){let c=this.playerAspectRatio/this.videoAspectRatio;a/=.5*this.container.clientWidth;b=c*b/(.5*this.container.clientHeight);return{x:32767*a,y:32767*b}}return{x:this.videoAspectRatio/this.playerAspectRatio*a/(.5*this.container.clientWidth)*32767,y:b/(.5*this.container.clientHeight)*32767}}registerMouseEvents(){this.video.onmouseenter=a=>{this.disableInteract?a.preventDefault():(this.sendMouseEnter(!0),a.buttons&1&&this.sendMouseDown(0,a.offsetX,a.offsetY),
a.buttons&2&&this.sendMouseDown(2,a.offsetX,a.offsetY),a.buttons&4&&this.sendMouseDown(1,a.offsetX,a.offsetY),a.buttons&8&&this.sendMouseDown(3,a.offsetX,a.offsetY),a.buttons&16&&this.sendMouseDown(4,a.offsetX,a.offsetY),this.callActionEventHander(0,a))};this.video.onmouseleave=a=>{this.disableInteract?a.preventDefault():(this.sendMouseLeave(!0),a.buttons&1&&this.sendMouseUp(0,a.offsetX,a.offsetY),a.buttons&2&&this.sendMouseUp(2,a.offsetX,a.offsetY),a.buttons&4&&this.sendMouseUp(1,a.offsetX,a.offsetY),
a.buttons&8&&this.sendMouseUp(3,a.offsetX,a.offsetYy),a.buttons&16&&this.sendMouseUp(4,a.offsetX,a.offsetY),this.callActionEventHander(1,a))};this.video.onmousemove=a=>{if(this.disableInteract)a.preventDefault();else{this.sendMouseMove(a.offsetX,a.offsetY,a.movementX,a.movementY);a.preventDefault();if(0==this.mouseDownButton||2==this.mouseDownButton)this.setCursor(),this.mouseDownButton=void 0;this.callActionEventHander(2,a)}};this.video.onmousedown=a=>{if(this.disableInteract)a.preventDefault();
else if(this.keyEventTarget==this.video&&this.video.focus(),this.sendMouseEnter(!0),this.isMiddleDoubleClick(a)&&this.onMButtonDoubleClick)this.onMButtonDoubleClick(a.offsetX,a.offsetY),a.preventDefault();else{if(0<this.keyMap.size){for(let b of this.keyMap.values())this.sendKeyup(b);this.keyMap.clear()}this.mouseDownButton=a.button;this.sendMouseDown(a.button,a.offsetX,a.offsetY);a.preventDefault();0!=a.button&&2!=a.button&&this.setCursor();this.callActionEventHander(3,a)}};this.video.onmouseup=
a=>{this.disableInteract?a.preventDefault():(this.mouseDownButton=void 0,this.sendMouseUp(a.button,a.offsetX,a.offsetY),a.preventDefault(),this.setCursor(),this.callActionEventHander(4,a))};this.video.oncontextmenu=a=>{a.preventDefault()};"onmousewheel"in this.video?this.video.addEventListener("mousewheel",a=>{this.disableInteract||this.sendMouseWheel(a.wheelDelta,a.offsetX,a.offsetY);a.preventDefault()},{passive:!1}):this.video.addEventListener("DOMMouseScroll",a=>{this.disableInteract||this.sendMouseWheel(-120*
a.detail,a.offsetX,a.offsetY);a.preventDefault()},!1)}registerTouchEvents(){this.initFingers=()=>{this.fingers=[9,8,7,6,5,4,3,2,1,0];this.fingerIds={};this.bWaitFor0Fingers=!1};this.rememberTouch=a=>{let b=this.fingers.pop();if(void 0===b){let c;null==(c=this.onEvents)||c.call(this,"\tError! Exhausted touch indentifiers!")}else this.fingerIds[a.identifier]=b};this.forgetTouch=a=>{this.fingers.push(this.fingerIds[a.identifier]);delete this.fingerIds[a.identifier]};this.triggerTouchEvent=(a,b)=>{"function"===
typeof this.touchListener&&this.onActionEnable&&this.touchListener(a,b)};this.initFingers();this.handleTouchStart=a=>{a.preventDefault();let b;null==(b=this.onEvents)||b.call(this,`[S] ${a.touches.length} / ${a.changedTouches.length}`);if(this.bWaitFor0Fingers){let d;null==(d=this.onEvents)||d.call(this,"\tWaiting...")}else if(2<a.touches.length){this.bWaitFor0Fingers=!0;let d;null==(d=this.onEvents)||d.call(this,"\tWAITING START!");this.sendTouch(81,a.touches)}else{for(let d of a.changedTouches)this.rememberTouch(d);
var c;null==(c=this.onEvents)||c.call(this,`\t${JSON.stringify(this.fingerIds)} ${JSON.stringify(this.fingers)}`);this.sendTouch(80,a.changedTouches);this.triggerTouchEvent("touchstart",a)}};this.handleTouchMove=a=>{if(this.disableInteract)a.preventDefault();else{a.preventDefault();var b;null==(b=this.onEvents)||b.call(this,`[M] ${a.touches.length} / ${a.changedTouches.length}`);if(this.bWaitFor0Fingers){let c;null==(c=this.onEvents)||c.call(this,"\tWaiting...")}else this.sendTouch(82,a.touches),
this.triggerTouchEvent("touchmove",a)}};this.handleTouchEnd=a=>{if(this.disableInteract)a.preventDefault();else{a.preventDefault();var b;null==(b=this.onEvents)||b.call(this,`[E] ${a.touches.length} / ${a.changedTouches.length}`);if(this.bWaitFor0Fingers){if(0==a.touches.length){this.bWaitFor0Fingers=!1;this.initFingers();let d;null==(d=this.onEvents)||d.call(this,"\tWAITING END!")}}else{this.sendTouch(81,a.changedTouches);for(let d of a.changedTouches)this.forgetTouch(d);var c;null==(c=this.onEvents)||
c.call(this,`\t${JSON.stringify(this.fingerIds)} ${JSON.stringify(this.fingers)}`);this.triggerTouchEvent("touchend",a)}}};this.handleTouchCancel=a=>{if(this.disableInteract)a.preventDefault();else{a.preventDefault();var b;null==(b=this.onEvents)||b.call(this,`[C] ${a.touches.length} / ${a.changedTouches.length}`);this.sendTouch(81,a.changedTouches);this.initFingers();this.triggerTouchEvent("touchcancel",a)}};this.video.addEventListener("touchstart",a=>this.handleTouchStart(a),!1);this.video.addEventListener("touchmove",
a=>this.handleTouchMove(a),!1);this.video.addEventListener("touchend",a=>this.handleTouchEnd(a),!1);this.video.addEventListener("touchcancel",a=>this.handleTouchCancel(a),!1)}sendTouch(a,b){this.callOnUserInteraction();let c=new DataView(new ArrayBuffer(2+10*b.length));c.setUint8(0,a);c.setUint8(1,b.length);a=2;let d=[],e=this.video.getBoundingClientRect();for(let f of b){let l=f.clientX-e.left,g=f.clientY-e.top,t=this.normalizeAndQuantizeUnsigned(l,g),k=this.fingerIds[f.identifier];if(void 0==k){let n;
null==(n=this.onEvents)||n.call(this,`\tError! Invalid: ${f.identifier}, ${JSON.stringify(this.fingerIds)} ${JSON.stringify(this.fingers)}, count:${b.length}`)}else c.setUint16(a,t.x,!0),a+=2,c.setUint16(a,t.y,!0),a+=2,c.setInt32(a,k,!0),a+=4,c.setUint8(a,255*f.force,!0),a+=1,c.setUint8(a,t.inRange?1:0,!0),a+=1,d.push(`{${k} (${parseInt(l)},${parseInt(g)})->(${parseInt(t.x)},${parseInt(t.y)})}`)}this.player.doEventSync(c.buffer);this.rtc.send(c.buffer)}sendKeydown(a){this.callOnUserInteraction();
let b=la(a),c;null==(c=this.onEvents)||c.call(this,`Send KeyDown, Code:${b}, Repeat:${a.repeat}`);a=(new Uint8Array([60,b,a.repeat])).buffer;this.player.doEventSync(a);this.rtc.send(a)}sendKeyup(a){this.callOnUserInteraction();a=la(a);var b;null==(b=this.onEvents)||b.call(this,`Send KeyUp:${a}`);b=(new Uint8Array([61,a])).buffer;this.player.doEventSync(b);this.rtc.send(b)}sendKeypress(a){this.callOnUserInteraction();var b;null==(b=this.onEvents)||b.call(this,`Send KeyPress:${a.charCode}`);b=new DataView(new ArrayBuffer(3));
b.setUint8(0,62);b.setUint16(1,a.charCode,!0);this.player.doEventSync(b.buffer);this.rtc.send(b.buffer)}sendMouseEnter(){this.callOnUserInteraction();var a=new DataView(new ArrayBuffer(1));a.setUint8(0,70);this.player.doEventSync(a.buffer);this.rtc.send(a.buffer);let b;null==(b=this.onEvents)||b.call(this,"Send MouseEnter")}sendMouseLeave(){this.callOnUserInteraction();var a=new DataView(new ArrayBuffer(1));a.setUint8(0,71);this.player.doEventSync(a.buffer);this.rtc.send(a.buffer);let b;null==(b=
this.onEvents)||b.call(this,"Send MouseLeave")}sendMouseMove(a,b,c,d){this.callOnUserInteraction();if(this.normalizeAndQuantizeUnsigned){var e=this.normalizeAndQuantizeUnsigned(a,b),f=this.normalizeAndQuantizeSigned(c,d),l=new DataView(new ArrayBuffer(9));l.setUint8(0,74);l.setUint16(1,e.x,!0);l.setUint16(3,e.y,!0);l.setInt16(5,f.x,!0);l.setInt16(7,f.y,!0);this.player.doEventSync(l.buffer);this.rtc.send(l.buffer);var g;null==(g=this.onEvents)||g.call(this,`Send MouseMove, X:${a}, Y:${b}, DeltaX:${c}, DeltaY:${d}`)}}sendMouseDown(a,
b,c){this.callOnUserInteraction();if(this.normalizeAndQuantizeUnsigned){var d=this.normalizeAndQuantizeUnsigned(b,c),e=new DataView(new ArrayBuffer(6));e.setUint8(0,72);e.setUint8(1,a);e.setUint16(2,d.x,!0);e.setUint16(4,d.y,!0);this.player.doEventSync(e.buffer);this.rtc.send(e.buffer);var f;null==(f=this.onEvents)||f.call(this,`Send MouseDown, Button:${a}, X:${b}, Y:${c}`)}}sendMouseUp(a,b,c){if(this.normalizeAndQuantizeUnsigned){var d=this.normalizeAndQuantizeUnsigned(b,c),e=new DataView(new ArrayBuffer(6));
e.setUint8(0,73);e.setUint8(1,a);e.setUint16(2,d.x,!0);e.setUint16(4,d.y,!0);this.player.doEventSync(e.buffer);this.rtc.send(e.buffer);var f;null==(f=this.onEvents)||f.call(this,`Send MouseUp, Button:${a}, X:${b}, Y:${c}`)}}sendMouseWheel(a,b,c){this.callOnUserInteraction();if(this.normalizeAndQuantizeUnsigned){var d=this.normalizeAndQuantizeUnsigned(b,c),e=new DataView(new ArrayBuffer(7));e.setUint8(0,75);e.setInt16(1,a,!0);e.setUint16(3,d.x,!0);e.setUint16(5,d.y,!0);this.player.doEventSync(e.buffer);
this.rtc.send(e.buffer);var f;null==(f=this.onEvents)||f.call(this,`Send MouseWheel, Delta:${a}, X:${b}, Y:${c}`)}}}class bc extends V{constructor(a,b,c){super(a);this.updateConnectionCount(b);this.createButton(c)}createButton(a){if(!this.elButton){this.elButton=document.createElement("button");this.elButton.id=`liveStatusSwitchButton_${this.uniqueId}`;this.elButton.innerHTML=S.Information;this.elButton.title=a;this.elButton.className="__liveStatusSwitchButton";if(L.isIOSDevice||L.isSafari)this.elButton.style.backgroundSize=
"cover";this.elButton.oncontextmenu=new Function("return false");this.elButton.onselectstart=new Function("return false");this.elButton.onclick=()=>this.setPanelVisible();this.elButton.onmousedown=b=>{1==b.button&&b.preventDefault()};this.appendElement(this.elButton)}}createPanel(){if(!this.elPanel){var a='<table border="0" cellspacing="0" cellpadding="0" style="position:absolute !important;padding:4px;left:0px;top:0px;width:100%;height:100%;">'+`<tr><td height="22" align="right" width="66" title="${B.getString("Connections")}-PlayerId-QualityControl">${B.getString("ConnInfo")}</td><td><span id="i_connections_${this.uniqueId}">1</span>-<span id="i_playerId_${this.uniqueId}">1</span></td><td align="right"><a id="i_hide_panel_${this.uniqueId}" href="javascript:">${B.getString("Close")}</a></td></tr>`+
`<tr><td height="22" align="right">${B.getString("Duration")}</td><td colspan="2" id="i_duration_${this.uniqueId}">00:00:00</td></tr>`+`<tr><td height="22" align="right">${B.getString("Resolution")}</td><td colspan="2" id="i_resolution_${this.uniqueId}">1920 x 1080</td></tr>`+`<tr><td height="22" align="right">${B.getString("Received")}</td><td colspan="2" id="i_bytesReceived_${this.uniqueId}">0 kB</td></tr>`+`<tr><td height="22" align="right">${B.getString("ReceivedFrames")}</td><td colspan="2" id="i_framesReceived_${this.uniqueId}">0</td></tr>`+
`<tr><td height="22" align="right" title="${B.getString("DroppedTip")}">${B.getString("Dropped")}</td><td colspan="2"><span id="i_packetsLost_${this.uniqueId}">0</span> / <span id="i_frameDropped_${this.uniqueId}">0</span></td></tr>`+`<tr><td height="22" align="right" title="${B.getString("DecodeTimeTip")}">${B.getString("DecodingTime")}</td><td colspan="2"><span id="i_compositeTime_${this.uniqueId}"></span></td></tr>`+`<tr><td height="22" align="right" title="${B.getString("DecodeFramesTip")}">${B.getString("DecodeFrames")}</td><td colspan="2"><span id="i_framesDecoded_${this.uniqueId}" title="number of frames decoded">0</span> / <span id="i_keyframesDecoded_${this.uniqueId}" title="Number of keyframes decoded">0</span></td></tr>`+
`<tr><td height="22" align="right">${B.getString("Bitrate")}</td><td colspan="2" id="i_bitrate_${this.uniqueId}">-</td></tr>`+`<tr><td height="22" align="right" title="${B.getString("FPSTip")}">${B.getString("FPS")}</td><td colspan="2" title="${B.getString("FPSTip")}"><span id="i_framerateRender_${this.uniqueId}">-</span> / <span id="i_framerateVideo_${this.uniqueId}">-</span></td></tr>`+`<tr><td height="22" align="right">${B.getString("QP")}</td><td colspan="2" id="i_qp_${this.uniqueId}">0</td></tr>`+
`<tr><td height="22" align="right" title="${B.getString("MaxQPTip")}">${B.getString("MaxQP")}</td><td colspan="2"><select id="i_settings_maxQP_${this.uniqueId}" style="width:84px;"></select></td></tr>`+`<tr><td height="22" align="right" title="Mbps">${B.getString("MaxBitrate")}</td><td colspan="2"><select id="i_settings_maxBitrate_${this.uniqueId}" style="width:84px;"></select></td></tr>`+"</table>";this.elPanel=document.createElement("div");this.elPanel.innerHTML=a;this.elPanel.id=`liveStatusPanel_${this.uniqueId}`;
this.elPanel.className="__liveStatusPanel __fadeinComp";this.elPanel.oncontextmenu=new Function("return false");this.elPanel.onselectstart=new Function("return false");this.appendElement(this.elPanel);this.get("i_hide_panel").onclick=()=>this.setPanelVisible();for(a=29;40>=a;a++)this.get("i_settings_maxQP").add(new Option(a,a));this.get("i_settings_maxQP").onchange=()=>{let b={encodeMaxQP:this.getValue("i_settings_maxQP")};this.player.rtc.updateParams(b);this.player.signallingConnection.updateParams(b);
this.get("i_settings_maxQP").blur()};for(a=10;100>=a;a+=5)this.get("i_settings_maxBitrate").add(new Option(a,a));this.get("i_settings_maxBitrate").onchange=()=>{let b={maxBitrate:this.getValue("i_settings_maxBitrate")};this.player.rtc.updateParams(b);this.player.signallingConnection.updateParams(b);this.get("i_settings_maxBitrate").blur()}}}setPosition(a){a||(a=this.container.getBoundingClientRect());this.elButton&&(this.elButton.style.top=`${a.height-32}px`);if(this.elPanel)if(this.customPosX&&this.customPosY)this.elPanel.style.left=
this.customPosX+"px",this.elPanel.style.top=this.customPosY+"px";else{let b=this.elPanel.getBoundingClientRect();this.elPanel.style.left="5px";this.elPanel.style.top=`${a.height-b.height-42}px`}}destroy(){this.updateLatencyTestUI=!1;this.visible&&this.setPanelVisible();this.elPanel&&(this.removeElement(this.elPanel),this.elPanel=null);this.elButton&&(this.removeElement(this.elButton),this.elButton=null)}setPanelVisible(a,b){this.customPosX=a;this.customPosY=b;if(this.elPanel)if("none"==this.elPanel.style.display)this.updateConnectionCount(),
this.elPanel.style.display="block",this.elPanel.className="__liveStatusPanel __fadeinComp",this.visible=!0,this.player.rtc.sendEnableIntervalSendQP(!0);else{this.elPanel.className="__liveStatusPanel __fadeoutComp";setTimeout(()=>{this.visible=!1;this.elPanel&&(this.elPanel.style.display="none");this.player.rtc.sendEnableIntervalSendQP(!1);this.setNeedReposUI();this.resize()},500);return}else this.createPanel(),this.visible=!0,this.resize(),this.updateConnectionCount(),this.player.rtc.requestInitialSettings(),
this.player.rtc.sendEnableIntervalSendQP(!0);this.setNeedReposUI();this.resize()}adjustMaxQP(a){return 18==a?(this.get("i_settings_maxQP")&&this.get("i_settings_maxQP").add(new Option(18,18)),18):0>a?40:0<=a&&29>a?29:40<a?40:a}adjustMaxBitrate(a){return 300==a?(this.get("i_settings_maxBitrate")&&this.get("i_settings_maxBitrate").add(new Option(300,300)),300):10>a?10:100<a?100:a}formatNumber(a,b){return"undefined"===typeof a?"-":(new Intl.NumberFormat(window.navigator.language,{maximumFractionDigits:b||
0})).format(a)}processValue(a){return"undefined"===typeof a?"-":a}updateButtonTip(a){this.elButton&&(this.elButton.title=a)}updateConnectionCount(a){"undefined"!==typeof a&&(this.playersCount=a);this.setText("i_connections",this.playersCount);this.updatePlayerId()}updatePlayerId(){this.setText("i_playerId",this.player.playerId+(this.player.isQualityController()?"-QC":""))}updateWebRtcStats(a){if(this.visible){var b=(a.timestamp-a.timestampStart)/1E3,c=new Intl.NumberFormat(window.navigator.language,
{maximumFractionDigits:0,minimumIntegerDigits:2}),d=[],e=[60,60];for(let f=0;f<e.length;f++)d.push(b%e[f]),b/=e[f];d.push(b);e=d[0];b=d[1];d=d[2];b=60==b?0:b;e=c.format(60==e?0:e);b=c.format(Math.floor(b));c=c.format(Math.floor(d));this.setText("i_duration",c+":"+b+":"+e);this.setText("i_resolution",a.frameWidth&&a.frameHeight?a.frameWidth+" x "+a.frameHeight:"-");c="B";d=a.bytesReceived||0;b=["kB","MB","GB"];for(e=0;e<b.length&&!(1E5>d);e++)d/=1E3,c=b[e];this.setText("i_bytesReceived",this.formatNumber(d)+
" "+c);this.setText("i_framesReceived",this.processValue(a.framesReceived));this.setText("i_framesDecoded",a.framesDecoded);this.setText("i_keyframesDecoded",this.processValue(a.keyFramesDecoded));this.updateVideoFramerate(this.formatNumber(a.framesPerSecond));this.updateBitrate(a.bitrate);this.setText("i_packetsLost",this.processValue(a.packetsLost));this.setText("i_frameDropped",this.processValue(a.framesDropped));this.setText("i_compositeTime",this.formatNumber(a.receiveToCompositeMs,1))}}updateVideoFramerate(a){let b=
this.get("i_framerateVideo");b.style.color=10>a?"red":20>a?"orange":"rgb(128,255,0)";b.innerText=a}updateBitrate(a){let b=this.get("i_bitrate");a?1E3<a?(b.style.color="rgb(128,255,0)",b.innerText=this.formatNumber(a/1E3,1)+" Mbps"):(b.style.color=100>a?"red":1E3>a?"orange":"rgb(128,255,0)",b.innerText=this.formatNumber(a)+" kbps"):b.innerText="-"}updateInitialSettings(a){let b=this.adjustMaxQP(a.Encoder.MaxQP);this.setValue("i_settings_maxQP",b);a=this.adjustMaxBitrate(a.WebRTC.MaxBitrate/1E3/1E3);
this.setValue("i_settings_maxBitrate",a)}updateQPAndRenderFPS(a,b){let c=this.get("i_qp");c.style.color=35<a?"red":26<a?"orange":"rgb(128,255,0)";c.innerText=a;this.get("i_framerateRender").innerText=this.processValue(b)}}class cc extends V{constructor(a){super(a);this.bDelayClear=!1;this.create()}create(){this.el=document.createElement("pre");this.el.id=`startupInfoPanel_${this.uniqueId}`;this.el.className="__startupInfoPanel";this.el.oncontextmenu=new Function("return false");this.el.onselectstart=
new Function("return false");this.appendElement(this.el);this._visible=!0}destroy(){this.el&&(this.removeElement(this.el),this.el=null)}setVisible(a){!this._visible&&a&&this.el&&(this.el.innerText="");this._visible!=a&&this.el&&((this._visible=a)?(this.el.className="__startupInfoPanel",this.el.style.display="block"):this.el.style.display="none",this.setNeedReposUI())}hide(){this.el.className="__startupInfoPanel __fadeoutComp";setTimeout(()=>this.setVisible(!1),500)}setDelayClear(a){this.bDelayClear=
a}log(a){if(this.el){if(this.bDelayClear)this.bDelayClear=!1,this.el.innerText=a+"\n";else{let b=this.el.innerText;b+=a+"\n";this.el.innerText=b}this.el.scrollTop=this.el.scrollHeight+100}}setPosition(a){a||(a=this.container.getBoundingClientRect());this.el&&(this.el.style.height=`${a.height-20}px`)}}class dc extends V{constructor(a){super(a);this.create()}create(){this.el||(this.el=document.createElement("i"),this.el.id=`statusIndicator_${this.uniqueId}`,this.el.className="__statusIndicator",this.appendElement(this.el))}destroy(){this.el&&
(this.removeElement(this.el),this.el=null)}setStatus(a){let b="Red",c=this.get("statusIndicator");switch(a){case M.WS_Disconnected:b="Red";c&&(c.style.display="inline-block");break;case M.WS_Connecting:b="RGB(200,200,200)";break;case M.WS_Connected:b="Yellow";break;case M.RTC_Opened:b="RGB(0,128,255)";break;case M.OnReady:b="RGB(0,255,0)",this.hide()}this.lastStatus=a;c&&(c.style.backgroundColor=b)}hide(){let a=this.get("statusIndicator");a&&(a.style.display="none")}}class ec{constructor(){this.css("@keyframes __fadeoutframes {0%{opacity:1;} 100%{opacity:0;}}.__fadeoutComp {animation-duration:600ms; animation-name:__fadeoutframes;}@keyframes __fadeinframes {0%{opacity:0;} 100%{opacity:0.65;}}.__fadeinComp {animation-duration:100ms; animation-name:__fadeinframes;}@keyframes twinkling{0%{opacity:0;}100%{opacity:1;}}.__statusIndicator {position:absolute;display:block;left:5px;top:5px;width:6px;height:6px;border-radius:50%;background-color:red;animation:twinkling 0.25s infinite ease-in-out;}.__startupInfoPanel { position:absolute;left:3px;top:0px;width:400px;background-color:transparent !important;text-shadow:black 0.1em 0.1em 0.2em;color:white;font-family:Verdana;font-size:12px !important;overflow:auto;}.__liveStatusPanel {position:absolute;left:5px;top:10px;width:164px;height:300px;background-color:rgba(50,50,50,0.8);color:#00FF00;font-family:Verdana;font-size:10px;box-shadow:0px 0px 5px 5px rgba(200,200,200,0.5);-moz-border-radius:8px;-webkit-border-radius:8px;}.__liveStatusPanel a:link { color: rgb(0,255,128); text-decoration: none; }.__liveStatusPanel a:hover { color: yellow; background:transparent; }.__liveStatusPanel input {background:transparent; border:1px solid gray; color: #00FF00;}.__liveStatusPanel select {background:transparent; border:1px solid gray; color: #00FF00;}.__liveStatusPanel option {background:#333; color: #00FF00;}.__liveStatusSwitchButton{position:absolute;padding:0;left:6px;width:26px;height:26px;opacity:0.65;cursor:pointer;background-size:conver;}.__fullscreeButton {position:absolute;padding:0;left:38px;width:26px;height:26px;opacity:0.65;cursor:pointer;background-size:conver;}.__viewHomeButton {position:absolute;padding:0;left:70px;width:26px;height:26px;opacity:0.65;cursor:pointer;background-size:conver;}.__taskWaiting {position:absolute;z-index:30;text-align:center;padding-left:5px;text-shadow:black 0.1em 0.1em 0.2em;color:lime;font-family:Verdana;font-size:12px !important;overflow:hidden;display:none;}.__eventsPanel {position:absolute;left:3px;top:0px;width:600px;height:200px;background-color:rgba(64,64,64,0.5) !important;text-shadow:black 0.1em 0.1em 0.2em;color:white;font-family:Verdana;font-size:12px !important;overflow:auto;}.__eventsPanelClear {position:absolute;left:3px;top:20px;text-shadow:black 0.1em 0.1em 0.2em;color:yellow;font-family:Verdana;font-size:12px !important;}.streamingVideoNoCursor {width:100%;height:100%;}.streamingVideoCursorMove {width:100%;height:100%;cursor:move;}.streamingVideoCursorPan {width:100%;height:100%;cursor:url(data:application/octet-stream;base64,AAACAAEAICAAAA8ADwCoDAAAFgAAACgAAAAgAAAAQAAAAAEAGAAAAAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////8AAAD///8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAD///////8AAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD////////////////////////////////////////////4B///+Af///AH///gA///wAP//8AB///gAf//8AH//+AB///gA///8k///////////////////////////////////////////////////////////////////////w==),auto;}.streamingVideoCursorPointer {width:100%;height:100%;cursor:url(data:application/octet-stream;base64,AAACAAEAICAAAA0ACACoDAAAFgAAACgAAAAgAAAAQAAAAAEAGAAAAAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAD///////8AAAD///////8AAAD///////8AAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAD///////8AAAD///////8AAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAD///////8AAAD///////8AAAAAAAD///8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAD///////8AAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD////////////////////////////////////////////4B///+Af///AH///gA///wAP//8AB//+AAf//AAD//wAA//+AAP//4AD//+AF///gT///4f///+H////h////8////////////////////////////////////////w==),auto;}.streamingVideoCursorRotate {width:100%;height:100%;cursor:url(data:application/octet-stream;base64,AAACAAEAICAAAA8ADwCoDAAAFgAAACgAAAAgAAAAQAAAAAEAGAAAAAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAD///////////////////////////////////////8AAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAD///////////////////////////////////////////8AAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////8AAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////8AAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////8AAAD///8AAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAD///////8AAAD///////8AAAD///////8AAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD////////////////////////////////////////////4B///+Af///AH//hgAh/wwAMP4cABh+HgAYfh8AGH4eABh+HgA4eAck4BwP//A+H//4fz///P/////////////////////////////////////////////////////w==),auto;}")}css(a){let b=
document.createElement("style");try{b.appendChild(document.createTextNode(a))}catch(c){b.styleSheet.cssText=a}(a=document.getElementsByTagName("head")[0])?a.appendChild(b):console.error("[Cloud] No head element found, cannot create css.")}}class fc extends V{constructor(a){super(a);this.elContainer=document.createElement("div");this.elContainer.id=`taskWaiting_${this.uniqueId}`;this.elContainer.className="__taskWaiting";this.elContainer.oncontextmenu=new Function("return false");this.elContainer.onselectstart=
new Function("return false");this.appendElement(this.elContainer);this.elText=document.createElement("div");this.elText.style.marginBottom="15px";this.elText.innerText="dddd";this.elContainer.appendChild(this.elText);this.elButton=document.createElement("input");this.elButton.type="button";this.elButton.value=B.getString("RestartInstNow");this.elButton.onclick=()=>{this.player.setInstanceOptions({iid:this.player.options.iid,reset:!0})};this.elContainer.appendChild(this.elButton);B.onLanguageChangedCallbacks.push(()=>
{this.elButton.value=B.getString("RestartInstNow")});this.setMode(0)}setMode(a){this._mode=a;0===a?this.elContainer.style.background="transparent":(this.elContainer.style.background="rgba(64,64,64,0.5)",this.elContainer.style.height="26px",this.elContainer.style.lineHeight="26px")}destroy(){this.elContainer&&(this.removeElement(this.elContainer),this.elContainer=null)}updateText(a){this.elText&&(this.elText.innerText=a,this.show())}show(){this.elContainer.className="__taskWaiting";this.elContainer.style.display=
"block"}hide(){this.elContainer.className="__taskWaiting __fadeoutComp";setTimeout(()=>this.elContainer.style.display="none",500)}setPosition(a,b,c,d){if(this.elContainer)if(a||(a=this.container.getBoundingClientRect()),0==this._mode){var e=a.width/2-150;0>e&&(e=10);this.elContainer.style.left=e+"px";this.elContainer.style.top=a.height/2+"px"}else e=0,b&&e++,c&&e++,d&&e++,a=a.height-32,this.elContainer.style.left=(0==e?5:1==e?38:2==e?70:102)+"px",this.elContainer.style.top=a+"px"}}class gc extends V{constructor(a){super(a);
this.create()}create(){if(!this.el){var a=[];a.push(B.getString("LeftClickTip"));a.push(B.getString("RightClickTip"));a.push(B.getString("MiddleClickTip"));a=a.join("\n");this.el=document.createElement("button");this.el.id=`viewHomeButton_${this.uniqueId}`;this.el.className="__viewHomeButton";if(L.isIOSDevice||L.isSafari)this.el.style.backgroundSize="cover";this.el.innerHTML=S.InitialCamera;this.el.title=a;this.el.oncontextmenu=new Function("return false");this.el.onselectstart=new Function("return false");
this.el.onclick=b=>this.player.viewHome(0);this.el.onmousedown=b=>{0!=b.button&&this.player.viewHome(b.button);b.preventDefault()};this.appendElement(this.el)}}destroy(){this.el&&(this.removeElement(this.el),this.el=null)}setPosition(a,b,c){a||(a=this.container.getBoundingClientRect());if(this.el){let d=5;if(b&&c)d=70;else if(b||c)d=38;this.el.style.left=d+"px";this.el.style.top=`${a.height-32}px`}}}var ia=0,oa=[];class hc{constructor(a,b){if(a){this.host=a;this.options=b;Y.processParams(this.options);
this.receiveRenderEvents=this.options.receiveRenderEvents;B.setLanguage();this.uniqueId=++ia;oa.push(this);this.options.apiOptions.player=this;this.api=new ca(null,this.options.apiOptions);if(this.options.domId)if(a=ka(this.options.domId))this.container=document.createElement("div"),this.container.style="position:relative;width:100%;height:100%;",a.innerHTML="",a.appendChild(this.container);else if("loading"==document.readyState){console.error(B.getString("DomLoading"));return}if(this.hasVideo=!!this.container)this.styleManager=
new ec,this.createWidgetsInConstructor(),this.canAdaptiveResolution=!1,this.lastResizeTime=Date.now(),this.orientationChangeTimeout=void 0;this.constructUrlAndConnect();this.resetInteractTimestamp()}else console.error("[DigitalTwinPlayer] The parameter `host` cannot be empty!")}constructUrlAndConnect(){this.logStartupInfo(`host: ${this.host}`);this.useHttps="https:"==location.protocol||this.options.useHttps;this.url=`${this.useHttps?"wss":"ws"}://${this.host}/player?hasVideo=${this.hasVideo}`;this.options.offer&&
(this.url+="&offer=1");this.options.iid&&(this.url+="&iid="+this.options.iid);this.options.pid&&(this.url+="&pid="+this.options.pid);this.options.password&&(this.url+="&protected=1");if("object"==typeof this.options.urlExtraInfo)for(let a in this.options.urlExtraInfo){let b=this.options.urlExtraInfo[a];null!=b&&void 0!=b&&(this.url+="&"+a+"="+b)}this.options.reset&&(this.url+="&reset=1",this.options.reset=!1);setTimeout(()=>this.connectSignallingServer(),200)}callEvent(a,b,c){a=this.options.events[a];
"function"==typeof a&&a(b,c)}doEventSync(a){if(this.options.enableEventSync)for(let b of oa)b.options.enableEventSync&&b.uniqueId!=this.uniqueId&&b.rtc.send(a)}setEnableEventSync(a){this.options.enableEventSync=a}getHost(){return this.host}getAPI(){return this.api}getInstanceInfo(){return this.paramInfo}getVideoElement(){let a;return null==(a=this.htmlVideo)?void 0:a.video}getVideoElementSize(){let a=this.getVideoElement();return{width:null==a?void 0:a.clientWidth,height:null==a?void 0:a.clientHeight}}getVideoStreamSize(){return{width:this.htmlVideo.videoWidth(),
height:this.htmlVideo.videoHeight()}}getVideoSize(){return this.getVideoStreamSize()}screen2World(a,b,c){let d=this.getVideoElementSize(),e=this.getVideoStreamSize(),f=0,l=0,g=e.height/e.width;d.height/d.width>g?l=-(d.width*g-d.height)/2:f=-(d.height/g-d.width)/2;a=e.width/(d.width-2*f)*(a-f);b=e.height/(d.height-2*l)*(b-l);return this.getAPI().coord.screen2World(a,b,c)}world2Screen(a,b,c,d){const e=this;return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){let f=yield e.getAPI().coord.world2Screen(a,
b,c,d),l=e.getVideoElementSize(),g=e.getVideoStreamSize(),t=0,k=0,n=g.height/g.width;l.height/l.width>n?k=-(l.width*n-l.height)/2:t=-(l.height/n-l.width)/2;f.screenPosition=[f.screenPosition[0]/(g.width/(l.width-2*t))+t,f.screenPosition[1]/(g.height/(l.height-2*k))+k];return f})}setResolution(a,b){if(this.streamerAdaptive&&(0!=a||0!=b)){window.devicePixelRatio&&(a*=window.devicePixelRatio,b*=window.devicePixelRatio);if(4096<a||4096<b)b=a/b,1<=b?(a=4096,b=4096/b):(a=4096*b,b=4096);this.streamerAdaptive&&
this.streamerLimitMaxResolution&&(a>this.streamerResX||b>this.streamerResY)&&(b=a/b,b>=this.streamerResX/this.streamerResY?(a=this.streamerResX,b=this.streamerResX/b):(a=this.streamerResY*b,b=this.streamerResY));a=Math.floor(a);b=Math.floor(b);256>a&&(console.warn("ResX:"+a),a=256);256>b&&(console.warn("ResY:"+b),b=256);var c=this.htmlVideo.videoWidth(),d=this.htmlVideo.videoHeight();if(a!=c&&a+1!=c&&a-1!=c||b!=d&&b+1!=d&&b-1!=d){if(this.lastWidth&&this.lastHeight&&(c=Math.abs(this.lastWidth-a),d=
Math.abs(this.lastHeight-b),(0!=c||0!=d)&&4>c&&4>d))return;this.lastWidth=a;this.lastHeight=b;this.rtc&&this.rtc.updateRenderResolution(a,b);return[a,b]}}}destroy(a,b){console.log(`call DigitalTwinPlayer.destroy: ${a||" "}`);if(!this.isDestroyed){this.logStartupInfo(a||" ");ia--;this.isDestroyed=!0;this.destroyReason=a;var c;null==(c=this.rtc)||c.close();var d;null==(d=this.signallingConnection)||d.close(b)}}setKeyEventTarget(a){let b;null==(b=this.htmlVideo)||b.setKeyEventTarget(a)}setKeyEventReceiver(a){this.setKeyEventTarget(a)}resize(){if(this.container){this.rect=
this.container.getBoundingClientRect();if(this.bNeedReposUI||!this.oldRect||this.oldRect.width!=this.rect.width||this.oldRect.height!=this.rect.height){this.bNeedReposUI=!1;this.oldRect=this.rect;let b;null==(b=this.eventsPanel)||b.setPosition(this.rect);let c;null==(c=this.startupInfo)||c.setPosition(this.rect);let d;null==(d=this.liveStatus)||d.setPosition(this.rect);let e;null==(e=this.fullscreenButton)||e.setPosition(this.rect,this.options.ui.statusButton);let f;null==(f=this.homeButton)||f.setPosition(this.rect,
this.options.ui.statusButton,this.options.ui.fullscreenButton);let l;null==(l=this.taskListBar)||l.setPosition(this.rect,this.options.ui.statusButton,this.options.ui.fullscreenButton,this.options.ui.homeButton)}if(this.htmlVideo.valid()){this.canAdaptiveResolution&&this.setResolution(this.htmlVideo.clientWidth(),this.htmlVideo.clientHeight());var a;null==(a=this.htmlVideo)||a.resize(this.streamerAdaptive&&this.canAdaptiveResolution)}}}setInstanceOptions(a){const b=this;return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){var c=
a.pid==b.options.pid&&a.iid==b.options.iid||!a.pid&&a.iid==b.options.iid||!a.iid&&a.pid==b.options.pid;if(c&&!a.reset)b.api.log("setInstanceOptions: no change.");else{if(!c){c=(!a.iid||a.iid==b.options.iid)&&a.pid!=b.options.pid;if(b.streamerLocked&&c){b.api.log(B.getString("CannotChangeProject"));return}c=yield b.signallingConnection.checkParams(a);if(null!==c){if(!1===c.iid){b.api.log(`iid(${a.iid})${B.getString("InstanceNotExist")}`);return}if(!1===c.pid){b.api.log(`pid(${a.pid})${B.getString("ProjectNotExist")}`);
return}}}console.warn("call setInstanceOptions!");b.destroy();b.options.iid=a.iid;b.options.pid=a.pid;b.options.reset=a.reset;b.constructUrlAndConnect()}})}set fullscreen(a){a?L.fullscreen(this.container):L.exitFullscreen()}get fullscreen(){return!!document.fullscreenElement}setActionEventEnabled(a){let b;null==(b=this.htmlVideo)||b.setActionEventEnabled(a)}setEnableInteract(a){let b;null==(b=this.htmlVideo)||b.setEnableInteract(a)}perfTest(a){let b;null==(b=this.rtc)||b.perfTest(a)}onApiReady(){this.setStatus(M.OnReady);
let a;null==(a=this.signallingConnection)||a.sendReady();1==this.options.ui.mainUI?this.api.settings.setMainUIVisibility(!0):0==this.options.ui.mainUI&&this.api.settings.setMainUIVisibility(!1);1==this.options.ui.campass?this.api.settings.setCampassVisible(!0):0==this.options.ui.campass&&this.api.settings.setCampassVisible(!1)}sendApi(a,b){if(a)if(b){let c;null==(c=this.signallingConnection)||c.sendApi(a)}else{let c;null==(c=this.rtc)||c.sendApi(a)}}onMainThreadBusy(a){if(1===a.busy){let b="BUSY! ",
c=this.api.apiQueue.queueSize(),d=this.api.apiQueue.dataSize();0<c&&(b+=`${B.getString("JSQueue")}${c}/${d}\uff0c`);1<a.taskCount&&(b+=`${B.getString("BackQueue")}${a.taskCount}\uff0c`);b+=`${B.getString("Execting")}${h[a.currentTask]}${B.getString("PleaseWait")}`;this.hasVideo&&(this.htmlVideo.isVideoLoaded()&&this.options.ui.taskListBar||!this.htmlVideo.isVideoLoaded())&&this.taskListBar.updateText(b)}else this.taskListBar.hide()}viewHome(a){this.api.viewHome(a)}resetInteractTimestamp(){let a=Date.now();
this.timeOfLastInteraction=a;let b;null==(b=this.signallingConnection)||b.resetInteractTimestamp(a)}initNodeInfoTip(){let a=this.paramInfo,b=a.project,c=a.nodeIP,d=a.hostName;this.options.iid=a.iid;this.options.pid=a.pid;this.streamerAdaptive=a.adjustResolution;this.streamerLimitMaxResolution=a.limitMaxResolution;this.streamerResX=a.resX;this.streamerResY=a.resY;this.streamerLocked=a.locked;if(this.kickOutInMinutes=a.kickOutInMinutes){let f=setInterval(()=>{if(Date.now()-this.timeOfLastInteraction>
6E4*this.kickOutInMinutes){let l=B.getString("DisconnectForIdle"),g;null==(g=this.startupInfo)||g.setVisible(!0);this.destroy(l,W.kicked);console.warn(l);clearInterval(f)}},2E4)}this.nodeInfoArray=[`${B.getString("NodeInfo")}`];d&&this.nodeInfoArray.push(`\t${B.getString("Host")}`+d);c&&this.nodeInfoArray.push(`\t${B.getString("HostAddress")}`+c);b&&this.nodeInfoArray.push(`\t\u3000${B.getString("Project")}`+b);this.options.pid&&this.nodeInfoArray.push(`\t${B.getString("ProjectId")}`+this.options.pid);
this.nodeInfoArray.push(`\t${B.getString("IID")}`+this.options.iid);this.nodeInfoArray.push(`\t${B.getString("Adaptive")}`+this.streamerAdaptive);this.nodeInfoArray.push(`\t${B.getString("LimitMaxRes")}`+this.streamerLimitMaxResolution);this.nodeInfoArray.push(`\t${B.getString("Resolution")}${this.streamerResX}x${this.streamerResY}`);this.nodeInfoArray.push(`\t${B.getString("Codec")}${a.codec||"H264"}`);this.nodeInfoArray.push(`\t${B.getString("Platform")}${a.platform}`);let e;null==(e=this.liveStatus)||
e.updateButtonTip(this.nodeInfoArray.join("\n"));this.api.log(this.nodeInfoArray.join("\n\t"))}logStartupInfo(a){if(a){a=`[${da()}] ${a}`;var b;null==(b=this.startupInfo)||b.log(a);1<ia&&(a=`[${this.uniqueId}] `+a);console.log("[DEBUG]"+a);this.api.log(a)}}setStatus(a,b){this.cloudStatus=a;let c;null==(c=this.statusIndicator)||c.setStatus(a);switch(a){case M.WS_Disconnected:if(this.isDestroyed){console.log("destroyed.");break}b&&(this.logStartupInfo(b),console.warn(b));break;case M.WS_Connecting:this.logStartupInfo(`connecting with ${this.useHttps?
"wss":"ws"}...`);break;case M.WS_Connected:this.logStartupInfo("connected");break;case M.OnReady:let d;null==(d=this.startupInfo)||d.hide();this.options.ui.debugEventsPanel&&!this.eventsPanel&&(this.eventsPanel=new Zb(this),this.eventsPanel.appendText("Start recording events here:"))}}createWidgetsInConstructor(){this.taskListBar=new fc(this);this.htmlVideo=new ac(this,{keyEventTarget:this.options.keyEventTarget,useBuiltinCursors:this.options.useBuiltinCursors,mouseKeyListener:this.options.events.mouseKeyListener,
touchListener:this.options.events.touchListener,onEvents:a=>{let b;return null==(b=this.eventsPanel)?void 0:b.appendText(a)},onVideoLoaded:()=>{this.logStartupInfo("video: loadedmetadata");this.createWidgets();this.callEvent("onVideoLoaded");this.resize()},onMButtonDoubleClick:(a,b)=>{let c;null==(c=this.liveStatus)||c.setPanelVisible(a,b)}});this.options.ui.statusIndicator&&(this.statusIndicator=new dc(this));this.options.ui.startupInfo&&(this.startupInfo=new cc(this),this.logStartupInfo("sdk version: "+
this.api.getVersion()),this.logStartupInfo("uniqueId: "+this.uniqueId));this.options.disableResizeObserver||L.isMobileDevice||(this.resizeObserver=new ResizeObserver(pa(()=>this.resize(),1E3)),this.resizeObserver.observe(this.container))}createWidgets(){this.options.ui.statusButton&&(this.liveStatus=new bc(this,this.playerCount,this.nodeInfoArray.join("\n")));this.options.ui.fullscreenButton&&(this.fullscreenButton=new $b(this));this.options.ui.homeButton&&(this.homeButton=new gc(this));this.taskListBar&&
this.taskListBar.setMode(1)}destroyWidgets(){this.liveStatus&&(this.liveStatus.destroy(),this.liveStatus=null);this.fullscreenButton&&(this.fullscreenButton.destroy(),this.fullscreenButton=null);this.homeButton&&(this.homeButton.destroy(),this.homeButton=null);this.eventsPanel&&(this.eventsPanel.destroy(),this.eventsPanel=null);this.taskListBar&&(this.taskListBar.destroy(),this.taskListBar=null)}isQualityController(){return this.bQualityController}initWebRtcPlayer(a){B.setLanguage(a.language);this.initNodeInfoTip();
this.rtc=new Yb({useStats:this.hasVideo&&(this.options.ui.statusButton||this.options.events.onRtcStatsReport),peerConnectionOptions:a.peerConnectionOptions,events:{onlog:b=>this.logStartupInfo(b),ontrack:b=>{let c;null==(c=this.htmlVideo)||c.setTrack(b)},onicecandidate:b=>{let c;return null==(c=this.signallingConnection)?void 0:c.sendCandidate(b)},onOfferCreated:b=>{let c;return null==(c=this.signallingConnection)?void 0:c.sendOffer(b.sdp,this.hasVideo)},onAnswerCreated:b=>{let c;return null==(c=
this.signallingConnection)?void 0:c.send(b)},onstats:b=>{let c;null==(c=this.liveStatus)||c.updateWebRtcStats(b);this.callEvent("onRtcStatsReport",b)},ondatachannelopen:()=>{this.setStatus(M.RTC_Opened);this.signallingConnection.sendCustomString(this.options.customString);this.hasVideo&&(this.rtc.requestQualityControl(),this.liveStatus&&this.rtc.requestInitialSettings());this.receiveRenderEvents||this.rtc.sendEnableReceiveEvents(!1);this.api.onConnectionOpen()},ondatachannelclose:b=>this.api.onConnectionClose(b),
ondatachannelerror:b=>this.api.onConnectionClose(b.error),ondatachannelmessage:(b,c)=>{switch(b){case "ResponseAPI":this.api.onConnectionMessage(c);break;case "InitialSettings":let d;null==(d=this.liveStatus)||d.updateInitialSettings(c);break;case "VideoEncoderAvgQP":let e;if(null==(e=this.liveStatus)?0:e.visible)b=(new TextDecoder("utf-16")).decode(c.data.slice(1)),(b=JSON.parse(b))&&this.liveStatus.updateQPAndRenderFPS(b.qp,b.renderFPS);break;case "QualityControlOwnership":this.bQualityController=
c;let f;null==(f=this.liveStatus)||f.updatePlayerId()}}},enableApiCallLog:this.options.enableApiCallLog},this);this.hasVideo&&this.htmlVideo.setWebRtcPlayer(this.rtc);this.logStartupInfo("setting up...");this.options.offer&&this.rtc.createOffer();this.hasVideo&&this.options.registerEvents&&this.htmlVideo.registerEvents()}connectSignallingServer(){this.setStatus(M.WS_Connecting);this.signallingConnection=new Wb({onopen:()=>{this.isDestroyed=!1;this.setStatus(M.WS_Connected);this.options.password&&
this.signallingConnection.sendInstancePassword(this.options.password)},onclose:a=>this.handleSignallingClose(a),onerror:()=>{this.setStatus(M.WS_Disconnected)},onmessage:a=>{switch(a.type){case "config":setTimeout(()=>this.initWebRtcPlayer(a),200);break;case "answer":this.playerId=a.playerId;this.rtc&&this.rtc.onReceiveAnswer(a);break;case "offer":this.playerId=a.playerId;this.rtc&&this.rtc.onReceiveOffer(a);break;case "iceCandidate":this.rtc&&this.rtc.onIceCandidate(a.candidate);break;case "paramInfo":this.paramInfo=
a;break;case "status":this.handleSignallingMessage_Status(a);break;case "playerCount":this.handleSignallingMessage_PlayerCount(a);break;case "api":this.api.onConnectionMessage(JSON.stringify(a));break;case "userConfig":this.captchaRequired=a.captchaRequired}},customString:this.options.customString});this.signallingConnection.connect(this.url)}handleSignallingClose(a){this.destroyWidgets();this.callEvent("onConnClose",a);var b;null==(b=this.startupInfo)||b.setVisible(!0);this.api.onConnectionClose(a);
this.rtc&&this.rtc.close();b="";this.isDestroyed?(b=B.getString("Destroyed"),b=this.destroyReason?b+(": "+this.destroyReason):b+"."):(b="closed: ",a.code&&(b+=a.code),a.reason?b+=" "+a.reason:1006==a.code&&(b+=" "+B.getString("Disconnect")));this.setStatus(M.WS_Disconnected,b);ra(a.code)||this.destroy();this.isDestroyed||(this.logStartupInfo(B.getString("Reconnect5s")),this.startupInfo.setDelayClear(!0),setTimeout(()=>this.connectSignallingServer(),5E3));a.code==W.instance_is_busy?confirm(B.getString("RestartAndRetry"))&&
this.setInstanceOptions({iid:this.busyIId,reset:!0}):a.code==W.no_username_provided?this.callEvent("onLoginRequired",this.captchaRequired):a.code==W.user_does_not_exist?this.callEvent("onLoginRequired",this.captchaRequired):a.code==W.not_logged_in&&this.callEvent("onLoginRequired",this.captchaRequired)}handleSignallingMessage_Status(a){let b=qa(a.state);(b=a.info||b)&&this.logStartupInfo(b);a.state===Q.StartFailed?(console.error(a.info),this.options.ui.startupInfo&&setTimeout(()=>alert(a.info),500)):
a.state==Q.ConfirmBusy&&(this.busyIId=a.info)}handleSignallingMessage_PlayerCount(a){this.playerCount=a.count;let b;null==(b=this.liveStatus)||b.updateConnectionCount(this.playerCount);this.canAdaptiveResolution=1===a.count}}v.APIErrorCode=fa;v.ActionMode={None:0,Follow:1,FollowBehindAndAbove:2,FollowBehind:3,FollowAbove:4,FollowBellow:5,FollowLeft:6,FollowRight:7,FollowWorldRotation:8};v.AnimatedImageButtonData=Aa;v.AssetType={EPT_Scene:1,EPT_ModelActor:2,EPT_Measurement:3,EPT_Cut:4,EPT_DynamicWater:5,
EPT_Vehicle:6,EPT_Tag:7,EPT_Light:8,EPT_Decal:9,EPT_LightBeam:10,EPT_RadiationPoint:11,EPT_Surface:12,EPT_ShpPoint:13,EPT_ShpPolyline:14,EPT_ShpPolygon:15,EPT_Polyline:16,EPT_VideoProjector:17,EPT_Panoramic:18,EPT_FlattenModifier:19,EPT_Cesium:20,EPT_CutPolygonModifier:21,EPT_EffectPoint:22,EPT_S3MLayer:23,EPT_ParticleActor:24,EPT_RoleActor:25,EPT_SoundActor:26,EPT_CustomActor:27,EPT_CameraTour:28,EPT_Animation:29};v.BPFuncParamType={Bool:0,UInt8:1,Int32:2,Float:3,Double:4,String:5,Color:6,Vector2D:7,
Vector:8,Rotator:9,IntArray:10,StringArray:11,VectorArray:12,Coordinate:13,CoordinateArray:14,FloatArray:15,DisplayStyle:16,CustomIconPath:17};v.BPFunctionData=Oa;v.BeamData=Ba;v.CameraTourData=Qa;v.CameraTourKeyFrame=Pa;v.CloudStatus=M;v.Color={LightPink:"RGB(255,182,193)",Pink:"RGB(255,192,203)",Crimson:"RGB(220,20,60)",LavenderBlush:"RGB(255,240,245)",PaleVioletRed:"RGB(219,112,147)",HotPink:"RGB(255,105,180)",DeepPink:"RGB(255,20,147)",MediumVioletRed:"RGB(199,21,133)",Orchid:"RGB(218,112,214)",
Thistle:"RGB(216,191,216)",plum:"RGB(221,160,221)",Violet:"RGB(238,130,238)",Magenta:"RGB(255,0,255)",Fuchsia:"RGB(255,0,255)",DarkMagenta:"RGB(139,0,139)",Purple:"RGB(128,0,128)",MediumOrchid:"RGB(186,85,211)",DarkVoilet:"RGB(148,0,211)",DarkOrchid:"RGB(153,50,204)",Indigo:"RGB(75,0,130)",BlueViolet:"RGB(138,43,226)",MediumPurple:"RGB(147,112,219)",MediumSlateBlue:"RGB(123,104,238)",SlateBlue:"RGB(106,90,205)",DarkSlateBlue:"RGB(72,61,139)",Lavender:"RGB(230,230,250)",GhostWhite:"RGB(248,248,255)",
Blue:"RGB(0,0,255)",MediumBlue:"RGB(0,0,205)",MidnightBlue:"RGB(25,25,112)",DarkBlue:"RGB(0,0,139)",Navy:"RGB(0,0,128)",RoyalBlue:"RGB(65,105,225)",CornflowerBlue:"RGB(100,149,237)",LightSteelBlue:"RGB(176,196,222)",LightSlateGray:"RGB(119,136,153)",SlateGray:"RGB(112,128,144)",DoderBlue:"RGB(30,144,255)",AliceBlue:"RGB(240,248,255)",SteelBlue:"RGB(70,130,180)",LightSkyBlue:"RGB(135,206,250)",SkyBlue:"RGB(135,206,235)",DeepSkyBlue:"RGB(0,191,255)",LightBLue:"RGB(173,216,230)",PowDerBlue:"RGB(176,224,230)",
CadetBlue:"RGB(95,158,160)",Azure:"RGB(240,255,255)",LightCyan:"RGB(225,255,255)",PaleTurquoise:"RGB(175,238,238)",Cyan:"RGB(0,255,255)",Aqua:"RGB(212,242,231)",DarkTurquoise:"RGB(0,206,209)",DarkSlateGray:"RGB(47,79,79)",DarkCyan:"RGB(0,139,139)",Teal:"RGB(0,128,128)",MediumTurquoise:"RGB(72,209,204)",LightSeaGreen:"RGB(32,178,170)",Turquoise:"RGB(64,224,208)",Auqamarin:"RGB(127,255,170)",MediumAquamarine:"RGB(0,250,154)",MediumSpringGreen:"RGB(0,255,127)",MintCream:"RGB(245,255,250)",SpringGreen:"RGB(60,179,113)",
SeaGreen:"RGB(46,139,87)",Honeydew:"RGB(240,255,240)",LightGreen:"RGB(144,238,144)",PaleGreen:"RGB(152,251,152)",DarkSeaGreen:"RGB(143,188,143)",LimeGreen:"RGB(50,205,50)",Lime:"RGB(0,255,0)",ForestGreen:"RGB(34,139,34)",Green:"RGB(0,128,0)",DarkGreen:"RGB(0,100,0)",Chartreuse:"RGB(127,255,0)",LawnGreen:"RGB(124,252,0)",GreenYellow:"RGB(173,255,47)",OliveDrab:"RGB(85,107,47)",Beige:"RGB(245,245,220)",LightGoldenrodYellow:"RGB(250,250,210)",Ivory:"RGB(255,255,240)",LightYellow:"RGB(255,255,224)",Yellow:"RGB(255,255,0)",
Olive:"RGB(128,128,0)",DarkKhaki:"RGB(189,183,107)",LemonChiffon:"RGB(255,250,205)",PaleGodenrod:"RGB(238,232,170)",Khaki:"RGB(240,230,140)",Gold:"RGB(255,215,0)",Cornislk:"RGB(255,248,220)",GoldEnrod:"RGB(218,165,32)",FloralWhite:"RGB(255,250,240)",OldLace:"RGB(253,245,230)",Wheat:"RGB(245,222,179)",Moccasin:"RGB(255,228,181)",Orange:"RGB(255,165,0)",PapayaWhip:"RGB(255,239,213)",BlanchedAlmond:"RGB(255,235,205)",NavajoWhite:"RGB(255,222,173)",AntiqueWhite:"RGB(250,235,215)",Tan:"RGB(210,180,140)",
BrulyWood:"RGB(222,184,135)",Bisque:"RGB(255,228,196)",DarkOrange:"RGB(255,140,0)",Linen:"RGB(250,240,230)",Peru:"RGB(205,133,63)",PeachPuff:"RGB(255,218,185)",SandyBrown:"RGB(244,164,96)",Chocolate:"RGB(210,105,30)",SaddleBrown:"RGB(139,69,19)",SeaShell:"RGB(255,245,238)",Sienna:"RGB(160,82,45)",LightSalmon:"RGB(255,160,122)",Coral:"RGB(255,127,80)",OrangeRed:"RGB(255,69,0)",DarkSalmon:"RGB(233,150,122)",Tomato:"RGB(255,99,71)",MistyRose:"RGB(255,228,225)",Salmon:"RGB(250,128,114)",Snow:"RGB(255,250,250)",
LightCoral:"RGB(240,128,128)",RosyBrown:"RGB(188,143,143)",IndianRed:"RGB(205,92,92)",Red:"RGB(255,0,0)",Brown:"RGB(165,42,42)",FireBrick:"RGB(178,34,34)",DarkRed:"RGB(139,0,0)",Maroon:"RGB(128,0,0)",White:"RGB(255,255,255)",WhiteSmoke:"RGB(245,245,245)",Gainsboro:"RGB(220,220,220)",LightGrey:"RGB(211,211,211)",Silver:"RGB(192,192,192)",DarkGray:"RGB(169,169,169)",Gray:"RGB(128,128,128)",DimGray:"RGB(105,105,105)",Black:"RGB(0,0,0)"};v.CommandType=h;v.CustomObjectData=Ka;v.CustomObjectData2=La;v.CustomTagData=
xa;v.DecalData=Ia;v.DigitalTwinAPI=ca;v.DigitalTwinPlayer=hc;v.DynamicWaterData=Na;v.FieldType={String:0,Number:1};v.HeatMapPointData=ua;v.HeatMapStyle={Normal:-1,CustomColor:0,CustomWave:1};v.HighlightAreaData=ya;v.HydroDynamic2DStyle={TrueWater:0,HeatMap:1};v.ImageButtonData=za;v.LayerVisibleData=ta;v.MapMode={Campass:0,SmallMap:1,BigMap:2};v.MeasurementMode={Coordinate:1,Linear:2,Horizontal:3,Vertical:4,MultiPoint:5,TerrainArea:6};v.MousePickMask=aa;v.ODLineData=Fa;v.PanelType={ViewshedAnalysis:0,
SkylineAnalysis:1,ViewDomeAnalysis:2,VisiblityAnalysis:3,FloodFillAnalysis:4,SolarAnalysis:5,CutFillAnalysis:6,TerrainSlopeAnalysi:7,ContourLineAnalysis:8};v.PanoramaData=Ha;v.Polygon3DData=Da;v.Polygon3DStyle={Wave:0,LoopLine:1,Gradual:2,DynamicGradual:3,WaveTransparent:4,WideWave:5,RotateArrow:6,RotateLine:7,RotateGradual:8,SingleColor:9,SingleColorWithLight:10,OriginColor:11,OceanWater:12,LakeWater:13,Opacity:14,Mask:15,Transparent:16,Volumn:17};v.PolygonData=Ga;v.PolygonStyle={SingleColor:0,CirclePoint:1,
Volumn:2,Gradual:3,DynamicGradual:4,WaveTransparent:5,WideWave:6,RotateArrow:7,RotateLine:8,RotateGradual:9,OnTerrain:10,OriginColor:11};v.PolylineData=Ea;v.PolylineStyle={Arrow:0,Arrow1:1,Flow:2,Beam:3,Normal:4,OnTerrain:5,DottedNormal:6,DottedCircle:7,OriginColor:8,OnePixelWidth:9};v.REVISION=20026;v.RadiationPointData=Ca;v.RendererType={SimpleRenderer:0,UniqueValueRenderer:1,ClassBreaksRenderer:2,VisibleRenderer:3};v.ResetType=na;v.SplineMeshStyle={Fence_1:0,Fence_2:1,Wall_1:2,Wall_2:3,Road_0:4,
Road_1:5,Road_2:6,Road_3:7,Road_4:8,Road_5:9,Road_6:10,Road_7:11,Road_8:12,Road_9:13,Fence_Tree:14,Pipe:15};v.TagData=va;v.TileLayerActorData=U;v.TileLayerData=Ja;v.UIPanelType={MainUI:0,NewACP:1,OpenACP:2,MergeACP:3,SaveACP:4,Load3DT:5,Load3DTService:6,LoadSHP:7,Load3DTiles:8,Exit:9,InfoTreeGet:10,ResourceGet:11,PreferenceSettings:12,Around:13,CenterAround:14,Robomb:15,Role:16,Map:17,Vehicle:18,Edit:19,CameraTour:20,Report:21,Weather:22,PostProcess:23,Camera:24,ShowSettings:25,Measure:26,Flatten:27,
Hole:28,Clip:29,Select:30,Screenshot:31,Material:32,Vegetation:33,Vehicle:34,DynamicWater:35,VideoProjection:36,Panorama:37,Light:38,Marker:39,Marker3D:40,Decal:41,Beam:42,RadiationPoint:43,Polyline:44,Polygon:45,Settings:46,Help:47,ViewshedAnalysis:48,SkylineAnalysis:49,ViewDomeAnalysis:50,VisiblityAnalysis:51,FloodFillAnalysis:52,SolarAnalysis:53,CutFillAnalysis:54,TerrainSlopeAnalysi:55,ContourLineAnalysis:56,SplineMesh:57};v.VERSION="6.1";v.VectorFieldStyle={Typhoon:"DTS_TPHOON",Waves:"DTS_WAVE",
Ocean:"DTS_OCEAN",Fluid:"DTS_STYLE_FLUID",Fluid_UV:"DTS_FLUID_COUNT_XYZ_UV",River:"DTS_RIVER",Wave:"DTS_WATER_WAVE"};v.VehicleViewMode={FirstPerson:0,ThirdPerson:1};v.VideoProjectionData=Ma;v.Viewport=R;v.VisualType={Color:0,Size:1,Height:1,Opacity:1};v.WaterFlowFieldStyle={HeatMap:0,Water:1,Particle:2};v.WaterMode={Animation:0,Simulation:1,Arrow:2};v.WebUIData=wa;v.__onCefResponse=function(a,b){if(ha)ha.onConnectionMessage(a,b)};Object.defineProperty(v,"__esModule",{value:!0});return v}({});
(()=>{if("undefined"!=typeof module&&module.exports)module.exports=acapi;else if("function"==typeof define&&define.amd)define(function(){return acapi});else{window.AcApiVersion=acapi.VERSION;acapi.AirCityAPI=acapi.DigitalTwinAPI;acapi.AirCityPlayer=acapi.DigitalTwinPlayer;for(let v in acapi)"VERSION"!=v&&(window[v]=acapi[v]);acapi.DigitalTwinAPI.__onApiConstructed=v=>{window.__g=v;window.fdapi=v}}})();
