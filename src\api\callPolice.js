import { ref } from 'vue'
import request from '../utils/request'
export const valueyuliang = ref('')

export function caseInformations() {
  return request({
    url: `/taiyuan/caseInformation/list2`,
    method: 'get'
  })
}
export function nlxxtj() {
  return request({
    url: `/taiyuan/bjya-nlxxtj/stat?name=40`,
    method: 'get'
  })
}
export function swGET(name) {
  return request({
    url: `/taiyuan/bjya-sw/stat?name=40-` + name,
    method: 'get'
  })
}
export function qsyltj() {
  return request({
    url: `/taiyuan/bjya-qsyltj/stat?name=40`,
    method: 'get'
  })
}
export function nlfsqkfx(time) {
  return request({
    url: time ? `/taiyuan/bjya-nlfsqkfx/stat?name=${time}-40` : `/taiyuan/bjya-nlfsqkfx/stat?name=40`,
    method: 'get'
  })
}
export function qyhfbjsl() {
  return request({
    url: `/taiyuan/bjya-qyhfbjsl/stat?name=40`,
    method: 'get'
  })
}
export function yjxxyzcdfj() {
  return request({
    url: `/taiyuan/bjya-yjxxyzcdfj/stat?name=40`,
    method: 'get'
  })
}
export function bjyaYjlxtj() {
  return request({
    url: `/taiyuan/bjya-yjlxtj/stat?name=40`,
    method: 'get'
  })
}
export function sbdbqk() {
  return request({
    url: `/taiyuan/bjya-sbdbqk/stat?name=40`,
    method: 'get'
  })
}

export function bjyaswcbsw() {
  return request({
    url: `/taiyuan/bjya-swcbsw/stat?name=40`,
    method: 'get'
  })
}

export function bjyafqdbqk() {
  return request({
    url: `/taiyuan/bjya-fqdbqk/stat?name=40`,
    method: 'get'
  })
}
export function yxfwGET() {
  return request({
    url: `/taiyuan/bjya-yxfw/stat?name=40`,
    method: 'get'
  })
}
export function bjyaYjlxtj2(area='', level='', road='') {
  return request({
    url: `/taiyuan/bjya-yjlxtj2/stat?name=40`,
    method: 'get'
  })
}

export function statisticsListGET(area='',grade='',type='',rainfall=40) {
  return request({
    url: `/taiyuan/statistics/list?area=${area}&grade=${grade}&type=${type}&rainfall=${rainfall}`,
    method: 'get'
  })
}
export function bjyaYxfw(level) {
  return request({
    url: level ?`/taiyuan/bjya-yxfw2/stat?name=${level}-40` : `/taiyuan/bjya-yxfw/stat?name=40`,
    method: 'get'
  })
}

export function bjyaYyfa(value) {
  return request({
    url: `/taiyuan/bjya-yyfa/stat?name=` + value,
    method: 'get'
  })
}
export function bjyaYyfaSelectData() {
  return request({
    url: `/taiyuan/bjya-yl/stat`,
    method: 'get'
  })
}

// 内涝发生情况分析
export function bjyaNlfsqkfx2() {
  return request({
    url: `/taiyuan/bjya-nlfsqkfx2/stat?name=` + valueyuliang.value,
    method: 'get'
  })
}

export function bjyaNlfsqkfx2Level(level) {
  return request({
    url: `/taiyuan/bjya-nlfsqkfx2/stat?name=${valueyuliang.value}-${level}`,
    method: 'get'
  })
}

export function statisticsNEWGET() {
  return request({
    url: `/taiyuan/statistics/list?pageSize=10000&rainfull=` + valueyuliang.value,
    method: 'get'
  })
}

export function bjyaSbdbqkNEW() {
  return request({
    url: `/taiyuan/bjya-sbdbqk/stat?name=` + valueyuliang.value,
    method: 'get'
  })
}

export function sbdbqkPhqNEW(name) {
  return request({
    url: `/taiyuan/${name}/stat?name=` + valueyuliang.value,
    method: 'get'
  })
}
export function bjyaSsddNEW() {
  return request({
    url: `/taiyuan/bjya-ssdd/stat?name=` + valueyuliang.value,
    method: 'get'
  })
}

export function bjyaRyddNEW() {
  return request({
    url: `/taiyuan/bjya-rydd/stat?name=` + valueyuliang.value,
    method: 'get'
  })
}

export function bjyaYdbcddNEW() {
  return request({
    url: `/taiyuan/bjya-cldd/stat?name=` + valueyuliang.value,
    method: 'get'
  })
}

export function bjyaYdbcddGET() {
  return request({
    url: `/taiyuan/bjya-ydbcdd/stat?name=` + valueyuliang.value,
    method: 'get'
  })
}






export function sbdbqkPhq(item) {
  return request({
    url: `/taiyuan/${item}/stat?name=` + valueyuliang.value,
    method: 'get'
  })
}
export function paihongquGet(item) {
  return request({
    url: `/taiyuan/${item}/stat?name=40`,
    method: 'get'
  })
}

export function getLocationsingleCallPolice(params) {
  return request({
    url: `/taiyuan/yldsbxx/list?pageSize=10000&sbname=${params}`,
    method: 'get'
  })
}

// export function sbdbqkHhcpsnl() {
//   return request({
//     url: `/taiyuan/sbdbqk-hhcpsnl/stat?name=40`,
//     method: 'get'
//   })
// }
// export function sbdbqkHhchhsj() {
//   return request({
//     url: `/taiyuan/sbdbqk-hhchhsj/stat?name=40`,
//     method: 'get'
//   })
// }