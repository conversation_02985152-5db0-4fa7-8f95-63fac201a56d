<template>
  <div class="roow-div">
    <div ref="mapRef" id="player" class="map-container"></div>
    <div class="screen-wrap">
      <!-- 导航栏 -->
      <div class="nav-bar">
        <div
          v-for="(item, index) in navItems"
          :key="index"
          class="nav-item"
          :class="{ active: activeNavButton === index }"
          @click="changeNav(index)"
        >
          {{ item.name }}
        </div>
      </div>

      <!-- 中间顶部背景图 -->
      <div class="middle-top">
        <div class="section-title">内涝安全预警监测综合驾驶舱系统</div>
      </div>

      <div class="middle-content" v-if="activeNavButton === 1">
        <div class="top-tools">
          <el-button
            style="width: 150px; height: 100px; font-size: 30px; border-radius: 8px"
            type="primary"
            size="default"
            @click="hdlStart"
            >播放推演
          </el-button>
        </div>
        <div class="progress-wrapper">
          <el-slider
            class="floating-progress"
            show-tooltip="false"
            v-model="progressValue"
            :step="1"
            :min="0"
            :max="10"
            :marks="twoDWaterSliderMarks"
            @change="handleProgressChange"
          />

          <!-- <el-button
            style="width: 150px; height: 100px; font-size: 30px; border-radius: 8px"
            type="primary"
            size="default"
            @click="hdlStart"
            >播放推演
          </el-button> -->
        </div>
        <!-- <div class="progress-wrapper">
          <el-slider
            class="floating-progress"
            show-tooltip="false"
            v-model="progressValue"
            :step="1"
            :min="0"
            :max="10"
            :marks="twoDWaterSliderMarks"
            @change="handleProgressChange"
          />
        </div> -->
      </div>

      <!-- 中间底部背景图 -->
      <div class="middle-bottom"></div>

      <!-- 这个里面放引用过来的组件 -->
      <div>
        <home
          v-if="activeNavButton === 0"
          @tagClicked="handleTagData"
          @clickTable="handleClickTable"
          @videoClicked="handleVideoClicked"
          @yuntuClickShow="yuntuClickShowP"
          @tagClickedbranch="handleTagDataBranch"
          @clickGoods="handleGoods"
          @clickScene="handleScene"
          @clickBc="handleBc"
          @clickWz="handleWz"
        />
        <intelligence
          v-if="activeNavButton === 2"
          @clickTable="handleClickTable"
          @videoClicked="handleVideoClicked"
          @tagClicked="handleTagData"
          @yuntuClickShow="yuntuClickShowP"
        />
        <callPolice
          v-if="activeNavButton === 1"
          @tagClicked="handleTagData"
          @clickTable="handleClickTable"
          @clickScene="handleScene"
          @yuntuClickShow="yuntuClickShowP"
          :shikeValue="currentShikeValue"
        />
        <assistantdecision
          v-if="activeNavButton === 3"
          @tagClicked="handleTagData"
          @clickTable="handleClickTable"
          @yuntuClickShow="yuntuClickShowP"
          @clickDate="handleDate"
        />
      </div>

      <map-popup
        ref="mapPopupRef"
        v-model:visible="dialogVisible"
        title=""
        :show-close="true"
        :close-on-click-modal="true"
        :destroy-on-close="true"
        class="map-popup-container"
        :device-data="deviceData"
        :single-marker-data="singleMarkerData"
        @close="handleClose"
      >
        <!-- 弹窗内容已移至组件内部 -->
      </map-popup>
      <!-- 云图iframe弹窗 -->
      <div
        v-if="dialogVisibleYuntu && middleImgShow === 1"
        class="yuntu-iframe-dialog-overlay"
        @click="handleOverlayClick"
      >
        <div class="yuntu-iframe-dialog" @click.stop>
          <!-- 标题栏 -->
          <div class="dialog-header">
            <div class="dialog-title">云图</div>
            <div class="dialog-close" @click.stop="closeYuntuDialog">×</div>
          </div>

          <!-- 内容区域 -->
          <div class="dialog-content" @click.stop>
            <iframe
              src="https://www.aifcst.com/home"
              frameborder="0"
              width="100%"
              height="1000px"
              style="border: none; border-radius: 8px; pointer-events: auto"
              @load="handleIframeLoad"
              @error="handleIframeError"
            ></iframe>
          </div>
        </div>
      </div>

      <!-- 原有的云中快报弹窗 -->
      <el-dialog
        modal-class="yuntu"
        v-model="dialogVisibleYuntu"
        width="1170px"
        :close-on-click-modal="true"
        :show-close="true"
        :destroy-on-close="true"
        class="yuntu-dialog"
        v-if="middleImgShow === 2"
      >
        <div class="yunzhongkuaibao-img"></div>
      </el-dialog>

      <!-- 物资table弹窗 -->
      <el-dialog modal-class="supplies-dialog" v-model="yjwzdialogVisible" title="应急物资" width="1500">
        <div style="margin-top: 40px">
          <el-table
            :data="yjwztableData"
            height="700"
            style="width: 100%; margin-bottom: 32px; background: transparent"
          >
            <el-table-column prop="wzlx" label="名称" />
            <el-table-column prop="ckname" label="仓库" />
            <el-table-column prop="jldw" label="单位" />
            <el-table-column prop="sl" label="数量" />
          </el-table>

          <el-pagination
            v-model:current-page="yjwzPageNum"
            v-model:page-size="yjwzPageSize"
            :page-sizes="[20, 40, 80, 100, 200]"
            size="large"
            layout="total, prev, pager, next, jumper"
            :total="yjwzTotal"
            @size-change="handleSizeChangeyjwz"
            @current-change="handleCurrentChangeyjwz"
          />
        </div>
      </el-dialog>

      <!-- 自定义弹窗 -->
      <div v-if="deviceDialogVisible" class="device-dialog-overlay">
        <div class="device-dialog" @click.stop>
          <!-- 弹窗头部 -->
          <div class="device-dialog-header">
            <div class="device-dialog-title">{{ deviceDetails?.sbname }}</div>
            <div class="device-dialog-close" @click="handledeviceClose">×</div>
          </div>

          <!-- 弹窗内容 -->
          <div class="device-dialog-body">
            <div class="device-dialog-content">
              <!-- 监测数据区域 -->
              <div class="monitoring-section">
                <div class="monitoring-title">监测数据</div>

                <!-- 图表区域 -->
                <div class="charts-container">
                  <div class="chart-item">
                    <div class="chart-wrapper" ref="chart1Ref"></div>
                  </div>
                  <div class="chart-item">
                    <div class="chart-wrapper" ref="chart2Ref"></div>
                  </div>
                </div>

                <!-- 数据表格区域 -->
                <div class="data-table-section">
                  <el-tabs v-model="tabsCctiveName" class="tas">
                    <el-tab-pane label="泵站组态" name="bz">
                      <img :src="bengzhanImage" class="preview-image" />
                    </el-tab-pane>
                    <el-tab-pane label="汇水分区" name="hsfq">
                      <div style="font-size: 24px">所属分区：汇水分区</div>
                    </el-tab-pane>
                    <el-tab-pane label="管网信息" name="ssfq">
                      <div style="font-size: 24px">管网信息：管网信息</div>
                    </el-tab-pane>
                    <el-tab-pane label="视频监控" name="spjk">
                      <div class="video-section">
                        <div class="video-content">
                          <!-- 视频信息列表 -->
                          <div class="video-info-list">
                            <div
                              v-for="(item, index) in videoData"
                              :key="index"
                              class="video-info"
                              :class="{ active: selectedVideoIndex === index }"
                              @click="selectVideo(index)"
                            >
                              {{ item.name || '暂无视频名称' }}
                            </div>
                          </div>
                          <!-- 视频播放器 -->
                          <div class="video-player" v-if="selectedVideo">
                            <img
                              :src="`https://picsum.photos/397/199?random=${Math.floor(Math.random() * 1000)}`"
                              alt="视频占位图"
                              style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px"
                            />
                          </div>
                          <!-- 无视频时的占位 -->
                          <div class="video-player-placeholder" v-else>
                            <div class="placeholder-text">请选择视频</div>
                          </div>
                        </div>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="权责信息" name="qzxx">
                      <div class="table-container">
                        <table class="info-table">
                          <thead>
                            <tr>
                              <th>泵站</th>
                              <th>权责部门</th>
                              <th>权责类型</th>
                              <th>联系方式</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr v-for="(item, index) in qzxxData" :key="index">
                              <td>
                                <div class="pump-station-cell">
                                  <img
                                    :src="bengzhanImage"
                                    alt="泵站"
                                    class="pump-station-thumbnail"
                                    @click="openImagePreview(bengzhanImage, '泵站图片')"
                                  />
                                </div>
                              </td>

                              <td>{{ item.qzbmqy || '-' }}</td>
                              <td>{{ item.qzlx || '-' }}</td>
                              <td style="cursor: pointer" @click="handlePhoneCall(item.lxfs)">
                                {{ item.lxfs || '-' }}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图片预览弹窗 -->
      <div v-if="imagePreviewVisible" class="image-preview-overlay" @click="closeImagePreview">
        <div class="image-preview-dialog" @click.stop>
          <!-- 弹窗头部 -->
          <div class="image-preview-header">
            <div class="image-preview-title">{{ imagePreviewTitle }}</div>
            <div class="image-preview-close" @click="closeImagePreview">×</div>
          </div>

          <!-- 图片内容 -->
          <div class="image-preview-content">
            <img :src="imagePreviewSrc" :alt="imagePreviewTitle" class="preview-image" />
          </div>
        </div>
      </div>

      <!-- 防汛车辆弹窗 -->
      <div v-if="carPreviewVisible" class="car-preview-overlay" @click="closeCarPreview">
        <!-- 弹窗头部 -->
        <div class="car-dialog-header">
          <div class="car-dialog-title">防汛车辆信息</div>
          <div class="car-dialog-close" @click="closeCarPreview">×</div>
        </div>
        <div class="car-preview-dialog">
          <div class="car-info">车牌号码：{{ deviceData?.cphm }}</div>
          <div class="car-info">车辆类型：{{ deviceData?.cllx }}</div>
          <div class="car-info">驾驶⼈姓名：{{ deviceData?.jsrxm }}</div>
          <div class="car-info">驾驶⼈联系电话：{{ deviceData?.jsrlxdh }}</div>
          <div class="car-info">所属部⻔：{{ deviceData?.ssbm }}</div>
          <div class="car-info">责任⼈姓名：{{ deviceData?.zrrxm }}</div>
        </div>
      </div>
      <!-- 应急泵车弹窗 -->
      <div v-if="yjbcPreviewVisible" class="car-preview-overlay" @click="closeyjbcPreview">
        <!-- 弹窗头部 -->
        <div class="car-dialog-header">
          <div class="car-dialog-title">应急泵车</div>
          <div class="car-dialog-close" @click="closeyjbcPreview">×</div>
        </div>
        <div class="car-preview-dialog">
          <div class="car-info">车牌号码：{{ deviceData?.cphm }}</div>
          <div class="car-info">车辆类型：{{ deviceData?.cllx }}</div>
          <div class="car-info">驾驶⼈姓名：{{ deviceData?.jsrxm }}</div>
          <div class="car-info">驾驶⼈联系电话：{{ deviceData?.jsrlxdh }}</div>
          <div class="car-info">所属部⻔：{{ deviceData?.ssbm }}</div>
          <div class="car-info">责任⼈姓名：{{ deviceData?.zrrxm }}</div>
        </div>
      </div>

      <!-- 单点表格弹窗 -->
      <div v-if="tablePreviewVisible" class="table-preview-overlay" @click="closeTablePreview">
        <!-- 弹窗头部 -->
        <!-- 预报积水点统计 -->
        <div class="car-dialog-header">
          <div class="car-dialog-title" v-if="singleMarkerData.tableData.name">
            {{ singleMarkerData.tableData.name }}
          </div>
          <!-- 设备报警情况 -->
          <div v-if="singleMarkerData.tableData.device_name" class="car-dialog-title">
            <span class="popup-value">{{ singleMarkerData.tableData.device_name }}</span>
          </div>
          <!-- 积涝统计 -->
          <div v-if="singleMarkerData.tableData?.station" class="car-dialog-title">
            <span class="popup-value">{{ singleMarkerData.tableData?.station }}</span>
          </div>
          <div v-if="singleMarkerData.tableData.bcname" class="car-dialog-title">
            名称： <span class="popup-value">{{ singleMarkerData.tableData.bcname }}</span>
          </div>
          <div v-if="singleMarkerData.tableData.wzname" class="car-dialog-title">
            名称： <span class="popup-value">{{ singleMarkerData.tableData.wzname }}</span>
          </div>
          <div class="car-dialog-close" @click="closeCarPreview">×</div>
        </div>
        <div class="car-preview-dialog">
          <div class="car-info" v-if="singleMarkerData.tableData.type">
            类型：<span class="popup-value">{{ singleMarkerData.tableData.type }}</span>
          </div>
          <div class="car-info" v-if="singleMarkerData.tableData.alarmLevel">
            报警等级：<span class="popup-value">{{ singleMarkerData.tableData.alarmLevel }}</span>
          </div>
          <div class="car-info" v-if="singleMarkerData.tableData.grade">
            预报等级：<span class="popup-value">{{ singleMarkerData.tableData.grade }}</span>
          </div>
          <div class="car-info" v-if="singleMarkerData.tableData.waterDepth">
            水位：<span class="popup-value">{{ singleMarkerData.tableData.waterDepth }}</span>
          </div>
          <div class="car-info" v-if="singleMarkerData.tableData.forecastTime">
            报警时间：<span class="popup-value">{{ singleMarkerData.tableData.forecastTime }}</span>
          </div>
          <div class="car-info" v-if="singleMarkerData.tableData.name">
            地址：<span class="popup-value">{{ singleMarkerData.tableData.name }}</span>
          </div>
          <div class="car-info" v-if="singleMarkerData.tableData.waterTime">
            持续时间：<span class="popup-value">{{ singleMarkerData.tableData.waterTime }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.accumulated_area" class="car-info">
            积水面积：<span class="popup-value">{{ singleMarkerData.tableData?.accumulated_area }} ㎡</span>
          </div>
          <div v-if="singleMarkerData.tableData?.max_water_depth" class="car-info">
            最大水深：<span class="popup-value">{{ singleMarkerData.tableData?.max_water_depth }} m</span>
          </div>
          <div v-if="singleMarkerData.tableData?.alert_level" class="car-info">
            预警等级：<span class="popup-value">{{ singleMarkerData.tableData?.alert_level }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.alert_time" class="car-info">
            预警时间：<span class="popup-value">{{ singleMarkerData.tableData?.alert_time }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.eventGridName" class="car-info">
            事件位置：<span class="popup-value">{{ singleMarkerData.tableData?.eventGridName }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.eventName" class="car-info">
            上报人：<span class="popup-value">{{ singleMarkerData.tableData?.eventName }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.eventRepDepartName" class="car-info">
            事件处置部门<span class="popup-value">{{ singleMarkerData.tableData?.eventRepDepartName }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.eventStatusName" class="car-info">
            审核状态<span class="popup-value">{{ singleMarkerData.tableData?.eventStatusName }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.eventTypeName" class="car-info">
            事件描述：<span class="popup-value">{{ singleMarkerData.tableData?.eventTypeName }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.rptTime" class="car-info">
            事件发生事件：<span class="popup-value">{{ singleMarkerData.tableData?.rptTime }}</span>
          </div>

          <div v-if="singleMarkerData.tableData?.time_period" class="car-info">
            时段：<span class="popup-value">{{ singleMarkerData.tableData?.time_period }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.water_change" class="car-info">
            积水面积：<span class="popup-value">{{ singleMarkerData.tableData?.water_change }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.water_storage_time" class="car-info">
            积水深度：<span class="popup-value">{{ singleMarkerData.tableData?.water_storage_time }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.area_expansion" class="car-info">
            积水面积：<span class="popup-value">{{ singleMarkerData.tableData?.area_expansion }}</span>
          </div>

          <div v-if="singleMarkerData.tableData?.status" class="car-info">
            泵站状况：<span class="popup-value">{{ singleMarkerData.tableData?.status }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.pumping_volume" class="car-info">
            抽水量：<span class="popup-value">{{ singleMarkerData.tableData?.pumping_volume }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.discharge_volume" class="car-info">
            抽排量：<span class="popup-value">{{ singleMarkerData.tableData?.discharge_volume }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.shutdown_status" class="car-info">
            启停状况：<span class="popup-value">{{ singleMarkerData.tableData?.shutdown_status }}</span>
          </div>
          <div v-if="singleMarkerData.tableData?.location" class="car-info">
            地址：<span class="popup-value">{{ singleMarkerData.tableData?.location }}</span>
          </div>
          <div v-if="singleMarkerData.tableData.device_name" class="car-info">
            排水分区： <span class="popup-value">{{ singleMarkerData.tableData.device_name }}</span>
          </div>

          <div v-if="singleMarkerData.tableData.quantity" class="car-info">
            数量： <span class="popup-value">{{ singleMarkerData.tableData.quantity }}</span>
          </div>
          <div v-if="singleMarkerData.tableData.responsible_person" class="car-info">
            负责人： <span class="popup-value">{{ singleMarkerData.tableData.responsible_person }}</span>
          </div>
          <div v-if="singleMarkerData.tableData.response_time" class="car-info">
            响应时间： <span class="popup-value">{{ singleMarkerData.tableData.response_time }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { onMounted, ref, onUnmounted, nextTick } from 'vue'
import * as acapi from '../../../public/js/ac.min.js'
import assistantdecision from './assistantdecision.vue'
import home from './home.vue'
import { useRouter } from 'vue-router'
import callPolice from './callPolice1.vue'
import intelligence from './intelligence2.vue'
import { yingjiwuziRootTable } from '@/api/intelligence'
import * as echarts from 'echarts'
import MapPopup from '@/components/mapPopup.vue'
import bengzhanImage from '@/assets/images/bengzhan.png'
import {
  getDeviceDetails1,
  getSuppliesDetails,
  getCarData,
  getdeviceInfo,
  getwaterLevel,
  getQzxx,
  getVideoInfo,
  getWzData
} from '@/api/home.js'

const router = useRouter()
const phoneNumber = ref('') // 示例电话号码

// 处理拨号功能 - 在新页面打开
const handlePhoneCall = phone => {
  if (!phone) {
    console.warn('电话号码为空')
    return
  }

  // 导航到CallPhone页面并传递电话号码参数
  router.push({
    name: 'callPhone',
    query: {
      phoneNum: phone
    }
  })
}

const tabsCctiveName = ref('bz')
// 防汛车辆信息
const carPreviewVisible = ref(false)
const closeCarPreview = () => {
  carPreviewVisible.value = false
}
const tablePreviewVisible = ref(false)

const closeTablePreview = () => {
  tablePreviewVisible.value = false
}
//  防汛车辆
const getCarDataDeails = async id => {
  if (!id) {
    console.error('设备ID不存在')
    return
  }

  try {
    const result = await getCarData(id)
    // console.log('设备详情数据1111:', result)

    if (result && result.code === 200) {
      deviceData.value = result.rows[0]
      console.log('deviceData.value', deviceData.value)
    } else {
      console.error('获取防汛数据失败或数据为空,调用应急物资数据')
      deviceData.value = null
    }
  } catch (error) {
    console.error('获取设备详情失败:', error)
    deviceData.value = null
  }
}
// 应急泵车
const yjbcPreviewVisible = ref(false)
const closeyjbcPreview = () => {
  yjbcPreviewVisible.value = false
}
//  应急泵车
const getYJBCDataDeails = async id => {
  if (!id) {
    console.error('设备ID不存在')
    return
  }

  try {
    const result = await getCarData(id)
    // console.log('设备详情数据1111:', result)

    if (result && result.code === 200) {
      deviceData.value = result.rows[0]
      console.log('deviceData.value', deviceData.value)
    } else {
      console.error('获取防汛数据失败或数据为空,调用应急物资数据')
      deviceData.value = null
    }
  } catch (error) {
    console.error('获取设备详情失败:', error)
    deviceData.value = null
  }
}

// 物资

const wzPreviewVisible = ref(false)
const closewzPreview = () => {
  wzPreviewVisible.value = false
}
//  应急泵车
const getWzDataDetails = async id => {
  if (!id) {
    console.error('设备ID不存在')
    return
  }
  yingjiwuziRootTable(cbdIdValue.value, yjwzPageNum.value, yjwzPageSize.value).then(res => {
    console.log(res, 'RootTable')
    yjwzdialogVisible.value = true
    if (res.code === 200) {
      yjwztableData.value = res.rows
      yjwzTotal.value = res.total
    }
  })
}

const deviceDialogVisible = ref(false)
const chart1Ref = ref(null)
const chart2Ref = ref(null)
let chart1Instance = null
let chart2Instance = null

// 图片预览相关变量
const imagePreviewVisible = ref(false)
const imagePreviewSrc = ref('')
const imagePreviewTitle = ref('')

// 图片预览方法
const openImagePreview = (imageSrc, title = '图片预览') => {
  imagePreviewSrc.value = imageSrc
  imagePreviewTitle.value = title
  imagePreviewVisible.value = true
}

const closeImagePreview = () => {
  imagePreviewVisible.value = false
  imagePreviewSrc.value = ''
  imagePreviewTitle.value = ''
}

// 键盘事件处理
const handleKeydown = event => {
  if (event.key === 'Escape' && imagePreviewVisible.value) {
    closeImagePreview()
  }
}

const handledeviceClose = () => {
  deviceDialogVisible.value = false
  // 销毁图表实例
  if (chart1Instance) {
    chart1Instance.dispose()
    chart1Instance = null
  }
  if (chart2Instance) {
    chart2Instance.dispose()
    chart2Instance = null
  }
}

// 初始化图表
const initCharts = async () => {
  // await nextTick()

  if (chart1Ref.value) {
    chart1Instance = echarts.init(chart1Ref.value)

    // 处理水位数据
    let xAxisData = []
    let seriesData = []
    let yAxisMin = 0
    let yAxisMax = 10

    if (waterLevelData.value && waterLevelData.value.length > 0) {
      // 从waterLevelData中提取时间和水位数据
      xAxisData = waterLevelData.value.map(item => {
        // 格式化时间显示，只显示时:分:秒
        const date = new Date(item.time)
        return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(
          date.getSeconds()
        ).padStart(2, '0')}`
      })

      seriesData = waterLevelData.value.map(item => parseFloat(item.waterlevel) || 0)

      // 动态计算Y轴范围
      if (seriesData.length > 0) {
        const minValue = Math.min(...seriesData)
        const maxValue = Math.max(...seriesData)
        const range = maxValue - minValue
        yAxisMin = Math.max(0, minValue - range * 0.1) // 最小值向下扩展10%，但不小于0
        yAxisMax = maxValue + range * 0.1 // 最大值向上扩展10%
      }
    } else {
      // 默认数据（当没有水位数据时）
      xAxisData = ['03:49:00', '05:07:00', '05:03:00', '05:10:00', '05:12:00']
      seriesData = [0, 0, 0, 0, 0]
    }

    const option1 = {
      backgroundColor: 'transparent',
      grid: {
        left: '10%',
        right: '5%',
        top: '5%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 24,
          margin: 8,
          // rotate: 45, // 旋转标签以避免重叠
          formatter: function (value) {
            // 如果标签太长，可以进一步简化
            return value
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '',
        nameTextStyle: {
          color: '#ffffff',
          fontSize: 24,
          show: false
        },
        // min: yAxisMin,
        // max: yAxisMax,
        min: 0,
        max: 10,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 24
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.2)',
            width: 1,
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '水位',
          data: seriesData,
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#00ff88',
            width: 2
          },
          itemStyle: {
            color: '#00ff88',
            borderColor: '#00ff88',
            borderWidth: 2
          },
          symbol: 'circle',
          symbolSize: 4,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(0, 255, 136, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(0, 255, 136, 0.05)'
                }
              ]
            }
          }
        }
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ff88',
        borderWidth: 1,
        textStyle: {
          color: '#ffffff'
        },
        formatter: function (params) {
          if (params && params.length > 0) {
            const data = params[0]
            return `时间: ${data.axisValue}<br/>水位: ${data.value} cm`
          }
          return ''
        }
      }
    }
    chart1Instance.setOption(option1)
  }

  if (chart2Ref.value) {
    chart2Instance = echarts.init(chart2Ref.value)
    const option2 = {
      backgroundColor: 'transparent',
      grid: {
        left: '0%',
        right: '10%',
        top: '5%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['尖草坪区', '杏花岭区', '小店区', '迎泽区', '万柏林区'],
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 24,
          margin: 10
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 150,
        interval: 30,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 24
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.2)',
            width: 1,
            type: 'dashed'
          }
        }
      },
      series: [
        {
          data: [80, 60, 90, 110, 80, 70, 100, 90, 70, 110],
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#00ff88',
            width: 2
          },
          itemStyle: {
            color: '#00ff88',
            borderColor: '#00ff88',
            borderWidth: 2
          },
          symbol: 'circle',
          symbolSize: 4,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(0, 255, 136, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(0, 255, 136, 0.05)'
                }
              ]
            }
          }
        }
      ]
    }
    chart2Instance.setOption(option2)
  }
}

// 更新图表数据
const updateChart1WithWaterLevel = () => {
  if (!chart1Instance || !waterLevelData.value) return

  // 处理水位数据
  let xAxisData = []
  let seriesData = []
  let yAxisMin = 0
  let yAxisMax = 10

  if (waterLevelData.value && waterLevelData.value.length > 0) {
    // 从waterLevelData中提取时间和水位数据
    xAxisData = waterLevelData.value.map(item => {
      // 格式化时间显示，只显示时:分:秒
      const date = new Date(item.time)
      return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(
        date.getSeconds()
      ).padStart(2, '0')}`
    })

    seriesData = waterLevelData.value.map(item => parseFloat(item.waterlevel) || 0)

    // 动态计算Y轴范围
    if (seriesData.length > 0) {
      const minValue = Math.min(...seriesData)
      const maxValue = Math.max(...seriesData)
      const range = maxValue - minValue
      yAxisMin = Math.max(0, minValue - range * 0.1) // 最小值向下扩展10%，但不小于0
      yAxisMax = maxValue + range * 0.1 // 最大值向上扩展10%
    }
  } else {
    // 默认数据（当没有水位数据时）
    xAxisData = ['03:49:00', '05:07:00', '05:03:00', '05:10:00', '05:12:00']
    seriesData = [0, 0, 0, 0, 0]
  }

  // 更新图表配置
  chart1Instance.setOption({
    xAxis: {
      data: xAxisData
    },
    yAxis: {
      // min: yAxisMin,
      // max: yAxisMax
      min: 0,
      max: 150
    },
    series: [
      {
        data: seriesData
      }
    ]
  })
}

const yjwzdialogVisible = ref(false)
const yjwztableData = ref()
const yjwzPageNum = ref(1)
const yjwzPageSize = ref(20)
const yjwzTotal = ref(0)
const cbdIdValue = ref('')
// 地图实例
const mapRef = ref(null)
const progressValue = ref(0)
const twoDWaterSliderMarks = ref([
  '00:10:00',
  '00:20:00',
  '00:30:00',
  '00:40:00',
  '00:50:00',
  '01:00:00',
  '01:10:00',
  '01:20:00',
  '01:30:00',
  '01:40:00',
  '01:50:00'
])

const dialogVisibleYuntu = ref(false)
const yuntuClickShowP = flag => {
  dialogVisibleYuntu.value = true
  middleImgShow.value = flag
}

const middleImgShow = ref(1)

// iframe加载处理函数
const handleIframeLoad = () => {
  console.log('云图iframe加载成功')
}

const handleIframeError = () => {
  console.error('云图iframe加载失败')
}

// 关闭云图弹窗
const closeYuntuDialog = () => {
  dialogVisibleYuntu.value = false
  middleImgShow.value = null
}

// 点击遮罩层关闭弹窗
const handleOverlayClick = event => {
  // 只有点击遮罩层本身时才关闭弹窗
  if (event.target.classList.contains('yuntu-iframe-dialog-overlay')) {
    dialogVisibleYuntu.value = false
  }
}
// 控制弹窗显示
const dialogVisible = ref(false)

// 存储鼠标点击位置的响应式变量
const mousePosition = ref({
  x: 0,
  y: 0,
  timestamp: null
})

// 控制点击指示器的显示
const showClickIndicator = ref(false)

// 处理全局鼠标点击事件
const handleGlobalClick = event => {
  clickX.value = event.clientX
  clickY.value = event.clientY
  console.log('全局鼠标点击位置:', clickX.value, clickY.value)
  // dialogVisible.value = true
  // 显示点击指示器
  showClickIndicator.value = true

  // 1秒后隐藏点击指示器
  setTimeout(() => {
    showClickIndicator.value = false
  }, 1000)
}

const activeNavButton = ref(0)
const currentShikeValue = ref('')
const hdlStart = () => {
  progressValue.value = 0
  let i = 1
  var interval = setInterval(() => {
    console.log(progressValue.value, 'progressValue.value')
    progressValue.value = i
    if (i <= twoDWaterSliderMarks.value.length) {
      currentShikeValue.value = twoDWaterSliderMarks.value[i]
    }
    handleProgressChange(i++)
    if (i >= twoDWaterSliderMarks.value.length) {
      clearInterval(interval)
    }
  }, 3000)
}
// 处理进度条变化
const handleProgressChange = value => {
  console.log(value, 'value')
  update2DWater(value)
}
// 随时间节点变化更新
const update2DWater = async value => {
  // 获取对应的时间字符串
  const timeStr = String(twoDWaterSliderMarks.value[value])

  const formattedTime = timeStr.replace(/:/g, '')

  let hydrodynamicModel_for_update = {
    id: 'hdm_shp_clip',
    updateTime: 1,
    shpDataFilePath: `@path:screenfile/water2D/dat/40mm/hydrodynamic_20250416_${formattedTime}.dat`
  }
  console.log(`@path:water2D/dat/40mm/hydrodynamic_20250416_${formattedTime}.dat`)
  console.log(hydrodynamicModel_for_update, 'hydrodynamicModel_for_update')
  await fdapi.hydrodynamic2d.update(hydrodynamicModel_for_update)
}
// 导航栏配置
const navItems = ref([{ name: '综合展示' }, { name: '报警演案' }, { name: '智能感知' }, { name: '辅助决策' }])

// 处理弹窗关闭事件
const handleClose = () => {
  console.log('弹窗关闭')
  dialogVisible.value = false
  // 确保关闭后重置设备数据
  deviceData.value = null
  singleMarkerData.value = null
}

// 切换导航
const changeNav = index => {
  console.log(index, '切换index')
  activeNavButton.value = index
  fdapi.weather.disableRainSnow()
  if (index === 1) {
    // 报警演案加载地图水淹
    init2DWater()
  } else {
    // 移除地图水淹
    fdapi.hydrodynamic2d.clear()
  }
}

const init2DWater = async () => {
  fdapi.hydrodynamic2d.clear()
  let hydrodynamic2d_add = {
    id: 'hdm_shp_clip', // HydroDynamic2D对象ID
    displayMode: 1, // 水流场样式： 0水面 1热力 2流场
    waterMode: 0, // 水面显示模式，枚举类型 0 水动画模式 ,1水仿真模式,2 水流向模式
    // offset: [0, 0, -18], //整体水面高度修正
    collision: false, //开启碰撞
    updateTime: 0,
    shpFilePath: '@path:screenfile/water2D/shp/processed_grid.shp', //预加载的水动力模型预演范围shp
    colors: {
      gradient: false,
      invalidColor: [0, 0, 0, 0],
      colorStops: [
        { value: 0, color: [0.22, 0.659, 0.0, 1] },
        { value: 0.15, color: [0.298, 0.902, 0.0, 1] },
        { value: 0.27, color: [1.0, 1.0, 0.0, 1] },
        { value: 0.4, color: [1.0, 0.667, 0.0, 1] },
        { value: 0.6, color: [1.0, 0.0, 0.0, 1] }
      ]
    }
  }
  await fdapi.hydrodynamic2d.add(hydrodynamic2d_add)
  //fdapi.hydrodynamic2d.focus('hdm_shp_clip', 1000);  //飞入 调试时可用
  let hydrodynamicModel_for_update = {
    id: 'hdm_shp_clip',
    updateTime: 3,
    shpDataFilePath: '@path:screenfile/water2D/dat/40mm/hydrodynamic_20250416_001000.dat'
  }
  fdapi.hydrodynamic2d.update(hydrodynamicModel_for_update)

  console.log('-------加载完成---------')
}
const tagDataList = ref(null)

// 应急物资
// 处理子组件传来的标签数据
const handleGoods = data => {
  tagDataList.value = data
  console.log('rootMap接收到的数据:', data)
  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData
    const tagData = data.tagData
    console.log(locations, 'locations')
    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        console.log(location, 'location111111111111111111111')
        if (location.xcoordinate && location.ycoordinate) {
          // 确保有ID，如果没有则生成一个唯一ID
          const markerId = location.id

          // 创建标记配置
          const goodsMarker = {
            id: markerId,
            groupId: 'goodsLocations',
            coordinate: [parseFloat(location.xcoordinate), parseFloat(location.ycoordinate), 0],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-25, 50],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: '@path:screenfile/zhtb/cl.png',
            hoverImagePath: '@path:screenfile/zhtb/cl.png',
            fixedSize: true,
            text: location.category,
            useTextAnimation: false,
            textRange: [1, 1000],
            textOffset: [0, 0],
            fontSize: 24,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 2
          }
          console.log(goodsMarker, 'goodsMarker')

          markers.push(goodsMarker)
        }
      })

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log(markers, '最终的打点数据')

        fdapi.marker.add(markers)
        fdapi.marker.focus(markers[0].id, 1200, 0)
        console.log('标记点添加成功，总数:', markers.length)
      } else {
        console.log('没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}
// 处理打点-经纬度分开版-防汛车辆
const handleTagDataBranch = data => {
  tagDataList.value = data
  console.log('rootMap接收到的数据:', data)
  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData
    console.log(locations, 'locations')
    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        console.log(location, 'location111111111111111111111')
        if (location.jd && location.wd) {
          // 确保有ID，如果没有则生成一个唯一ID
          // 防汛车辆
          const markerId = 'FXCL_' + location.sbbh
          // 创建标记配置
          const markerCar = {
            id: markerId,
            groupId: 'tagLocations',
            coordinate: [parseFloat(location.jd), parseFloat(location.wd)],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: '@path:screenfile/zhtb/cl.png',
            hoverImagePath: '@path:screenfile/zhtb/cl.png',
            fixedSize: true,
            text: location.cphm,
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 24,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true
          }
          console.log(markerCar, 'marker')

          markers.push(markerCar)
        }
      })

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log(markers, '最终的打点数据')

        fdapi.marker.add(markers)
        fdapi.marker.focus(markers[0].id, 1200, 0)
        console.log('标记点添加成功，总数:', markers.length)
      } else {
        console.log('没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}

// 处理物资打点
const handleWz = data => {
  tagDataList.value = data
  console.log('rootMap接收到的数据:', data)
  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData
    console.log(locations, 'locations')
    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        console.log(location, 'location111111111111111111111')
        if (location.xcoordinate && location.ycoordinate) {
          // 确保有ID，如果没有则生成一个唯一ID
          // 防汛车辆
          const markerId = 'WZ_' + location.cbdId
          // 创建标记配置
          const markerCar = {
            id: markerId,
            groupId: 'wzLocations',
            coordinate: [parseFloat(location.xcoordinate), parseFloat(location.ycoordinate)],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: '@path:screenfile/zhtb/phq.png',
            hoverImagePath: '@path:screenfile/zhtb/phq.png',
            fixedSize: true,
            text: location.ckname,
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 24,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true
          }
          console.log(markerCar, 'marker')

          markers.push(markerCar)
        }
      })

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log(markers, '最终的打点数据')

        fdapi.marker.add(markers)
        fdapi.marker.focus(markers[0].id, 1200, 0)
        console.log('标记点添加成功，总数:', markers.length)
      } else {
        console.log('没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}

// 处理泵车打点
const handleBc = data => {
  tagDataList.value = data
  console.log('rootMap接收到的数据:', data)
  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData
    console.log(locations, 'locations')
    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        console.log(location, 'location111111111111111111111')
        if (location.jd && location.wd) {
          // 确保有ID，如果没有则生成一个唯一ID
          // 防汛车辆
          const markerId = 'YJBC_' + location.sbbh
          // 创建标记配置
          const markerCar = {
            id: markerId,
            groupId: 'bcLocations',
            coordinate: [parseFloat(location.jd), parseFloat(location.wd)],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: '@path:screenfile/zhtb/bc.png',
            hoverImagePath: '@path:screenfile/zhtb/bc.png',
            fixedSize: true,
            text: location.cphm,
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 24,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true
          }
          console.log(markerCar, 'marker')

          markers.push(markerCar)
        }
      })

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log(markers, '最终的打点数据')

        fdapi.marker.add(markers)
        fdapi.marker.focus(markers[0].id, 1200, 0)
        console.log('标记点添加成功，总数:', markers.length)
      } else {
        console.log('没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}

// 处理场景分类-设备信息打点
const handleScene = data => {
  tagDataList.value = data
  console.log('rootMap接收到的数据:', data)
  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData
    const tagData = data.tagData
    console.log(locations, 'locations')
    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        console.log(location, 'location111111111111111111111')
        if (location.xcoordinate && location.ycoordinate) {
          // 确保有ID，如果没有则生成一个唯一ID
          const markerId = location.yldId

          // 创建标记配置
          const marker = {
            id: markerId,
            groupId: 'senceLocations',
            coordinate: [parseFloat(location.xcoordinate), parseFloat(location.ycoordinate), 0],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: tagData.icon2 || '@path:screenfile/zhtb/cl.png',
            hoverImagePath: tagData.icon2 || '@path:screenfile/zhtb/cl.png',
            fixedSize: true,
            text: location.sbname,
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 24,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true
          }
          console.log(marker, 'marker')

          markers.push(marker)
        }
      })

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log(markers, '最终的打点数据')

        fdapi.marker.add(markers)
        fdapi.marker.focus(markers[0].id, 1200, 0)
        console.log('标记点添加成功，总数:', markers.length)
      } else {
        console.log('没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}

// 降雨日历打点
const handleDate = data => {
  tagDataList.value = data
  console.log('降雨日历打点数据', data)
  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData
    console.log(locations, 'locations')
    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()
      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        console.log(location, 'location111111111111111111111')
        if (location.xcoordinate && location.ycoordinate) {
          // 确保有ID，如果没有则生成一个唯一ID
          const markerId = location.id
          // 创建标记配置
          const marker = {
            id: markerId,
            groupId: 'dateLocations',
            coordinate: [parseFloat(location.xcoordinate), parseFloat(location.ycoordinate), 0],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: '@path:screenfile/zhtb/llj.png',
            hoverImagePath: '@path:screenfile/zhtb/llj.png',
            fixedSize: true,
            text: location.name,
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 24,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true,
            popupURL: '@path:screenfile/zhtb/bz.png'
          }
          console.log(marker, 'marker')

          markers.push(marker)
        }
      })

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log(markers, '最终的打点数据')

        fdapi.marker.add(markers)
        fdapi.marker.focus(markers[0].id, 1200, 0)
        // markers.forEach(marker => {
          // fdapi.marker.showPopupWindow(marker.id, function () {
          //   console.log('弹窗已打开', marker.id)
          // })
          fdapi.marker.showAllPopupWindow( function () {
            console.log('marker1和marker2文本背景色设置为半透明绿色')
          })
        // })
        console.log('标记点添加成功，总数:', markers.length)
      } else {
        console.log('没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}
// 处理子组件传来的标签数据
const handleTagData = data => {
  tagDataList.value = data
  console.log('rootMap接收到的数据:', data)
  // 这里可以处理接收到的数据，例如在地图上标记位置等
  if (data) {
    // 处理位置数据
    const locations = data.locationData
    const tagData = data.tagData || []
    console.log(locations, 'locations')
    // 可以在地图上标记这些位置
    try {
      // 先清除现有标记
      fdapi.marker.clear()

      // 遍历位置数据并添加标记
      let markers = []
      locations.forEach((location, index) => {
        console.log(location, 'location111111111111111111111')
        if (location.xcoordinate && location.ycoordinate) {
          // 确保有ID，如果没有则生成一个唯一ID
          const markerId = location.id

          // 创建标记配置
          const marker = {
            id: markerId,
            groupId: 'tagLocations',
            coordinate: [parseFloat(location.xcoordinate), parseFloat(location.ycoordinate), 0],
            coordinateType: 1, // 经纬度坐标系
            anchors: [-15, 30],
            imageSize: [30, 30],
            hoverImageSize: [30, 30],
            range: [100, 10000000],
            viewHeightRange: [100, 10000000],
            rangeRatio: 0.01,
            imagePath: tagData.icon2 || '@path:screenfile/zhtb/cl.png',
            hoverImagePath: tagData.icon2 || '@path:screenfile/zhtb/cl.png',
            fixedSize: true,
            text: location.sbname,
            userData: location.taiyuanFlag + '?' + location.cbdId, // 传入marker数据, taiyuanFlag手动定义，智能感知里面的应急物资中使用
            useTextAnimation: true,
            textRange: [1, 1000000000],
            textOffset: [0, 0],
            fontSize: 24,
            fontColor: window.Color ? window.Color.White : '#FFFFFF',
            fontOutlineColor: window.Color ? window.Color.Black : '#000000',
            showLine: true,
            lineSize: [2, 100],
            lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
            displayMode: 4,
            autoHeight: true
          }
          console.log(marker, 'marker')

          markers.push(marker)
        }
      })

      // 只有当markers数组不为空时才添加标记和聚焦
      if (markers.length > 0) {
        console.log(markers, '最终的打点数据')

        fdapi.marker.add(markers)
        fdapi.marker.focus(markers[0].id, 1200, 0)
        console.log('标记点添加成功，总数:', markers.length)
      } else {
        console.log('没有有效的位置数据可添加标记')
      }
    } catch (error) {
      console.error('添加标记点失败:', error)
    }
  }
}

// 单个点位数据
const singleMarkerData = ref(null)

// 单个地图打点
const handleClickTable = data => {
  singleMarkerData.value = data

  // 检查数据是否存在
  if (!data || !data.tableData) {
    console.error('无效的表格数据')
    return
  }

  const tableData = data.tableData
  console.log(JSON.stringify(tableData), 'tableData', '单个地图打点data')

  // 检查是否有坐标数据
  if (!tableData.coordinate) {
    console.error('坐标数据不存在')
    return
  }

  try {
    // 分割坐标字符串为经纬度数组
    const coordinates = tableData.coordinate.split(',').map(coord => parseFloat(coord))
    console.log(coordinates, 'coordinates')

    // 验证坐标数据有效性
    if (coordinates.length < 2 || isNaN(coordinates[0]) || isNaN(coordinates[1])) {
      console.error('无效的坐标数据:', tableData.coordinate)
      return
    }

    // 清除现有标记
    fdapi.marker.clear()

    // 确保有ID，如果没有则生成一个唯一ID
    const markerId = `TAB-${Date.now()}`

    // 创建新的标记
    const singleMarker = {
      id: markerId,
      groupId: 'singleMarker',
      coordinate: [coordinates[0], coordinates[1]], // 使用分割后的经纬度
      coordinateType: 1, // 经纬度坐标系
      anchors: [-15, 30],
      imageSize: [30, 30],
      hoverImageSize: [30, 30],
      range: [100, 10000000],
      viewHeightRange: [100, 10000000],
      rangeRatio: 0.01,
      imagePath: '@path:screenfile/zhtb/dw.png',
      hoverImagePath: '@path:screenfile/zhtb/dw.png',
      fixedSize: true,
      text:
        tableData.eventGridName ||
        tableData.station ||
        tableData.device_name ||
        tableData.name ||
        tableData.wzname ||
        tableData.bcname ||
        '',
      useTextAnimation: true,
      textRange: [1, 1000000000],
      textOffset: [0, 0],
      fontSize: 24,
      fontColor: window.Color ? window.Color.White : '#FFFFFF',
      fontOutlineColor: window.Color ? window.Color.Black : '#000000',
      showLine: true,
      lineSize: [2, 100],
      lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
      displayMode: 4,
      autoHeight: true
    }
    console.log(singleMarker, 'singleMarker')
    fdapi.marker.add([singleMarker])
    fdapi.marker.focus(singleMarker.id, 1000, 0)
    console.log('singleMarker打点成功')
  } catch (error) {
    console.error('添加单个标记点失败:', error)
  }
}

// 视频打点
const handleVideoClicked = data => {
  singleMarkerData.value = data

  // 检查数据是否存在
  if (!data || !data.tableData) {
    console.error('无效的视频数据')
    return
  }

  const tableData = data.tableData
  console.log(data, '单个地图打点data')

  try {
    // 清除现有标记
    fdapi.marker.clear()

    // 创建新的标记
    const videoMarker = {
      id: tableData.id,
      groupId: 'singleMarker',
      coordinate: [tableData.jd, tableData.wd, 0], // 使用分割后的经纬度
      coordinateType: 1, // 经纬度坐标系
      anchors: [-15, 30],
      imageSize: [30, 30],
      hoverImageSize: [30, 30],
      range: [100, 10000000],
      viewHeightRange: [1000, 10000000], // 增加视角高度下限，从200改为1000
      rangeRatio: 0.01,
      imagePath: '@path:screenfile/zhtb/bz.png',
      hoverImagePath: '@path:screenfile/zhtb/bz.png',
      fixedSize: true,
      text: tableData.cameraManufacturer || '',
      useTextAnimation: true,
      textRange: [1, 1000000000],
      textOffset: [0, 0],
      fontSize: 24,
      fontColor: window.Color ? window.Color.White : '#FFFFFF',
      fontOutlineColor: window.Color ? window.Color.Black : '#000000',
      showLine: true,
      lineSize: [2, 100],
      lineColor: window.Color ? window.Color.SpringGreen : '#00FF7F',
      displayMode: 4,
      autoHeight: true
    }
    fdapi.marker.add([videoMarker])
    fdapi.marker.focus(videoMarker.id, 1000, 0) // 增加飞入高度，从100改为1000
    console.log('videoMarker打点成功')
  } catch (error) {
    console.error('添加单个标记点失败:', error)
  }
}

const deviceData = ref()
const getDeviceData = async id => {
  if (!id) {
    console.error('设备ID不存在')
    return
  }

  try {
    const result = await getDeviceDetails1(id)
    // console.log('设备详情数据1111:', result)

    if (result && result.code === 200) {
      deviceData.value = result.data
      console.log('deviceData.value', deviceData.value)
    } else {
      console.error('获取设备数据失败或数据为空,调用防汛车辆数据')
      deviceData.value = null
    }
  } catch (error) {
    console.error('获取设备详情失败:', error)
    deviceData.value = null
  }
}

const getSuppliesData = async id => {
  if (!id) {
    console.error('设备ID不存在')
    return
  }

  try {
    const result = await getSuppliesDetails(id)
    // console.log('设备详情数据1111:', result)

    if (result && result.code === 200) {
      deviceData.value = result.data
      console.log('deviceData.value', deviceData.value)
    } else {
      console.error('获取应急物资数据失败或数据为空,调用其他数据')
      deviceData.value = null
    }
  } catch (error) {
    console.error('获取设备详情失败:', error)
    deviceData.value = null
  }
}
//  防汛车辆
// const getCarDataDeails = async id => {
//   if (!id) {
//     console.error('设备ID不存在')
//     return
//   }

//   try {
//     const result = await getCarData(id)
//     // console.log('设备详情数据1111:', result)

//     if (result && result.code === 200) {
//       deviceData.value = result.rows[0]
//       console.log('deviceData.value', deviceData.value)
//     } else {
//       console.error('获取防汛数据失败或数据为空,调用应急物资数据')
//       deviceData.value = null
//     }
//   } catch (error) {
//     console.error('获取设备详情失败:', error)
//     deviceData.value = null
//   }
// }
// 处理地图点击事件
const clickX = ref(0)
const clickY = ref(0)

const yingjiwuziRootGETTable = () => {
  yingjiwuziRootTable(cbdIdValue.value, yjwzPageNum.value, yjwzPageSize.value).then(res => {
    console.log(res, 'RootTable')
    yjwzdialogVisible.value = true
    if (res.code === 200) {
      yjwztableData.value = res.rows
      yjwzTotal.value = res.total
    }
  })
}

const deviceDetails = ref(null)
// 获取设备详情 event.id
const getDeviceDetails = async id => {
  try {
    const result = await getdeviceInfo(id)
    console.log('设通过名称查询设备信息备详情数据:', result)
    if (result && result.code === 200) {
      deviceDetails.value = result.rows[0]
      if (deviceDetails.value.eventsncode) {
        getwaterLevelData(deviceDetails.value.sncode)
      }
      console.log('设通过名称查询设备信息备详情数据成功', deviceDetails.value)
    } else {
      message.error('获取设备详情失败')
    }
  } catch (error) {
    console.error('获取设备详情失败:', error)
    deviceData.value = null
  }
}
const waterLevelData = ref(null)
const qzxxData = ref([])
// 获取设备水位数据 deviceDetails.sncode
const getwaterLevelData = async id => {
  try {
    const result = await getwaterLevel(id)
    console.log('获取设备水位数据:', result)
    if (result && result.code === 200) {
      waterLevelData.value = result.rows
      console.log('获取设备水位数据成功', waterLevelData.value)
      // 数据加载完成后更新图表
      setTimeout(() => {
        updateChart1WithWaterLevel()
      }, 100)
    } else {
      console.error('获取设备水位失败')
    }
  } catch (error) {
    console.error('获取设备水位失败:', error)
  }
}
// 获取权责信息event.id
const getQzxxData = async id => {
  try {
    const result = await getQzxx(id)
    console.log('获取权责信息:', result)
    if (result && result.code === 200) {
      qzxxData.value = result.rows
      console.log('获取权责信息成功', qzxxData.value)
    } else {
      console.error('获取权责信息失败')
    }
  } catch (error) {
    console.error('获取权责信息失败')
  }
}

const videoData = ref(null)
// 当前选中的视频索引
const selectedVideoIndex = ref(null)
// 当前选中的视频对象
const selectedVideo = ref(null)

// 选择视频的方法
const selectVideo = index => {
  selectedVideoIndex.value = index
  selectedVideo.value = videoData.value[index]
  console.log('选中视频:', selectedVideo.value)
}

// 获取视频信息-event.id
const getVideoData = async id => {
  try {
    const result = await getVideoInfo()
    console.log('获取视频信息:', result)
    if (result && result.code === 200) {
      videoData.value = result.rows
      console.log('获取视频信息成功', videoData.value)
      // 默认选中第一个视频
      if (videoData.value && videoData.value.length > 0) {
        selectVideo(0)
      }
    } else {
      console.error('获取视频信息失败')
    }
  } catch (error) {
    console.error('获取视频信息失败')
  }
}
// 连接数字孪生配置
const options = ref({
  domId: 'player', // 修改为新的ID
  resset: true,
  apiOptions: {
    onReady: function () {
      fdapi.settings.setMainUIVisibility(false)
      init2DWater()
    },
    onEvent: async function (event) {
      console.log('地图点击事件:', event)
      if (event.eventtype === 'LeftMouseButtonClick' && event.Type === 'marker') {
        // 这里处理应急物资的逻辑
        // 取marker里面的userDATA数据
        if (event.UserData) {
          // 确定应急物资后，需要展示弹窗显示当前仓库下的所有应急物资数据

          const yjwzSplit = event.UserData.split('?')
          if (yjwzSplit[0] === 'taiyuanFlag' && yjwzSplit[1]) {
            cbdIdValue.value = yjwzSplit[1]
            yingjiwuziRootGETTable()
            return
          }
        }

        if (event.Id.startsWith('YLD')) {
          getDeviceDetails(event.Id)
          getQzxxData(event.Id)
          getVideoData(event.Id)

          setTimeout(() => {
            deviceDialogVisible.value = true
          }, 1000)
          setTimeout(() => {
            initCharts()
          }, 1200)
          console.log('包含YLD')
        }
        // 防汛车辆弹窗
        if (event.Id.startsWith('FXCL_')) {
          await getYJBCDataDeails(event.Id.replace('FXCL_', ''))
          carPreviewVisible.value = true
          console.log('包含FXCL_')
          return
        }

        // 应急泵车弹窗
        if (event.Id.startsWith('YJBC_')) {
          await getCarDataDeails(event.Id.replace('YJBC_', ''))
          yjbcPreviewVisible.value = true
          console.log('YJBC_')
          return
        }
        // 物资弹窗
        if (event.Id.startsWith('WZ_')) {
          await getWzDataDetails(event.Id.replace('WZ_', ''))
          wzPreviewVisible.value = true
          console.log('WZ_')
          return
        }
        // 表格弹窗
        if (event.Id.startsWith('TAB')) {
          tablePreviewVisible.value = true
        }

        // 原有的marker点击处理逻辑
        // if (!singleMarkerData.value) {
        //   getDeviceData(event.Id)
        //   getCarDataDeails(event.Id)
        //   getSuppliesData(event.id)
        // }
        // console.log('event.Id:', event.Id, singleMarkerData.value, deviceData.value)
        // dialogVisible.value = true
      }
    }
    // resset: false
    // onReady: function () {
    //   console.info('此时可以调API了')
    //   fdapi.marker.clear()
    //   //支持经纬度坐标和普通投影坐标两种类型
    //   let o1 = {
    //     id: 'm1',
    //     groupId: 'markerAdd',
    //     coordinate: [112.53279714, 37.70480413, 0], //坐标位置
    //     coordinateType: 1, //默认0是投影坐标系，也可以设置为经纬度空间坐标系值为1
    //     anchors: [-25, 50], //锚点，设置Marker的整体偏移，取值规则和imageSize设置的宽高有关，图片的左上角会对准标注点的坐标位置。示例设置规则：x=-imageSize.width/2，y=imageSize.height
    //     imageSize: [100, 100], //图片的尺寸
    //     hoverImageSize: [100, 100], //鼠标悬停时显示的图片尺寸
    //     range: [1, 100000], //可视范围
    //     viewHeightRange: [100, 10000], // 可见高度范围
    //     rangeRatio: 0.01, //可见高度范围的调整系数
    //     imagePath: '@path:screenfile/zhtb/bz.png', //显示图片路径
    //     hoverImagePath: '@path:screenfile/zhtb/cl.png', // 鼠标悬停时显示的图片路径
    //     fixedSize: true, //图片固定尺寸，取值范围：false 自适应，近大远小，true 固定尺寸，默认值：false
    //     text: '北京银行', //显示的文字
    //     useTextAnimation: false, //关闭文字展开动画效果 打开会影响效率
    //     textRange: [1, 1000], //文本可视范围[近裁距离, 远裁距离]
    //     textOffset: [0, 0], // 文本偏移
    //     textBackgroundColor: Color.SpringGreen, //文本背景颜色
    //     fontSize: 24, //字体大小
    //     fontOutlineSize: 1, //字体轮廓线大小
    //     fontColor: Color.White, //字体颜色
    //     fontOutlineColor: Color.Black, //字体轮廓线颜色
    //     showLine: true, //标注点下方是否显示垂直牵引线
    //     lineSize: [2, 100], //垂直牵引线宽度和高度[width, height]
    //     lineColor: Color.SpringGreen, //垂直牵引线颜色
    //     lineOffset: [0, 0], //垂直牵引线偏移
    //     autoHidePopupWindow: true, //失去焦点后是否自动关闭弹出窗口
    //     autoHeight: false, // 自动判断下方是否有物体
    //     displayMode: 4, //智能显示模式  开发过程中请根据业务需求判断使用四种显示模式
    //     clusterByImage: true, // 聚合时是否根据图片路径分类，即当多个marker的imagePath路径参数相同时按路径对marker分类聚合
    //     priority: 0, //避让优先级
    //     occlusionCull: false //是否参与遮挡剔除
    //   }
    //   fdapi.marker.add([o1])
    //   fdapi.marker.focus(o1.id, 100, 0)
    // }
  }
})
const loadLinkGeojson = async () => {
  // 添加前先清除保证id唯一
  //fdapi.geoJSONLayer.clear()

  // 简单渲染器
  const simpleRenderer = {
    // 渲染器类型
    rendererType: RendererType.SimpleRenderer,
    // 默认符号化配置
    defaultSymbol: {
      // 符号化类型枚举：0 simple-marker圆形点填充  1 simple-line线填充  2 simple-fill面填充 3 polygon3d填充
      symbolType: 1,
      // 填充颜色
      color: [1, 0, 0, 1],
      // 默认轮廓线
      outline: {
        // 线宽
        width: 8
      }
    }
  }

  // 用简单渲染器添加GeoJSONLayer
  await fdapi.geoJSONLayer.add({
    id: 'layer2',
    visible: true, // 加载后是否显示
    rotation: [0, 0, 0], // 图层旋转
    offset: [0, 0, 0], // 基于原始位置的偏移量
    needProject: false, // 开启投影转换
    collision: true, // 开启碰撞
    onTerrain: true, // 是否贴地
    url: import.meta.env.VITE_DTS_GW,
    renderer: simpleRenderer
  })
  // fdapi.geoJSONLayer.focus("layer2", 100);
}

const subcatchGeojson = async () => {
  // 添加前先清除保证id唯一
  //fdapi.geoJSONLayer.clear();

  // 简单渲染器
  const simpleRenderer = {
    // 渲染器类型
    rendererType: RendererType.SimpleRenderer,
    // 默认符号化配置
    defaultSymbol: {
      // 符号化类型枚举：0 simple-marker圆形点填充  1 simple-line线填充  2 simple-fill面填充 3 polygon3d填充
      symbolType: 3,
      // 默认高度
      height: 20,
      // 默认填充颜色
      color: [0, 0, 0, 0],
      // 默认轮廓线
      outline: {
        // 线宽
        width: 10,
        // 颜色
        color: [0, 0, 1, 1]
      }
    }
  }

  // 用简单渲染器添加GeoJSONLayer
  fdapi.geoJSONLayer.add({
    id: 'subcatchLayer',
    visible: true, // 加载后是否显示
    rotation: [0, 0, 0], // 图层旋转
    offset: [0, 0, 0], // 基于原始位置的偏移量
    needProject: false, // 开启投影转换
    textMarkerField: 'TextString',
    textRange: [0, 1000000000], // 文字标注可见范围
    onTerrain: true, // 是否贴地
    collision: true, // 开启碰撞
    url: import.meta.env.VITE_DTS_PSFQ, // 这里切换成自己的路径
    renderer: simpleRenderer
  })

  // setTimeout(function () {
  //   fdapi.geoJSONLayer.focus("'subcatchLayer", 100);
  // }, 2000);
}

const api = ref(null)
// 太原数字孪生内网地址
const host = ref(import.meta.env.VITE_DTS_URL)

onMounted(async () => {
  // getDeviceDetails()
  // getVideoData()
  // 获取权责信息数据
  // getQzxxData()

  // 初始化图表
  // setTimeout(() => {
  //   initCharts()
  //   // 图表初始化完成后获取水位数据
  //   setTimeout(() => {
  //     getwaterLevelData()
  //   }, 200)
  // }, 100)

  // 添加键盘事件监听器
  document.addEventListener('keydown', handleKeydown)

  // fdapi.weather.disableRainSnow()

  // getDeviceData('507')
  // getDeviceData('SHB-25825165')
  // dialogVisible.value = true
  // 修改这里，接入地图配置
  try {
    // 确保先引入了ac.min.js
    if (typeof acapi !== 'undefined') {
      // 创建数字孪生平台实例
      console.log('加载飞渡')
      api.value = await new DigitalTwinPlayer(host.value, options.value)

      console.log('数字孪生平台初始化成功')
    } else {
      console.error('ac.min.js未正确加载，请检查引入路径')
    }
  } catch (error) {
    console.error('数字孪生平台初始化失败:', error)
  }
  setTimeout(() => {
    if (mapRef.value) {
      loadLinkGeojson()
      subcatchGeojson()
      console.log('管网加载-排水分区！！！！！！')
    }
  }, 3000)

  // 添加全局点击事件监听器
  // document.addEventListener('click', handleGlobalClick)
})

// 组件卸载时移除事件监听器
onUnmounted(() => {
  fdapi.weather.disableRainSnow()
  fdapi.marker.clear()
  // 移除键盘事件监听器
  document.removeEventListener('keydown', handleKeydown)
  // document.removeEventListener('click', handleGlobalClick)
})

const handleSizeChangeyjwz = size => {
  yjwzPageSize.value = size
  yjwzPageNum.value = 1
  yingjiwuziRootGETTable()
}
const handleCurrentChangeyjwz = current => {
  yjwzPageNum.value = current
  yingjiwuziRootGETTable()
}
</script>
<style lang="scss" scoped>
:deep(.el-tabs__active-bar) {
}
:deep(.el-tabs__nav-wrap::after) {
  background: transparent !important;
}

.car-preview-overlay {
  border: 1px solid #4190d8;
  position: fixed;
  top: 20%;
  left: 0;
  width: 1000px;
  height: 800px;
  pointer-events: auto;
  background: rgba(26, 72, 123, 0.9693);
  display: flex;
  z-index: 10000;
  backdrop-filter: blur(5px);
  margin-left: 40%;
  background: rgba(26, 72, 123, 0.9);
  box-shadow: inset 0px 0px 57px 0px #0e4571;
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #4190d8;
  //opacity: 0.95;
  .car-dialog-header {
    background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
    padding: 10px 20px;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    width: 100.1%;
    align-items: center;
    font-weight: 600;
    background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
    border-radius: 0px 0px 0px 0px;

    .car-dialog-title {
      color: #ffffff;
      font-size: 38px;
      font-weight: bold;
      margin: 0;
      padding-left: 30px;
    }

    .car-dialog-close {
      color: #ffffff;
      font-size: 24px;
      cursor: pointer;
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      transition: all 0.3s ease;
    }
  }

  .car-preview-dialog {
    width: 100%;
    margin-top: 90px;

    .car-info {
      margin-top: 50px;
      position: relative;
      height: 50px;
      line-height: 50px;
      font-size: 40px;
      color: #fff;
      width: 90%;
      margin-left: 5%;
      font-size: 32px;
    }
  }
}

.yuntu-img {
  width: 2140px;
  height: 800px;
  background: url('@/assets/images/intellisensenew/yuntu.png') no-repeat center/100%;
}
.yunzhongkuaibao-img {
  width: 2140px;
  height: 800px;
  background: url('@/assets/images/intellisensenew/yuzhongkuaibao.png') no-repeat center/100%;
}

// :deep(.el-icon svg) {
//   width: 200px !important;
//   height: 200px !important;
// }

// :deep(.el-dialog__headerbtn .el-dialog__close) {
//   color: #ffffff !important;
//   font-size: 60px !important;
// }
.top-tools {
  position: absolute;
  top: 20px;
  left: -9%;
  z-index: 10000;
}

.progress-wrapper {
  position: absolute;
  //bottom: 50px;
  //left: 108%;
  //transform: translateX(-50%);
  //width: 80%;
  z-index: 10000;
}

.floating-progress {
  background-color: rgba(255, 255, 255, 0.8);
  padding: 10px 80px 20px 80px;
  //height: 50px !important;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 覆盖Element Plus默认样式，使进度条更加突出 */
.floating-progress :deep(.el-slider__runway) {
  background-color: rgba(0, 0, 0, 0.1);
}

.floating-progress :deep(.el-slider__bar) {
  background-color: #409eff;
}

.floating-progress :deep(.el-slider__button) {
  border-color: #409eff;
}

.floating-progress :deep(.el-slider__marks-text) {
  color: #606266;
}
.roow-div {
  position: relative;
  height: 100vh;
  // border: 2px solid red;
  .map-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // border: 1px solid yellow;
    z-index: 1; // 添加z-index，确保地图在底层
  }
  .screen-wrap {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // border: 1px solid yellow;
    pointer-events: none; // 这里保持none，让鼠标事件穿透到地图
    z-index: 2; // 添加z-index，确保UI元素在地图上层

    .map-popup-container {
      :deep(.el-dialog__headerbtn .el-dialog__close) {
        color: #ffffff !important;
        font-size: 60px !important;
      }
    }

    /* 确保弹窗和其他需要交互的元素可以接收鼠标事件 */
    .map-popup-container,
    .el-dialog,
    .el-dialog__wrapper,
    .el-dialog__header,
    .el-dialog__headerbtn,
    .el-dialog__close {
      pointer-events: auto !important;
      z-index: 10000;
    }

    .middle-content {
      position: absolute;
      bottom: 250px;
      right: 37%;
      width: 2110px;
      //height: 200px;
      pointer-events: auto;
    }
    /* 导航栏样式 */
    .nav-bar {
      position: absolute;
      top: 13%;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      border-radius: 8px;
      padding: 5px;
      pointer-events: auto; // 保持导航栏可点击
      z-index: 100;

      .nav-item {
        width: vw(200);
        height: vh(80);
        background-image: url('@/assets/images/home/<USER>');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        display: flex;
        justify-content: center;
        padding-top: vh(14);
        font-family: JiangChengXieHei;
        color: #fff;
        font-size: vh(24);
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .nav-item.active {
        background-image: url('@/assets/images/home/<USER>');
        color: #fff;
        font-weight: bold;
      }
    }

    /* 中间顶部背景图样式 */
    .middle-top {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 30%;
      height: vh(220);
      background-image: url('@/assets/images/middle-top.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      padding-top: vh(20);
      z-index: 90;
      .section-title {
        font-family: YouSheBiaoTiHei;
        font-size: vh(80);
        color: #ffffff;
        text-align: center;
        font-style: normal;
      }
    }

    /* 中间底部背景图样式 */
    .middle-bottom {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 35%;
      height: vh(80);
      background-image: url('@/assets/images/middle-bottom.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      z-index: 90;
    }

    /* 子组件容器样式 */
    .component-container {
      position: relative;
      width: 100%;
      height: 100%;
      pointer-events: auto; // 修改为auto，让子组件可以接收鼠标事件
      z-index: 10;

      /* 为了让子组件中需要点击的元素正常工作，而让其他部分能穿透到地图 */
      > * {
        pointer-events: auto; // 确保子组件内的元素可以接收鼠标事件
      }
    }
  }
}

/* 确保弹窗可以正常交互 */
:deep(.custom-dialog) {
  pointer-events: auto !important;
  z-index: 10000 !important;
}

.map-popup-container {
  pointer-events: auto !important;
  z-index: 10000;
  :deep(.el-dialog__headerbtn .el-dialog__close) {
    color: #ffffff !important;
    font-size: 60px !important;
  }
}

/* 弹窗内容样式 */
.popup-content {
  pointer-events: auto;
  padding: 5px;
  font-size: 24px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 30px;
  .popup-item {
    margin-bottom: 8px;

    color: #ffffff;

    .popup-value {
      color: #ffffff;
      font-weight: bold;
    }
  }
}

/* 鼠标点击指示器样式 */
.click-indicator {
  position: fixed;
  width: 30px;
  height: 30px;
  transform: translate(-50%, -50%);
  pointer-events: none; /* 确保指示器不会干扰鼠标事件 */
  z-index: 10000;
}

.click-ripple {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid rgba(255, 0, 0, 0.8);
  animation: ripple 1s ease-out;
}

.click-coords {
  position: absolute;
  top: 30px;
  left: 0;
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

@keyframes ripple {
  0% {
    transform: scale(0.3);
    opacity: 1;
  }
  100% {
    transform: scale(3);
    opacity: 0;
  }
}
</style>

<style lang="scss" scoped>
:deep(.el-dialog) {
  --el-dialog-bg-color: transparent;
  background-image: url('@/assets/images/tcbg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
:deep(.supplies-dialog .el-dialog .el-dialog__header) {
  background: transparent !important;
}
// :deep(.supplies-dialog .el-dialog) {
//   background: none !important;
// }
:deep(.supplies-dialog .el-dialog) {
  background-image: url('@/assets/images/tcbg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  // background: none !important;
  box-shadow: none !important;
}
:deep(.supplies-dialog .el-dialog .el-dialog__body .el-table__header-wrapper tr) {
  background: #1b72df;
}
.table-preview-overlay {
  border: 1px solid #4190d8;
  position: fixed;
  top: 20%;
  left: 0;
  width: 1000px;
  height: 800px;
  pointer-events: auto;
  background: rgba(26, 72, 123, 0.9693);
  display: flex;
  z-index: 10000;
  backdrop-filter: blur(5px);
  margin-left: 44.5%;
  background: rgba(26, 72, 123, 0.9);
  box-shadow: inset 0px 0px 57px 0px #0e4571;
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #4190d8;
  //opacity: 0.95;
  .car-dialog-header {
    background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
    padding: 10px 20px;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    width: 100%;
    align-items: center;
    font-weight: 600;
    background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
    border-radius: 0px 0px 0px 0px;

    .car-dialog-title {
      color: #ffffff;
      font-size: 38px;
      font-weight: bold;
      margin: 0;
      padding-left: 30px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .car-dialog-close {
      color: #ffffff;
      font-size: 24px;
      cursor: pointer;
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      transition: all 0.3s ease;
    }
  }

  .car-preview-dialog {
    width: 100%;
    margin-top: 90px;

    .car-info {
      margin-top: 50px;
      position: relative;
      height: 50px;
      line-height: 50px;
      font-size: 40px;
      color: #fff;
      width: 90%;
      margin-left: 5%;
      font-size: 32px;
    }
  }
}

.yuntu {
  .el-dialog {
    width: 2200px;
    height: 860px;
    background: transparent;
    box-shadow: none;
  }
}

/* 云图iframe弹窗遮罩层 */
.yuntu-iframe-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* 云图iframe弹窗样式 */
.yuntu-iframe-dialog {
  width: 30%;

  background-color: #fff;
  border-radius: 12px;

  position: relative;
  max-height: 90vh;
  overflow: hidden;

  .dialog-header {
    background-color: #fff;

    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .dialog-title {
      color: #000000;
      font-size: 28px;
      font-weight: 600;
      margin: 0;
    }

    .dialog-close {
      color: #000000;
      font-size: 40px;
      font-weight: bold;
      cursor: pointer;
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      transition: all 0.3s ease;
      pointer-events: auto;

      &:hover {
        color: #333333;
        background-color: rgba(0, 0, 0, 0.1);
      }
    }
  }

  .dialog-content {
    padding: 30px;

    iframe {
      width: 100%;
      height: 1000px;
      border: none;
      border-radius: 8px;
      pointer-events: auto; /* 确保iframe可以接收鼠标事件 */
      user-select: auto; /* 允许文本选择 */
    }
  }
}

/* 原有云图弹窗样式 */
.yuntu-dialog {
  .el-dialog {
    background: rgba(6, 72, 146, 0.95);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__close {
      color: #ffffff !important;
      font-size: 20px;

      &:hover {
        color: #409eff !important;
      }
    }
  }
}
.el-slider__bar,
.el-slider__button,
.el-slider,
.el-slider__runway,
.el-slider__button-wrapper,
.el-button {
  pointer-events: auto; /* 确保这不是设置在滑块或其父元素上的 */
}
.progress-wrapper {
  width: 2100px;
}
.el-slider {
  width: 2100px;
  height: 150px;
  padding-left: 80px;
  padding-right: 80px;
}
.el-slider__runway,
.el-slider__bar,
.el-slider__button-wrapper,
el-slider__button {
  height: 40px;
}
.el-slider__stop,
.el-slider__button {
  width: 40px;
  height: 40px;
}
.el-slider__button-wrapper {
  top: 0;
}
.el-slider__marks-text {
  font-size: 32px;
  bottom: -42px;
}

/* 确保弹窗关闭按钮可点击 */
.el-dialog__headerbtn,
.el-dialog__close,
.el-icon {
  pointer-events: auto !important;
  z-index: 10001 !important;
}

.supplies-dialog {
  .el-dialog {
    // background: rgba(26, 72, 123, 0.9693);
    // box-shadow: inset 0px 0px 57px 0px #0e4571;

    border-radius: 0px 0px 0px 0px;
    border: 1px solid #4190d8;
    padding: 0;
    .el-dialog__header {
      height: 50px;
      // background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
      border-radius: 0px 0px 0px 0px;
      padding-left: 30px;
      .el-dialog__title {
        line-height: 50px;
        color: #fff;
        font-size: 24px;
      }
      .el-dialog__close {
        color: #fff;
        font-size: 30px;
      }
    }
    .el-dialog__body {
      padding-left: 50px;
      padding-right: 50px;
      padding-bottom: 80px;
      .el-form-item__label {
        color: #fff;
        font-size: 22px;
      }
      .el-select__wrapper {
        background: transparent;
        // box-shadow: 0 0 0 1px #4190d8 inset;
        .el-select__selected-item {
          color: #fff;
          font-size: 18px;
        }
      }
      .el-input__wrapper {
        background: transparent;
        // box-shadow: 0 0 0 1px #4190d8 inset;
        .el-input__inner {
          color: #fff;
          font-size: 18px;
        }
        .el-input__inner::-webkit-input-placeholder {
          color: #fff;
        }
        .el-input__inner::-moz-placeholder {
          color: #fff;
        }
      }
      .el-button {
        background: rgba(0, 147, 255, 0.2);
        border: 1px solid #1f8ad4;
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 500;
        font-size: 18px;
        color: #ffffff;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .el-table__row {
        background: transparent;
        td {
          background: transparent;
          color: #fff;
          font-size: 22px;
          border-bottom: 1px solid rgba(216, 216, 216, 0.2);
          padding: 16px 0;
        }
      }
      .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
        background-color: rgba(31, 138, 212, 0.4);
      }
      .el-table__header-wrapper {
        tr {
          background: transparent;
          // background: rgba(52, 120, 187, 0.3632);
          border-radius: 2px 2px 2px 2px;
          // border: 1px solid rgba(60, 139, 217, 0.4542);
          th {
            background: transparent;
            font-size: 20px;
            color: #fff;
            border-bottom: none;
          }
        }
      }

      .el-table--fit .el-table__inner-wrapper::before {
        width: 0px;
      }

      .el-pager {
        li {
          background: transparent;
          color: #d8d8d8;
          font-size: 18px;
          &.is-active {
            background: #008aff;
          }
        }
      }
      .el-pagination {
        float: right;
        button {
          background: transparent;
        }
        .btn-prev,
        .btn-next {
          color: #fff;
        }
        .el-pagination__total,
        .el-pagination__jump {
          color: #fff;
          font-size: 18px;
        }
      }
    }
  }
}

/* Device Dialog 自定义弹窗样式 */
.device-dialog-overlay {
  position: fixed;
  top: 40px;
  left: 0;
  width: 100%;
  height: 100%;
  // background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  pointer-events: auto;
}

.device-dialog {
  width: 20%;
  height: 71%;
  // background: #0e4571;
  // border: 1px solid #00a2ec;
  // border-radius: 8px;
  // box-shadow: 0 0 20px rgba(0, 162, 236, 0.3);
  background-image: url('@/assets/images/tcbg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .device-dialog-header {
    // background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
    padding: 20px 20px 0 30px;
    display: flex;
    justify-content: space-between;
    // height: 40px;
    width: 100%;
    align-items: center;

    .device-dialog-title {
      color: #ffffff;
      font-size: 24px;
      font-weight: bold;
      margin: 0;
    }

    .device-dialog-close {
      color: #ffffff;
      font-size: 50px;
      cursor: pointer;
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      transition: all 0.3s ease;
    }
  }

  .device-dialog-body {
    padding: 20px;
    // background: #194779;
    flex: 1;
    overflow-y: auto;
  }
}

.device-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  color: #ffffff;
  // background: #194779;
}

.monitoring-section {
  .monitoring-title {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    color: #ffffff;
    //margin-bottom: 15px;
    width: 277px;
    height: 40px;
    background: url('@/assets/images/home/<USER>') no-repeat center/100%;
    padding-left: 30px;

    // .monitoring-icon {
    //   width: 16px;
    //   height: 16px;
    //   background: #00a2ec;
    //   border-radius: 2px;
    //   margin-right: 8px;
    // }
  }

  .charts-container {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    // margin-bottom: 20px;

    .chart-item {
      width: 50%;
      height: 250px;
      // background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
      // border: 1px solid rgba(0, 162, 236, 0.3);
      // border-radius: 4px;
      padding: 10px;

      .chart-wrapper {
        width: 100%;
        height: 100%;
        background: transparent;
      }
    }
  }

  .data-table-section {
    padding-top: 20px;
    .el-tabs__item {
      color: #fff;
      font-size: 26px;
      padding-bottom: 20px;
    }
    .table-container {
      // background: linear-gradient(135deg, #1e4a72 0%, #2a5298 100%);
      padding: 0;
      max-height: 300px; // 设置表格容器的最大高度
      overflow: hidden;
      border: 1px solid rgba(65, 144, 216, 0.3); // 添加边框以明确滚动区域
      border-radius: 4px;

      .info-table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        background: transparent;
        table-layout: fixed; // 固定表格布局，确保列宽一致

        // 表头样式
        thead {
          display: block;
          width: 100%;
          background: rgba(52, 120, 187, 0.3632);
          border-radius: 2px 2px 2px 2px;
          border: 1px solid rgba(60, 139, 217, 0.4542);

          tr {
            display: table;
            width: 100%;
            table-layout: fixed;

            th {
              display: table-cell;
              color: #ffffff;
              font-size: 24px;
              font-weight: bold;
              text-align: center;
              padding: 8px 15px;
              border-bottom: 1px solid rgba(65, 144, 216, 0.3);
              // 设置每列的固定宽度，确保对齐
              &:nth-child(1) {
                width: 25%;
              } // 泵站列
              &:nth-child(2) {
                width: 25%;
              } // 权责部门列
              &:nth-child(3) {
                width: 25%;
              } // 权责类型列
              &:nth-child(4) {
                width: 25%;
              } // 联系方式列
            }
          }
        }

        // 表体样式
        tbody {
          display: block;
          max-height: 172px; // 减去表头高度
          overflow-y: auto; // 垂直滚动
          overflow-x: hidden; // 隐藏水平滚动

          // 自定义滚动条样式
          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(65, 144, 216, 0.6);
            border-radius: 3px;

            &:hover {
              background: rgba(65, 144, 216, 0.8);
            }
          }

          tr {
            display: table;
            width: 100%;
            table-layout: fixed;
            transition: background-color 0.3s ease;

            td {
              display: table-cell;
              padding: 12px 15px;
              border-bottom: 1px solid rgba(65, 144, 216, 0.3);
              font-size: 24px;
              text-align: center;
              color: #ffffff;
              // 设置文本溢出处理
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              // 确保与表头列宽一致
              &:nth-child(1) {
                width: 25%;
              } // 泵站列
              &:nth-child(2) {
                width: 25%;
              } // 权责部门列
              &:nth-child(3) {
                width: 25%;
              } // 权责类型列
              &:nth-child(4) {
                width: 25%;
              } // 联系方式列

              &.label-cell {
                color: #ffffff;
                font-weight: 500;
                // background: rgba(40, 130, 191, 0.3);
              }

              &.value-cell {
                color: #ffffff;
              }

              .pump-station-cell {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                // 确保内容不会溢出
                overflow: hidden;

                .pump-station-thumbnail {
                  width: 32px;
                  height: 32px;
                  border-radius: 4px;
                  object-fit: cover;
                  flex-shrink: 0; // 防止图片被压缩
                }

                span {
                  color: #ffffff;
                  font-size: 24px;
                  // 文本溢出处理
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  flex: 1;
                }
              }
            }
          }
        }
      }
    }
  }
}

.video-section {
  .video-title {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 15px;
    width: 277px;
    height: 29px;
    background: url('@/assets/images/home/<USER>') no-repeat center/100%;
    padding-left: 30px;

    .video-icon {
      width: 16px;
      height: 16px;
      background: #00a2ec;
      border-radius: 2px;
      margin-right: 8px;
    }
  }

  .video-content {
    width: 100%;
    padding: 15px;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 20px;

    .video-info-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
      min-width: 200px;

      .video-info {
        color: #ffffff;
        font-size: 24px;
        padding: 10px 15px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid transparent;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(0, 162, 236, 0.5);
        }

        &.active {
          background: rgba(0, 162, 236, 0.3);
          border-color: #00a2ec;
          color: #00a2ec;
        }
      }
    }

    .video-player {
      position: relative;
      width: 397px;
      height: 199px;
      background: #000000;
      border-radius: 4px;
      overflow: hidden;
    }

    .video-player-placeholder {
      position: relative;
      width: 397px;
      height: 199px;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 2px dashed rgba(255, 255, 255, 0.3);

      .placeholder-text {
        color: rgba(255, 255, 255, 0.6);
        font-size: 24px;
      }
    }
  }
}

// 图片预览弹窗样式
.image-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: auto;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(5px);

  .image-preview-dialog {
    position: relative;
    max-width: 100%;
    max-height: 100%;
    background: rgba(30, 74, 114, 0.95);
    border-radius: 12px;
    border: 2px solid rgba(65, 144, 216, 0.6);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    animation: imagePreviewFadeIn 0.3s ease-out;

    .image-preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      background: rgba(52, 120, 187, 0.8);
      border-bottom: 1px solid rgba(65, 144, 216, 0.3);

      .image-preview-title {
        color: #ffffff;
        font-size: 24px;
        font-weight: bold;
        font-family: 'Microsoft YaHei', sans-serif;
      }

      .image-preview-close {
        color: #ffffff;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        width: 32px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: scale(1.1);
        }
      }
    }

    .image-preview-content {
      padding: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;

      .preview-image {
        max-width: 100%;
        max-height: 70vh;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        transition: transform 0.3s ease;
      }
    }
  }
}

// 缩略图点击效果
.pump-station-thumbnail {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid rgba(65, 144, 216, 0.5);
}

// 弹窗动画
@keyframes imagePreviewFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
