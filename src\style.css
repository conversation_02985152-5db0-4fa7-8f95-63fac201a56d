/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: auto;
  font-family: "Microsoft YaHei", "JiangChengXieHei", "Alibaba-PuHuiTi", "DIN-Bold", "YouSheBiaoTiHei-2", Arial, sans-serif;
}

#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

/* 原始app-container样式由App.vue控制 */

/* 驾驶舱系统全局样式 */
:root {
  --primary-color: #00ffff;
  --secondary-color: #0095ff;
  --bg-dark: #061537;
  --bg-panel: rgba(6, 30, 71, 0.7);
  --border-color: #0e4b80;
  --scale-ratio: 1;
  /* 屏幕缩放比例变量 */

  /* 全局字体变量 */
  --font-jiangcheng: 'JiangChengXieHei', sans-serif;
  --font-jiangcheng-900: 'JiangChengXieHei-900W', sans-serif;
  --font-alibaba: 'Alibaba-PuHuiTi', sans-serif;
  --font-din: 'DIN-Bold', sans-serif;
  --font-youshe: 'YouSheBiaoTiHei-2', sans-serif;
}

/* 字体类名，方便在组件中直接使用 */
.font-jiangcheng {
  font-family: var(--font-jiangcheng);
}

.font-jiangcheng-900 {
  font-family: var(--font-jiangcheng-900);
}

.font-alibaba {
  font-family: var(--font-alibaba);
}

.font-din {
  font-family: var(--font-din);
}

.font-youshe {
  font-family: var(--font-youshe);
}

button {
  cursor: pointer;
  border: none;
  outline: none;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 149, 255, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 149, 255, 0.7);
}

/* 屏幕适配相关样式 */
html.width-based .scale-width {
  transform: scale(var(--scale-ratio));
  transform-origin: left top;
}

html.height-based .scale-height {
  transform: scale(var(--scale-ratio));
  transform-origin: left top;
}


/* 大屏项目通用样式 */
.full-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 数据可视化组件通用样式 */
.chart-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-title {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.chart-content {
  width: 100%;
  height: calc(100% - 1.5rem);
}

@font-face {
  font-family: 'JiangChengXieHei';
  src: url('./assets/fonts/江城斜黑体700W.ttf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
  /* 优化字体加载体验 */
}

@font-face {
  font-family: 'JiangChengXieHei-900W';
  src: url('./assets/fonts/江城斜黑体900W.ttf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
  /* 优化字体加载体验 */
}

@font-face {
  font-family: ' Alibaba-PuHuiTi';
  src: url('./assets/fonts/Alibaba-PuHuiTi-Heavy.ttf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
  /* 优化字体加载体验 */
}

@font-face {
  font-family: 'DIN-Bold';
  src: url('./assets/fonts/DIN-Bold.ttf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
  /* 优化字体加载体验 */
}

@font-face {
  font-family: 'YouSheBiaoTiHei-2';
  src: url('./assets/fonts/YouSheBiaoTiHei-2.ttf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
  /* 优化字体加载体验 */
}