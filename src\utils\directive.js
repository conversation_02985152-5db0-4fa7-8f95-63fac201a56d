import * as ECharts from 'echarts'
import elementResizeDetectorMaker from 'element-resize-detector'

const HANDLER = '_vue_resize_handler'
function bind(el, binding) {
  el[HANDLER] = binding.value
    ? binding.value
    : () => {
        let chart = ECharts.getInstanceByDom(el)
        if (!chart) {
          return
        }
        chart.resize()
      }
  // 监听绑定的div大小变化，更新 echarts 大小
  elementResizeDetectorMaker().listenTo(el, el[HANDLER])
}
function unbind(el) {
  // 移除监听器
  try {
    elementResizeDetectorMaker().removeListener(el, el[HANDLER])
  } catch (e) {
    console.error('移除图表resize监听器失败:', e)
  }
  delete el[HANDLER]
}

// 创建自定义指令
export const chartResizeDirective = {
  mounted: bind,
  updated: bind, // 添加updated生命周期钩子
  unmounted: unbind
}

// 注册指令的函数，可以在main.js中调用
export function registerChartResizeDirective(app) {
  app.directive('chart-resize', chartResizeDirective)
}
