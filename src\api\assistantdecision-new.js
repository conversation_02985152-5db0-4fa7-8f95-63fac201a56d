import request from '../utils/request'

// 左边
export function getJyjlzl() {
  return request({
    url: '/taiyuan/fzjc-z-jyjlzl/stat',
    method: 'get'
  })
}
export function getDdzltj() {
  return request({
    url: '/taiyuan/fzjc-z-ddzltj/stat',
    method: 'get'
  })
}

export function getPsss() {
  return request({
    url: '/taiyuan/fzjc-z-psssddtj/stat',
    method: 'get'
  })
}

export function getRywz() {
  return request({
    url: '/taiyuan/fzjc-z-rydptj/stat',
    method: 'get'
  })
}

export function getWzdptj() {
  return request({
    url: '/taiyuan/fzjc-z-wzdptj/stat',
    method: 'get'
  })
}

export function getBcddtj() {
  return request({
    url: '/taiyuan/fzjc-z-bcdptj/stat',
    method: 'get'
  })
}

export function getYldbjtb(params = {}) {
  const queryParams = new URLSearchParams()

  // 添加分页参数
  if (params.pageNum) {
    queryParams.append('pageNum', params.pageNum)
  }
  if (params.pageSize) {
    queryParams.append('pageSize', params.pageSize)
  }

  // 添加其他查询参数
  if (params.area) {
    queryParams.append('area', params.area)
  }
  if (params.type) {
    queryParams.append('type', params.type)
  }

  const url = queryParams.toString() ? `/taiyuan/statistics/list?${queryParams.toString()}` : '/taiyuan/statistics/list'

  return request({
    url: url,
    method: 'get'
  })
}

// 右边
// 调度指令统计
export function getDdzrtjRight() {
  return request({
    url: '/taiyuan/fzjc-y-ddzltj/stat',
    method: 'get'
  })
}

// 人员调配统计
export function getRydptjRight() {
  return request({
    url: '/taiyuan/fzjc-y-rydptj/stat',
    method: 'get'
  })
}

// 物资调配统计
export function getWzdptjRight() {
  return request({
    url: '/taiyuan/fzjc-y-wzdptj/stat',
    method: 'get'
  })
}

// 移动泵车调配统计
export function getBcdptjRight() {
  return request({
    url: '/taiyuan/fzjc-y-bcdptj/stat',
    method: 'get'
  })
}

// 排水设施调度统计
export function getPsssddtjRight() {
  return request({
    url: '/taiyuan/fzjc-y-psssddtj/stat',
    method: 'get'
  })
}

export function getJyjlzlRight() {
  return request({
    url: '/taiyuan/fzjc-y-jyjlzl/stat',
    method: 'get'
  })
}

// 降雨日历
// 降雨日历-降雨积涝总览
export function getjyjltjDate(name) {
  return request({
    url: `/taiyuan/fzjc-rl-jyjlzl/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-调度指令统计
export function getddzltjDate(name) {
  return request({
    url: `/taiyuan/fzjc-rl-ddzltj/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-人员调配统计
export function getrydptjDate(name) {
  return request({
    url: `/taiyuan/fzjc-rl-rydptj/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-物资调配统计
export function getwzdptjDate(name) {
  return request({
    url: `/taiyuan/fzjc-rl-wzdptj/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-移动泵车调配统计
export function getydbcdptjDate(name) {
  return request({
    url: `/taiyuan/fzjc-rl-bcdptj/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-排水设施调度统计
export function getpsssddtjDate(name) {
  return request({
    url: `/taiyuan/fzjc-rl-psssddtj/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-降雨积涝总览表格
export function getYldbjtbDate(name, params = {}) {
  const queryParams = new URLSearchParams()
  queryParams.append('forecastTime', name)

  // 添加分页参数
  if (params.pageNum) {
    queryParams.append('pageNum', params.pageNum)
  }
  if (params.pageSize) {
    queryParams.append('pageSize', params.pageSize)
  }

  // 添加其他查询参数
  if (params.area) {
    queryParams.append('area', params.area)
  }
  if (params.type) {
    queryParams.append('type', params.type)
  }

  return request({
    url: `/taiyuan/statistics/list?${queryParams.toString()}`,
    method: 'get'
  })
}

export function getvetData(params = {}) {
  const queryParams = new URLSearchParams()
  // 添加其他查询参数
  if (params.cate) {
    queryParams.append('cate', params.cate)
  }
  if (params.grade) {
    queryParams.append('grade', params.grade)
  }

  return request({
    url: `/taiyuan/statistics/list?${queryParams.toString()}`,
    method: 'get'
  })
}
