<template>
  <div class="screen-container">
    <div class="bg-layer bg-layer-1"></div>
    <div class="bg-layer bg-layer-2"></div>
    <div class="bg-layer bg-layer-3"></div>
    <!-- 地图容器 -->
    <div class="map-container" id="player"></div>

    <!-- 拆分为三个独立的容器 -->
    <!-- 左侧区域容器 -->
    <div class="left-container">
      <div class="left-section">
        <div class="time-weather">
          <div class="time-date">
            <div class="time">{{ currentTime }}</div>
            <div class="date">{{ currentDate }}</div>
          </div>
          <div class="divider"></div>
          <div class="weather">
            <img class="weather-icon" :src="getAssetsFile('sunny.png')" alt="天气" />
            <img class="weather-icon1" :src="getAssetsFile('wendu.png')" alt="温度" />
            <span class="temperature">17℃</span>
          </div>
        </div>

        <!-- 左右布局容器 -->
        <div class="content-layout">
          <!-- 上部内容 -->
          <div class="content-top">
            <!-- 左侧内容 -->
            <div class="content-top-left">
              <!-- 左侧内容分为上中下三个板块 -->
              <div class="left-top-section">
                <!-- 标题栏 -->
                <div class="chart-header">
                  <div class="chart-title">全市平均降雨</div>
                  <div class="chart-unit">雨量（mm）</div>
                </div>
                <!-- 内容区域 -->
                <div class="section-content">
                  <div class="rain-summary-panel">
                    <!-- 主要数据区 -->
                    <div class="rain-main-data">
                      <div class="rain-data-block">
                        <img class="rain-icon" :src="getAssetsFile('l-bg1.png')" />
                        <div class="rain-info">
                          <div class="rain-value">109.9</div>
                          <div class="rain-label">平均雨量</div>
                        </div>
                      </div>
                      <div class="rain-data-block">
                        <img class="rain-icon" :src="getAssetsFile('l-bg2.png')" />
                        <div class="rain-info">
                          <div class="rain-value">147.3</div>
                          <div class="rain-label">当前降雨量</div>
                        </div>
                      </div>
                    </div>
                    <!-- 预测区 -->
                    <div class="rain-forecast-list">
                      <div class="rain-forecast-item">
                        <div class="forecast-time blue">24h</div>
                        <div class="forecast-info">
                          <div class="forecast-value">49.6</div>
                          <div class="forecast-label">24h累计</div>
                        </div>
                      </div>
                      <div class="rain-forecast-item">
                        <div class="forecast-time yellow">1h</div>
                        <div class="forecast-info">
                          <div class="forecast-value">49.6</div>
                          <div class="forecast-label">未来1h</div>
                        </div>
                      </div>
                      <div class="rain-forecast-item">
                        <div class="forecast-time dblue">2h</div>
                        <div class="forecast-info">
                          <div class="forecast-value">49.6</div>
                          <div class="forecast-label">未来2h</div>
                        </div>
                      </div>
                      <div class="rain-forecast-item">
                        <div class="forecast-time green">4h</div>
                        <div class="forecast-info">
                          <div class="forecast-value">49.6</div>
                          <div class="forecast-label">未来4h</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="left-middle-section">
                <!-- 标题栏 -->
                <div class="chart-header">
                  <div class="chart-title">水情形势分析</div>
                  <div class="chart-unit">雨量（mm）</div>
                </div>
                <!-- 内容区域 -->
                <div class="section-content">
                  <div class="rainfall-bar-chart" ref="rainfallBarChart"></div>
                </div>
              </div>
              <div class="left-bottom-section">
                <!-- 标题栏 -->
                <div class="chart-header">
                  <div class="chart-title">水情形势分析</div>
                </div>
                <!-- 内容区域 -->
                <div class="section-content">
                  <div class="bottom-bar-chart" ref="bottomBarChart"></div>
                </div>
              </div>
            </div>
            <!-- 右侧内容 -->
            <div class="content-top-right">
              <!-- 右侧内容分为上下两个板块 -->
              <div class="right-top-section">
                <!-- 标题栏 -->
                <div class="alarm-header">
                  <div class="alarm-title">实时告警</div>
                  <div class="alarm-tabs">
                    <div
                      v-for="(tab, index) in alarmTabs"
                      :key="index"
                      class="alarm-tab"
                      :class="{ active: currentAlarmTab === tab.value }"
                      @click="changeAlarmTab(tab.value)"
                    >
                      {{ tab.name }}
                    </div>
                  </div>
                </div>
                <!-- 内容区域 -->
                <div class="section-content">
                  <div class="water-stats-container">
                    <!-- 积水点总数 -->
                    <div class="total-points-card">
                      <div class="card-title">积水点总数</div>
                      <div class="total-value">{{ totalPoints }}</div>
                    </div>
                    <!-- 分级统计卡片 -->
                    <div class="level-stats-grid">
                      <div
                        class="level-card"
                        v-for="(item, index) in levelStats"
                        :key="index"
                        :style="{
                          backgroundImage: `url(${getAssetsFile(item.bgImage)})`
                        }"
                      >
                        <div class="level-title">{{ item.title }}</div>
                        <div class="level-value" :class="`level-${item.level}`">
                          {{ item.value }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="right-bottom-section">
                <!-- 内容区域 -->
                <div class="section-content">
                  <!-- 实时告警表格 -->
                  <div class="alarm-table-container">
                    <!-- 表头标签切换 -->
                    <div class="alarm-table-tabs">
                      <div
                        v-for="(tab, index) in alarmTableTabs"
                        :key="index"
                        class="alarm-table-tab"
                        :class="{ active: currentAlarmTableTab === tab.value }"
                        @click="changeAlarmTableTab(tab.value)"
                      >
                        {{ tab.name }}
                      </div>
                    </div>
                    <!-- 表格头部 -->
                    <div class="alarm-table-header">
                      <div class="alarm-th th-index">序号</div>
                      <div class="alarm-th th-risk">风险名称</div>
                      <div class="alarm-th th-time">预警时间</div>
                      <div class="alarm-th th-water">水位(m)</div>
                      <div class="alarm-th th-level">等级</div>
                      <div class="alarm-th th-status">是否应急</div>
                      <div class="alarm-th th-respond">响应时间</div>
                      <div class="alarm-th th-area">区域</div>
                    </div>
                    <!-- 表格内容 -->
                    <div class="alarm-table-body">
                      <div class="alarm-tr" v-for="(row, idx) in alarmTableData" :key="row.id">
                        <div class="alarm-td th-index">{{ row.index }}</div>
                        <div class="alarm-td th-risk">{{ row.risk }}</div>
                        <div class="alarm-td th-time">{{ row.time }}</div>
                        <div class="alarm-td th-water">{{ row.water }}</div>
                        <div class="alarm-td th-level">
                          <span :class="['level-badge', row.levelColor]">{{ row.level }}</span>
                        </div>
                        <div class="alarm-td th-status">{{ row.status }}</div>
                        <div class="alarm-td th-respond">
                          {{ row.respondTime }}
                        </div>
                        <div class="alarm-td th-area">{{ row.area }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 下部内容 -->
          <div class="content-bottom">
            <!-- 下部内容将在这里添加 -->
            <div class="chart-header">
              <div class="chart-title">水情形式分析</div>
            </div>
            <!-- 内容区域 -->
            <div class="section-content">
              <div class="rain-monitor-table-container">
                <div class="rain-monitor-table-header">
                  <div class="rain-th th-index1">序号</div>
                  <div class="rain-th th-station1">河道站</div>
                  <div class="rain-th th-hour1">小时时降雨(mm)</div>
                  <div class="rain-th th-warning1">警戒</div>
                  <div class="rain-th th-over1">超警值</div>
                  <div class="rain-th th-level1">预警级别</div>
                  <div class="rain-th th-time1">首次发警时间</div>
                  <div class="rain-th th-f11">未来1小时雨量(mm)</div>
                  <div class="rain-th th-f21">未来2小时雨量(mm)</div>
                  <div class="rain-th th-f41">未来4小时雨量(mm)</div>
                </div>
                <div class="rain-monitor-table-body">
                  <div class="rain-tr" v-for="(row, idx) in rainMonitorTableData" :key="row.index">
                    <div class="rain-td th-index1">{{ row.index }}</div>
                    <div class="rain-td th-station1">{{ row.station }}</div>
                    <div class="rain-td th-hour1">{{ row.hourRain }}</div>
                    <div class="rain-td th-warning1">{{ row.warning }}</div>
                    <div class="rain-td th-over1">
                      <span
                        :class="{
                          'over-red': row.over > 0,
                          'over-blue': row.over < 0
                        }"
                        >{{ row.over > 0 ? '+' + row.over : row.over }}</span
                      >
                    </div>
                    <div class="rain-td th-level1">
                      <span :class="['level-badge', row.levelColor]">{{ row.level }}</span>
                    </div>
                    <div class="rain-td th-time1">{{ row.firstTime }}</div>
                    <div class="rain-td th-f11">{{ row.future1h }}</div>
                    <div class="rain-td th-f21">{{ row.future2h }}</div>
                    <div class="rain-td th-f41">{{ row.future4h }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间区域容器 -->
    <div class="middle-container">
      <div class="middle-section">
        <!-- 标题 -->
        <div class="section-title">内涝安全预警监测综合驾驶舱系统</div>
        <!-- 导航按钮 -->
        <div class="nav-buttons">
          <div
            v-for="(btn, index) in navButtons"
            :key="index"
            :class="['nav-button', { active: activeNavButton === index }]"
            @click="activeNavButton = index"
          >
            <div class="nav-button-text">{{ btn.text }}</div>
          </div>
        </div>
        <!-- 中间内容 -->
        <div></div>
      </div>
    </div>

    <!-- 右侧区域容器 -->
    <div class="right-container">
      <div class="right-section">
        <!-- 用户信息和返回门户 -->
        <div class="user-portal-container">
          <div class="user-info">
            <img class="user-icon" src="https://picsum.photos/200/300" alt="用户" />
            <span class="username">管理员</span>
          </div>
          <div class="portal-back">
            <img class="portal-icon" src="https://picsum.photos/200/300" alt="门户" />
            <span class="portal-text">返回门户</span>
          </div>
        </div>
        <!-- 右侧其他内容 -->
        <div class="right-section-bottom">
          <!-- 左侧模块 -->
          <div class="right-bottom-module left-module">
            <!-- 左侧模块上部分 -->
            <div class="left-module-top">
              <div class="chart-header">
                <div class="chart-title">监控方案</div>
              </div>
              <div class="module-content">
                <!-- 方案选择区域 -->
                <div class="plan-selection-area">
                  <div class="plan-selection-label">选择方案</div>
                  <div class="plan-selection-dropdown">
                    <span class="selected-plan">典型场景-30mm降雨</span>
                    <span class="dropdown-arrow">▼</span>
                  </div>
                </div>

                <!-- 方案描述信息区域 -->
                <div class="plan-description-area">
                  <div class="description-item">
                    <span class="description-label descript">方案描述</span>
                  </div>
                  <div class="description-item">
                    <span class="description-label">预测起报起始时间：</span>
                    <span class="description-value">2023-07-20 08:30</span>
                  </div>
                  <div class="description-item">
                    <span class="description-label">预测时长:</span>
                    <span class="description-value">24小时</span>
                  </div>
                  <div class="description-item">
                    <span class="description-label">场景说明:</span>
                    <span class="description-value">xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</span>
                  </div>
                </div>

                <!-- 主要数据展示卡片区域 -->
                <div class="water-data-cards">
                  <div class="water-data-card" v-for="(card, index) in waterDataCards" :key="index">
                    <div
                      class="card-content"
                      :style="{
                        backgroundImage: `url(${getAssetsFile(card.bgImage)})`
                      }"
                    >
                      <div class="card-label">{{ card.label }}</div>
                      <div class="card-value">
                        {{ card.value }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 左侧模块下部分 -->
            <div class="left-module-bottom">
              <div class="chart-header">
                <div class="chart-title">积水数据监测</div>
              </div>
              <div class="module-content">
                <!-- 顶部统计信息卡片 -->
                <div class="stats-cards">
                  <div v-for="(card, index) in statsCards" :key="index" class="stats-card">
                    <div class="stats-icon">
                      <img :src="getAssetsFile(card.icon)" :alt="card.title" />
                    </div>
                    <div class="stats-info">
                      <div class="stats-title">{{ card.title }}</div>
                      <div class="stats-value">
                        {{ card.value }}<span class="stats-unit">{{ card.unit }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 数据表格区 -->
                <div class="water-monitoring-table">
                  <table>
                    <thead>
                      <tr>
                        <th>名称</th>
                        <th>类型</th>
                        <th>积水面积</th>
                        <th>最大范围</th>
                        <th>预警时间</th>
                        <th>等级</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(item, index) in waterMonitoringData" :key="index">
                        <td>{{ item.name }}</td>
                        <td>{{ item.type }}</td>
                        <td>{{ item.area }}</td>
                        <td>{{ item.maxRange }}</td>
                        <td>{{ item.alertTime }}</td>
                        <td>
                          <span :class="['level-badge', `level-${item.levelClass}`]">{{ item.level }}</span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <!-- 折线图区域 -->
                <div class="water-level-chart">
                  <div class="chart-info">
                    <div class="chart-label">水位(m)</div>
                  </div>
                  <div class="chart-container" ref="waterLevelChart"></div>
                </div>
              </div>
            </div>
          </div>
          <!-- 右侧模块 -->
          <div class="right-bottom-module right-module">
            <div class="chart-header">
              <div class="chart-title">按类型分类统计</div>
            </div>
            <div class="module-content">
              <!-- 右侧模块内容 -->
              <div class="district-stats-container">
                <!-- 头部信息展示区域 -->
                <div class="district-stats-header">
                  <div
                    v-for="(item, index) in districtHeaderData"
                    :key="index"
                    class="district-stats-card"
                    :style="{
                      backgroundImage: `url(${getAssetsFile(item.bgImage)})`
                    }"
                  >
                    <div class="stats-label" :class="`stats-label-${index + 1}`">
                      {{ item.label }}
                    </div>
                    <div class="card-info">
                      <div class="info-row">
                        <div v-for="(subItem, subIndex) in item.subItems" :key="subIndex" class="info-column">
                          <div class="info-value">{{ subItem.value }}</div>
                          <div class="info-label">{{ subItem.label }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 主表格区域 -->
                <div class="main-table-wrapper">
                  <table class="district-data-table">
                    <thead>
                      <tr>
                        <th>编号</th>
                        <th>类型</th>
                        <th>距离道路(km)</th>
                        <th>影响建筑(m²)</th>
                        <th>影响人口(人)</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(item, index) in districtTableData" :key="index">
                        <td>{{ item.id }}</td>
                        <td>{{ item.name }}</td>
                        <td>{{ item.distance }}</td>
                        <td>{{ item.area }}</td>
                        <td>{{ item.population }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <!-- 设备统计区域 -->
                <div class="equipment-stats">
                  <div class="equipment-item" v-for="(item, index) in equipmentStats" :key="index">
                    <div class="equipment-icon">
                      <img :src="getAssetsFile(item.icon)" :alt="item.label" />
                    </div>
                    <div class="equipment-info">
                      <div class="equipment-value">{{ item.value }}</div>
                      <div class="equipment-label">{{ item.label }}</div>
                    </div>
                  </div>
                </div>

                <!-- 水泵表格 -->
                <div class="pump-table-wrapper">
                  <table class="pump-data-table">
                    <thead>
                      <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>数量(台)</th>
                        <th>流量(m³/s)</th>
                        <th>联系人/电话</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(item, index) in pumpTableData" :key="index">
                        <td>{{ item.id }}</td>
                        <td>{{ item.name }}</td>
                        <td>{{ item.count }}</td>
                        <td>{{ item.flow }}</td>
                        <td>{{ item.contact }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <!-- 底部人员配置区域 -->
                <div class="personnel-stats">
                  <div
                    class="personnel-item"
                    v-for="(item, index) in personnelStats"
                    :key="index"
                    :style="{
                      backgroundImage: `url(${getAssetsFile(item.icon)})`
                    }"
                  >
                    <div class="personnel-info">
                      <div class="personnel-title">{{ item.title }}</div>
                      <div class="personnel-data">
                        <div class="personnel-count">{{ item.teams }}<span>队</span></div>
                        <div class="personnel-count">{{ item.people }}<span>人</span></div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 消防队表格 -->
                <div class="firefighter-table-wrapper">
                  <table class="firefighter-data-table">
                    <thead>
                      <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>联系人</th>
                        <th>人数(人)</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(item, index) in firefighterTableData" :key="index">
                        <td>{{ item.id }}</td>
                        <td>{{ item.name }}</td>
                        <td>{{ item.contact }}</td>
                        <td>{{ item.count }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
// 引入数字孪生js
// 引入数字孪生js
import * as acapi from '../../assets/ac.min.js'
// 导航按钮
const navButtons = [{ text: '综合展示' }, { text: '智能感知' }, { text: '报警决策' }, { text: '辅助决策' }]
const activeNavButton = ref(0)
// 获取assets静态资源
const getAssetsFile = url => {
  return new URL(`../../assets/images/alarmplan/${url}`, import.meta.url).href
}

// 时间和日期
const currentTime = ref('')
const currentDate = ref('')

// 积水点级别数据
const levelStats = ref([
  { title: '60cm以上', value: 16, level: 'high', bgImage: 'l-bg4.png' },
  { title: '40-60cm', value: 21, level: 'medium', bgImage: 'l-bg5.png' },
  { title: '30-40cm', value: 16, level: 'low', bgImage: 'l-bg6.png' },
  { title: '15-30cm', value: 16, level: 'blue1', bgImage: 'l-bg7.png' },
  { title: '15cm以下', value: 21, level: 'green', bgImage: 'l-bg8.png' },
  { title: '10cm以下', value: 21, level: 'purple', bgImage: 'l-bg9.png' }
])

// 积水点总数
const totalPoints = ref(168)

// echarts图表相关
const rainfallChart = ref(null)
const rainfallBarChart = ref(null)
const bottomBarChart = ref(null)
const waterLevelChart = ref(null)
let chartInstance = null
let barChartInstance = null
let bottomBarChartInstance = null
let timer = null
let statsUpdateTimer = null

// 标签页相关
const tabs = [
  { name: '实时', value: 'realtime' },
  { name: '3日', value: '3days' },
  { name: '7日', value: '7days' }
]
const currentTab = ref('realtime')

// 告警标签页相关
const alarmTabs = [
  { name: '雨情', value: 'rain' },
  { name: '水情', value: 'water' },
  { name: '内涝', value: 'flood' }
]
const currentAlarmTab = ref('flood')

// 告警表格标签页相关
const alarmTableTabs = [
  { name: '全部', value: 'all' },
  { name: '红色', value: 'red' },
  { name: '橙色', value: 'orange' },
  { name: '黄色', value: 'yellow' },
  { name: '已启用', value: 'enabled' },
  { name: '未启用', value: 'disabled' }
]
const currentAlarmTableTab = ref('all')

// 切换标签页
const changeTab = tab => {
  currentTab.value = tab
  // 可以在这里根据选中的标签页更新图表数据
  initRainfallChart()
}

// 切换告警标签页
const changeAlarmTab = tab => {
  currentAlarmTab.value = tab
  // 可以在这里根据选中的告警标签页更新数据
  // 例如更新积水点数据或其他相关数据
}

// 切换告警表格标签页
const changeAlarmTableTab = tab => {
  currentAlarmTableTab.value = tab
  // 可以在这里根据选中的表格标签页过滤表格数据
  // 例如根据等级或状态过滤告警数据
}

// 更新时间函数
const updateTime = () => {
  const now = new Date()

  // 格式化时分秒
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  const seconds = now.getSeconds().toString().padStart(2, '0')
  currentTime.value = `${hours}:${minutes}:${seconds}`

  // 格式化年月日
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  currentDate.value = `${year} ${month} ${day}`
}

// 初始化雨水情况分析图表
const initRainfallChart = () => {
  if (!rainfallChart.value) return

  // 销毁已存在的图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建新的图表实例
  chartInstance = echarts.init(rainfallChart.value)
  let yAxisData = ['小店区', '迎泽区', '杏花岭', '尖草坪', '万柏林', '晋源区']
  let data1 = [0.8, 1.0, 0.9, 0.6, 0.8, 0.5]
  let data2 = [0.8, 1.0, 0.9, 0.6, 0.8, 0.5]
  const option = {
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      show: true,
      bottom: '8%',
      right: '3%',
      itemGap: 15,
      itemWidth: 30,
      itemHeight: 6,
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      data: [
        {
          name: '历史',
          icon: 'rect'
        },
        {
          name: '预报',
          icon: 'rect'
        }
      ]
    },
    title: {
      text: '单位(mm)',
      left: '3%',
      bottom: '8%',
      textStyle: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 'normal'
      }
    },
    grid: [
      {
        show: false,
        left: '20%',
        top: '0',
        bottom: '15%',
        containLabel: true,
        width: '35%'
      },
      {
        show: false,
        left: '5%',
        bottom: '29%',
        top: '0',
        width: '0%'
      },
      {
        show: false,
        left: '55%',
        top: '0',
        bottom: '15%',
        containLabel: true,
        width: '30%'
      }
    ],
    xAxis: [
      {
        type: 'value',
        inverse: true,
        axisLabel: {
          show: true,
          color: '#fff',
          margin: 10
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#30414F',
            type: 'dashed',
            width: 1,
            opacity: 0.3
          }
        }
      },
      {
        gridIndex: 1,
        show: true,
        axisLabel: {
          color: '#fff',
          margin: 0
        },
        splitLine: {
          lineStyle: {
            color: '#fff',
            type: 'dashed'
          }
        }
      },
      {
        gridIndex: 2,
        type: 'value',
        axisLabel: {
          show: true,
          color: '#fff',
          margin: 10
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#30414F',
            type: 'dashed',
            width: 1,
            opacity: 0.3
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'category',
        inverse: false,
        position: 'right',
        axisLabel: {
          show: false
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#fff',
            width: 5
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        data: yAxisData
      },
      {
        type: 'category',
        inverse: false,
        gridIndex: 1,
        position: 'left',
        axisLabel: {
          align: 'left',
          padding: [8, 0, 0, 0],
          fontSize: 12,
          fontWeight: 500,
          color: `#fff`
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#fff'
          }
        },
        axisTick: {
          show: false
        },
        data: yAxisData
      },
      {
        type: 'category',
        inverse: false,
        gridIndex: 2,
        position: 'left',
        axisLabel: {
          show: false
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#fff'
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        data: yAxisData
      }
    ],
    series: [
      // 左侧历史数据柱
      {
        type: 'bar',
        barWidth: 8,
        name: '历史',
        xAxisIndex: 0,
        yAxisIndex: 0,
        data: data1,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#3EDFB7' },
              { offset: 1, color: '#89E5CF' }
            ],
            globalCoord: false
          }
        },
        backgroundStyle: {
          color: 'rgba(48, 65, 79, 0.3)' // 透明背景色
        },
        // showBackground: true, // 开启背景
        z: 2
      },
      // 左侧背景柱
      {
        type: 'bar',
        barWidth: 16,
        barGap: '-140%',
        xAxisIndex: 0,
        yAxisIndex: 0,
        name: '背景',
        silent: true,
        tooltip: {
          show: false
        },
        label: {
          normal: {
            show: false
          }
        },
        itemStyle: {
          normal: {
            color: 'rgba(48, 65, 79, 0.3)'
          }
        },
        z: 0,
        data: [1, 1, 1, 1, 1, 1]
      },
      // 右侧预报数据柱
      {
        type: 'bar',
        barWidth: 8,
        name: '预报',
        xAxisIndex: 2,
        yAxisIndex: 2,
        data: data2,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#9FC9F3' },
              { offset: 1, color: '#4899EA' }
            ],
            globalCoord: false
          }
        },
        backgroundStyle: {
          color: 'rgba(48, 65, 79, 0.3)', // 透明背景色
          borderRadius: 8
        },
        showBackground: true, // 开启背景
        z: 2
      },
      // 右侧背景柱
      {
        type: 'bar',
        barWidth: 16,
        barGap: '-140%',
        name: '背景',
        silent: true,
        xAxisIndex: 2,
        yAxisIndex: 2,
        tooltip: {
          show: false
        },
        label: {
          normal: {
            show: false
          }
        },
        itemStyle: {
          normal: {
            color: 'rgba(48, 65, 79, 0.3)'
          }
        },
        z: 1,
        data: [1, 1, 1, 1, 1, 1]
      },
      // 添加中间的竖线
      {
        name: '中线',
        type: 'line',
        symbol: 'none',
        silent: true,
        tooltip: {
          show: false
        },
        position: ['10%', '10%'],
        data: [
          [0, -1],
          [0, 10]
        ],
        lineStyle: {
          color: '#30414F',
          width: 3
        },
        z: 0
      }
    ]
  }

  // 设置图表配置并渲染
  chartInstance.setOption(option)

  // 确保图表铺满容器
  chartInstance.resize()

  // 添加窗口大小变化监听器
  window.addEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
    if (barChartInstance) {
      barChartInstance.resize()
    }
  })
}

// 初始化柱状图
const initBarChart = () => {
  if (!rainfallBarChart.value) return

  // 销毁已存在的图表实例
  if (barChartInstance) {
    barChartInstance.dispose()
  }

  // 创建新的图表实例
  barChartInstance = echarts.init(rainfallBarChart.value)

  // 准备数据
  const xAxisData = ['08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00']
  const seriesData = [25, 10, 10, 50, 25, 50, 10, 50]

  const option = {
    grid: {
      top: '12%',
      left: '3%',
      right: '3%',
      bottom: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#fff',
          opacity: 0.5
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 22,
        margin: 15
      }
    },
    yAxis: {
      type: 'value',
      max: 50,
      interval: 10,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 22
      },
      splitLine: {
        lineStyle: {
          color: '#30414F',
          opacity: 0.3
        }
      }
    },
    series: [
      // 数据柱
      {
        data: seriesData,
        type: 'bar',
        barWidth: 35,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#3B73B8'
              },
              {
                offset: 1,
                color: '#3B73B8'
              }
            ]
          }
        },
        z: 2
      },
      // 背景柱
      {
        data: [50, 50, 50, 50, 50, 50, 50, 50],
        type: 'bar',
        barWidth: 35,
        barGap: '-100%',
        itemStyle: {
          color: 'rgba(255,255,255, 0.2)'
        },
        silent: true,
        tooltip: {
          show: false
        },
        z: 1
      }
    ]
  }

  // 设置图表配置并渲染
  barChartInstance.setOption(option)

  // 确保图表铺满容器
  barChartInstance.resize()
}

// 初始化底部横向条形图
const initBottomBarChart = () => {
  if (!bottomBarChart.value) return
  if (bottomBarChartInstance) {
    bottomBarChartInstance.dispose()
  }
  bottomBarChartInstance = echarts.init(bottomBarChart.value)
  const yAxisData = ['站点1', '站点2', '站点3', '站点4', '站点2']
  const seriesData = [120, 150, 190, 90, 130]
  const option = {
    grid: {
      left: '10%',
      right: '0%',
      top: 10,
      bottom: 40,
      containLabel: false
    },
    xAxis: {
      type: 'value',
      min: 0,
      max: 250,
      interval: 25,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 22,
        margin: 10
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: '#30414F',
          type: 'solid',
          width: 1,
          opacity: 1
        }
      }
    },
    yAxis: {
      type: 'category',
      data: yAxisData,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 22,
        align: 'left',
        margin: 80,
        padding: [0, 0, 0, 0]
      }
    },
    series: [
      {
        type: 'bar',
        data: seriesData,
        barWidth: 16,
        itemStyle: {
          color: 'rgba(0, 221, 255, 0.8)',
          borderRadius: 6
        },
        label: {
          show: false
        },
        z: 2
      },
      // 背景条
      {
        type: 'bar',
        data: [250, 250, 250, 250, 250],
        barWidth: 16,
        barGap: '-100%',
        itemStyle: {
          color: 'rgba(255, 255, 255, 0.2)'
        },
        silent: true,
        tooltip: {
          show: false
        },
        z: 1
      }
    ]
  }
  bottomBarChartInstance.setOption(option)
  bottomBarChartInstance.resize()
}

// 实时告警表格数据
const alarmTableData = ref([
  {
    id: 1,
    index: '01',
    risk: '低洼路段',
    time: '08-01 12:10',
    water: '10.2',
    level: 'Ⅱ',
    levelColor: 'level-yellow',
    status: '已启动',
    respondTime: '08-01 13:10',
    area: '小店区'
  },
  {
    id: 2,
    index: '02',
    risk: '车辆下穿',
    time: '08-01 12:10',
    water: '10.2',
    level: 'Ⅱ',
    levelColor: 'level-yellow',
    status: '已启动',
    respondTime: '08-01 13:10',
    area: '小店区'
  },
  {
    id: 3,
    index: '03',
    risk: '泵站',
    time: '08-01 12:10',
    water: '10.2',
    level: 'Ⅱ',
    levelColor: 'level-yellow',
    status: '已启动',
    respondTime: '08-01 13:10',
    area: '长治路区'
  },
  {
    id: 4,
    index: '04',
    risk: '低洼路段',
    time: '08-01 12:10',
    water: '10.2',
    level: 'Ⅳ',
    levelColor: 'level-blue',
    status: '已启动',
    respondTime: '08-01 13:10',
    area: '长治路区'
  },
  {
    id: 5,
    index: '05',
    risk: '低洼路段',
    time: '08-01 12:10',
    water: '10.2',
    level: 'Ⅰ',
    levelColor: 'level-red',
    status: '已启动',
    respondTime: '08-01 13:10',
    area: '小店区'
  },
  {
    id: 6,
    index: '06',
    risk: '车辆下穿',
    time: '08-01 13:10',
    water: '10.2',
    level: 'Ⅱ',
    levelColor: 'level-yellow',
    status: '已启动',
    respondTime: '08-01 13:10',
    area: '小店区'
  },
  {
    id: 7,
    index: '07',
    risk: '泵站',
    time: '08-01 13:10',
    water: '10.2',
    level: 'Ⅱ',
    levelColor: 'level-yellow',
    status: '已启动',
    respondTime: '08-01 13:10',
    area: '小店区'
  },
  {
    id: 8,
    index: '08',
    risk: '低洼路段',
    time: '08-01 13:10',
    water: '10.2',
    level: 'Ⅱ',
    levelColor: 'level-yellow',
    status: '已启动',
    respondTime: '08-01 13:10',
    area: '小店区'
  },
  {
    id: 9,
    index: '09',
    risk: '低洼路段',
    time: '08-01 13:10',
    water: '10.2',
    level: 'Ⅱ',
    levelColor: 'level-yellow',
    status: '已启动',
    respondTime: '08-01 13:10',
    area: '小店区'
  },
  {
    id: 10,
    index: '10',
    risk: '车辆下穿',
    time: '08-01 13:10',
    water: '10.2',
    level: 'Ⅱ',
    levelColor: 'level-yellow',
    status: '已启动',
    respondTime: '08-01 13:10',
    area: '小店区'
  },
  {
    id: 10,
    index: '10',
    risk: '车辆下穿',
    time: '08-01 13:10',
    water: '10.2',
    level: 'Ⅱ',
    levelColor: 'level-yellow',
    status: '已启动',
    respondTime: '08-01 13:10',
    area: '小店区'
  },
  {
    id: 10,
    index: '10',
    risk: '车辆下穿',
    time: '08-01 13:10',
    water: '10.2',
    level: 'Ⅱ',
    levelColor: 'level-yellow',
    status: '已启动',
    respondTime: '08-01 13:10',
    area: '小店区'
  }
])

// 小时降雨监测表静态数据
const rainMonitorTableData = [
  {
    index: '01',
    station: '万柏林区',
    hourRain: 44.6,
    warning: 10,
    over: 34.6,
    level: '红色',
    levelColor: 'level-red',
    firstTime: '08/01 16:00',
    future1h: 44.6,
    future2h: 44.6,
    future4h: 44.6
  },
  {
    index: '02',
    station: '杏花岭区',
    hourRain: 30.2,
    warning: 35,
    over: -4.8,
    level: '蓝色预警',
    levelColor: 'level-blue',
    firstTime: '08/01 16:00',
    future1h: 12.6,
    future2h: 12.6,
    future4h: 12.6
  },
  {
    index: '03',
    station: '小店区',
    hourRain: 36.5,
    warning: 10,
    over: 26.5,
    level: '轻度',
    levelColor: 'level-light',
    firstTime: '08/01 16:00',
    future1h: 22.7,
    future2h: 22.7,
    future4h: 22.7
  },
  {
    index: '04',
    station: '小店区',
    hourRain: 36.5,
    warning: 10,
    over: 26.5,
    level: '轻度',
    levelColor: 'level-light',
    firstTime: '08/01 16:00',
    future1h: 18.2,
    future2h: 18.2,
    future4h: 18.2
  },
  {
    index: '05',
    station: '小店区',
    hourRain: 36.5,
    warning: 10,
    over: 26.5,
    level: '轻度',
    levelColor: 'level-light',
    firstTime: '08/01 16:00',
    future1h: 44.6,
    future2h: 44.6,
    future4h: 44.6
  }
]

// 水情数据卡片
const waterDataCards = ref([
  {
    label: '最大积水面积',
    value: '89.11',
    bgImage: 'r-bg1.png'
  },
  {
    label: '最大积水水深(m)',
    value: '0.89',
    bgImage: 'r-bg2.png'
  },
  {
    label: '最大积水水量(ml)',
    value: '7.51',
    bgImage: 'r-bg3.png'
  }
])

// 统计信息卡片数据
const statsCards = ref([
  {
    title: '最大淹没面积',
    value: '1.87',
    unit: '万km²',
    icon: 'r-bg4.png'
  },
  {
    title: '最大水深',
    value: '0.7',
    unit: 'm',
    icon: 'r-bg5.png'
  },
  {
    title: '受影响村庄',
    value: '6',
    unit: '个',
    icon: 'r-bg6.png'
  },
  {
    title: '影响人数',
    value: '0.6',
    unit: '万人',
    icon: 'r-bg7.png'
  }
])

// 水位监测数据
const waterMonitoringData = ref([
  {
    name: '区域1',
    type: '海河流域',
    area: '0.1',
    maxRange: '38.9',
    alertTime: '2024-10-28 8:00',
    level: 'I',
    levelClass: 'red'
  },
  {
    name: '区域2',
    type: '海河流域',
    area: '0.1',
    maxRange: '38.9',
    alertTime: '2024-10-28 8:00',
    level: 'II',
    levelClass: 'orange'
  },
  {
    name: '区域3',
    type: '海河流域',
    area: '0.1',
    maxRange: '38.9',
    alertTime: '2024-10-28 8:00',
    level: 'III',
    levelClass: 'yellow'
  },
  {
    name: '区域4',
    type: '海河流域',
    area: '0.2',
    maxRange: '38.9',
    alertTime: '2024-10-28 8:00',
    level: 'IV',
    levelClass: 'blue'
  },
  {
    name: '区域5',
    type: '海河流域',
    area: '0.2',
    maxRange: '38.9',
    alertTime: '2024-10-28 8:00',
    level: 'IV',
    levelClass: 'blue'
  }
])

// 区域数据表格
const districtTableData = ref([
  {
    id: '01',
    name: 'XXX小区',
    distance: '0',
    area: '400',
    population: '121'
  },
  {
    id: '02',
    name: 'XX村',
    distance: '0.2',
    area: '572',
    population: '77'
  },
  {
    id: '03',
    name: 'XXX小区',
    distance: '0.2',
    area: '601',
    population: '82'
  },
  {
    id: '04',
    name: 'XX村',
    distance: '0.4',
    area: '821',
    population: '11'
  },
  {
    id: '05',
    name: 'XX村',
    distance: '0.4',
    area: '821',
    population: '11'
  }
])

// 区域头部数据
const districtHeaderData = ref([
  {
    bgImage: 'r-bg8.png',
    label: '轻度影响',
    subItems: [
      { label: '街道', value: '8' },
      { label: '建筑', value: '6' },
      { label: '人员', value: '122' }
    ]
  },
  {
    bgImage: 'r-bg9.png',
    label: '中度影响',
    subItems: [
      { label: '街道', value: '8' },
      { label: '建筑', value: '6' },
      { label: '人员', value: '122' }
    ]
  },
  {
    bgImage: 'r-bg10.png',
    label: '严重影响',
    subItems: [
      { label: '街道', value: '8' },
      { label: '建筑', value: '6' },
      { label: '人员', value: '122' }
    ]
  }
])

// 人员配置数据
const personnelStats = ref([
  {
    title: '武警',
    icon: 'r-bg14.png',
    teams: '3',
    people: '6'
  },
  {
    title: '运输组织',
    icon: 'r-bg15.png',
    teams: '6',
    people: '6'
  },
  {
    title: '消防',
    icon: 'r-bg16.png',
    teams: '3',
    people: '6'
  }
])

// 水泵数据表格
const pumpTableData = ref([
  {
    id: '01',
    name: '抽水泵',
    count: '2',
    flow: '10.51',
    contact: '赵某某 15276281172'
  },
  {
    id: '02',
    name: '抽水泵',
    count: '3',
    flow: '10.51',
    contact: '赵某某 15276281172'
  },
  {
    id: '03',
    name: '抽水泵',
    count: '4',
    flow: '10.51',
    contact: '赵某某 15276281172'
  },
  {
    id: '04',
    name: '抽水泵',
    count: '5',
    flow: '10.51',
    contact: '赵某某 15276281172'
  },
  {
    id: '05',
    name: '抽水泵',
    count: '5',
    flow: '10.51',
    contact: '赵某某 15276281172'
  }
])

// 消防人员数据表格
const firefighterTableData = ref([
  {
    id: '01',
    name: '消防一大队',
    contact: '赵某某',
    count: '7'
  },
  {
    id: '02',
    name: '消防一大队',
    contact: '赵某某',
    count: '10'
  },
  {
    id: '03',
    name: '消防一大队',
    contact: '赵某某',
    count: '8'
  },
  {
    id: '04',
    name: '消防一大队',
    contact: '赵某某',
    count: '9'
  },
  {
    id: '05',
    name: '消防一大队',
    contact: '赵某某',
    count: '10'
  }
])

// 水位折线图时间轴标签
const timeLabels = ref([
  '08:00',
  '09:00',
  '10:00',
  '11:00',
  '12:00',
  '13:00',
  '14:00',
  '15:00',
  '16:00',
  '17:00',
  '18:00',
  '19:00',
  '20:00',
  '21:00',
  '22:00'
])

// 水位折线图实例
let waterLevelChartInstance = null

// 设备统计数据
const equipmentStats = ref([
  {
    label: '抽水泵',
    value: '30',
    icon: 'r-bg11.png'
  },
  {
    label: '沙袋',
    value: '1821',
    icon: 'r-bg12.png'
  },
  {
    label: '照明设备',
    value: '300',
    icon: 'r-bg13.png'
  }
])
// 连接数字孪生配置
const options = ref({
  domId: 'player', // 修改为新的ID
  apiOptions: {
    onReady: function () {
      console.info('此时可以调API了')
      // 设置鼠标拾取掩码，允许鼠标点击、移动和悬停事件
    }
  }
})

const api = ref(null)
// 太原数字孪生内网地址
const host = ref('*************:8008/dts')
// const host = ref("************:8080");
// 组件挂载时启动定时器和初始化屏幕适配
onMounted(() => {
  // 初始化时间
  updateTime()
  // 设置定时器更新时间
  timer = setInterval(updateTime, 1000)

  // 初始化图表
  initRainfallChart()
  initBarChart()
  initBottomBarChart() // 添加调用底部图表初始化函数
  initWaterLevelChart()
  // 修改这里，接入地图配置
  try {
    // 确保先引入了ac.min.js
    if (typeof acapi !== 'undefined') {
      // 创建数字孪生平台实例
      console.log('加载飞渡')
      api.value = new DigitalTwinPlayer(host.value, options.value)
      console.log('数字孪生平台初始化成功')

      // 不在这里设置鼠标交互，而是在onReady回调中设置
    } else {
      console.error('ac.min.js未正确加载，请检查引入路径')
    }
  } catch (error) {
    console.error('数字孪生平台初始化失败:', error)
  }
  // 启动数据动态更新
  statsUpdateTimer = setInterval(updateStatsData, 5000) // 每5秒更新一次统计数据

  window.addEventListener('resize', handleResize)
})

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
  if (barChartInstance) {
    barChartInstance.resize()
  }
  if (bottomBarChartInstance) {
    bottomBarChartInstance.resize()
  }
  if (waterLevelChartInstance) {
    waterLevelChartInstance.resize()
  }
}

// 组件卸载时清除定时器和屏幕适配事件监听
onUnmounted(() => {
  // 清除定时器
  if (timer) {
    clearInterval(timer)
  }

  // 清除统计数据更新定时器
  if (statsUpdateTimer) {
    clearInterval(statsUpdateTimer)
  }

  // 清除图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }
  if (barChartInstance) {
    barChartInstance.dispose()
  }
  if (bottomBarChartInstance) {
    bottomBarChartInstance.dispose()
  }
  if (waterLevelChartInstance) {
    waterLevelChartInstance.dispose()
  }

  window.removeEventListener('resize', handleResize)
})

// 初始化水位折线图
const initWaterLevelChart = () => {
  if (waterLevelChartInstance) {
    waterLevelChartInstance.dispose()
  }

  waterLevelChartInstance = echarts.init(waterLevelChart.value)

  const option = {
    grid: {
      top: '10%',
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c} m'
    },
    legend: {
      show: true,
      right: '60%',
      top: '0%',
      textStyle: {
        color: '#fff',
        fontSize: 22
      },
      itemWidth: 40,
      itemHeight: 3,
      itemGap: 10,
      itemStyle: {
        color: '#00C6FF'
      },
      data: [
        {
          name: '水位',
          icon: 'rect'
        }
      ]
    },
    xAxis: {
      type: 'category',
      data: timeLabels.value,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: '#fff',
        fontSize: 22,
        padding: [15, 0, 0, 0]
      }
    },
    yAxis: {
      type: 'value',
      min: 3.41,
      max: 3.61,
      interval: 0.05,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      axisLabel: {
        color: '#fff',
        fontSize: 22
      }
    },
    series: [
      {
        name: '水位',
        data: [3.43, 3.45, 3.47, 3.48, 3.49, 3.52, 3.59, 3.57, 3.54, 3.51, 3.49, 3.47, 3.45, 3.43, 3.42],
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: {
          width: 2,
          color: '#00C6FF'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 198, 255, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(0, 198, 255, 0)'
              }
            ]
          }
        }
      }
    ]
  }

  waterLevelChartInstance.setOption(option)
  // 确保图表铺满容器
  waterLevelChartInstance.resize()
}

// 更新统计数据函数
const updateStatsData = () => {
  // 这里可以添加更新统计数据的逻辑
  // 例如模拟随机变化数据
  totalPoints.value = Math.floor(Math.random() * 20) + 160

  // 随机更新级别统计数据
  levelStats.value = levelStats.value.map(item => {
    return {
      ...item,
      value: Math.floor(Math.random() * 10) + 15
    }
  })
}
</script>

<style lang="scss" scoped>
// 基础变量和混合
// 屏幕容器
.screen-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;

  // 全适配模式下的居中处理
  .full-scale & {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 背景层
.bg-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%; // 修改为100% 100%确保图片完整显示
}

.bg-layer-1 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 1;
}

.bg-layer-2 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 2;
}

.bg-layer-3 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 3;
}

// 地图容器
.map-container {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 5;
}

/* 三栏布局 - 容器 */
.left-container,
.middle-container,
.right-container {
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 10; // 确保UI在最上层
  pointer-events: none; // 容器本身不接收鼠标事件，允许事件穿透到地图
}

.left-container {
  left: 0;
  width: 27%;
}

.middle-container {
  left: 45%;
  // width: 40%;
  height: auto;
  // top: 2%;
}

.right-container {
  left: 73%;
  width: 27%;
}
/* 三栏内部区域 */
.left-section {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: vh(15);
  padding: vh(40) vw(0) vh(75) vw(90);
}

.right-section {
  width: 100%;
  height: 100%;
  padding: vh(40) vw(90) vh(0) vw(0);
}

.middle-section {
  // width: 100%;
  height: 250px;
  padding: vh(5);
  pointer-events: auto; // 恢复鼠标事件，使UI元素可交互
}

/* 时间和天气样式 */
.time-weather {
  /**1920 */
  display: flex;
  align-items: center;
  color: #fff;
  margin-left: vw(40);
  // margin-top: vh(10);

  // @include respond-to("ultra-wide") {
  //   /** 1920-8162 以上*/
  //   margin-bottom: 20px;
  //   margin-left: 200px;
  //   margin-top: 30px;
  // }

  // @include respond-to("standard") {
  //   /** 1920 以下*/
  //   margin-left: 40px;
  //   margin-top: 10px;
  // }
}

.time-date {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.time {
  font-size: vh(24);

  // @include respond-to("standard") {
  //   font-size: 16px;
  // }

  // @include respond-to("ultra-wide") {
  //   font-size: 20px;
  // }
}

.date {
  font-size: vh(14);
  margin-right: vw(2);
  // @include respond-to("standard") {
  //   font-size: 10px;
  // }

  // @include respond-to("ultra-wide") {
  //   font-size: 10px;
  // }
}

.divider {
  margin: 0 vw(20);
  width: vw(2);
  height: vh(30);
  background-color: #3a607c;
  // @include respond-to("ultra-wide") {
  //   height: 30px;
  // }
}

.weather {
  display: flex;
  align-items: center;
}

.weather-icon {
  width: vw(24);
  height: vh(24);
  margin-right: vw(10);

  // @include respond-to("ultra-wide") {
  //   width: 24px;
  //   height: 24px;
  // }
}

.weather-icon1 {
  width: vw(24);
  height: vh(22);

  // @include respond-to("ultra-wide") {
  //   width: 24px;
  //   height: 24px;
  // }
}

.temperature {
  font-size: vh(24);
  // @include respond-to("ultra-wide") {
  //   font-size: 20px;
  // }
}

/* 中间区域标题 */
.section-title {
  font-family: YouSheBiaoTiHei;
  font-size: vh(80);
  color: #ffffff;
  text-align: center;
  font-style: normal;
}

/* 导航按钮 */
.nav-buttons {
  display: flex;
  justify-content: center;
  gap: vw(20);
  margin-top: vh(60);
}

.nav-button {
  width: vw(200);
  height: vh(80);
  background-image: url('@/assets/images/home/<USER>');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  padding-top: vh(14);
  font-family: JiangChengXieHei;
  color: #fff;
  font-size: vh(24);
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-button.active {
  background-image: url('@/assets/images/home/<USER>');
  color: #fff;
  font-weight: bold;
}

/* 用户信息和返回门户样式 */
.user-portal-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: #fff;
  margin-right: vw(40);
  margin-top: vh(10);
  gap: vw(20);

  // @include respond-to("ultra-wide") {
  //   margin-right: 200px;
  //   margin-top: 30px;
  // }

  // @include respond-to("standard") {
  //   margin-right: 40px;
  //   margin-top: 10px;
  // }
}

.user-info,
.portal-back {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-icon,
.portal-icon {
  width: vw(24);
  height: vh(24);
  margin-right: vw(5);

  // @include respond-to("ultra-wide") {
  //   width: 24px;
  //   height: 24px;
  // }
}

.username,
.portal-text {
  font-size: vh(14);

  // @include respond-to("ultra-wide") {
  //   font-size: 16px;
  // }
}

/* 左右布局容器样式 */
.content-layout {
  width: 100%;
  height: calc(100% - 50px);
  /* 减去顶部时间天气区域的高度 */
  display: flex;
  flex-direction: column;
  /* 改为纵向排列 */
  margin-top: vh(10);
  gap: 1%;
}

.content-top {
  flex: 7.5;
  /* 上部分占7.5份 */
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 1.5%;
  position: relative;
}

.content-top-left {
  flex: 5;
  border-radius: 4px;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: vh(10);
}

/* 左侧三个板块样式 */
.left-top-section,
.left-middle-section,
.left-bottom-section {
  flex: 1;
  background: linear-gradient(90deg, rgba(6, 72, 146, 0.24) 0%, rgba(6, 79, 156, 0.06) 100%);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.content-top-right {
  flex: 5;
  border-radius: 4px;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: vh(10);
}

/* 右侧两个板块样式 */
.right-top-section {
  flex: 4;
  background: linear-gradient(90deg, rgba(6, 72, 146, 0.24) 0%, rgba(6, 79, 156, 0.06) 100%);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.right-bottom-section {
  flex: 6;
  background: linear-gradient(90deg, rgba(6, 72, 146, 0.24) 0%, rgba(6, 79, 156, 0.06) 100%);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.section-content {
  flex: 1;
  overflow: hidden;
}

.content-bottom {
  flex: 2.5;
  /* 下部分占2.5份 */
  width: 100%;
  background: linear-gradient(90deg, rgba(6, 72, 146, 0.24) 0%, rgba(6, 79, 156, 0.06) 100%);
  border-radius: 4px;
}

/* 雨水情况分析图表样式 - 更新后的样式 */
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: vh(45);
  margin-bottom: vh(5);
  background: url('@/assets/images/home/<USER>') no-repeat;
  background-size: 100% 100%;
  padding: 0;
}

.chart-title {
  font-weight: normal;
  font-size: vh(26);
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 vw(50);
  font-family: JiangChengXieHei;
  color: #d8f1ff;
  // @include respond-to("ultra-wide") {
  //   font-size: 18px;
  // }
}

.chart-unit {
  font-family: Alibaba-PuHuiTi;
  font-weight: normal;
  font-size: vh(22);
  margin-right: vw(20);
  color: #fff;
}

.chart-tabs {
  display: flex;
  height: 100%;
}

.chart-tab {
  padding: 0 vw(50);
  height: 100%;
  display: flex;
  align-items: center;
  color: #fff;
  font-size: vh(14);
  cursor: pointer;
  margin-left: vw(5);
  background: url('@/assets/images/home/<USER>') no-repeat;
  background-size: 100% 100%;

  &.active {
    background: url('@/assets/images/home/<USER>') no-repeat;
    background-size: 100% 100%;
    color: #00ffff;
  }

  // @include respond-to("ultra-wide") {
  //   font-size: 16px;
  // }
}

.rainfall-chart {
  width: 100%;
  height: calc(100% - vh(30));
  margin-top: vh(10);
  position: relative;
}

.rainfall-bar-chart {
  width: 100%;
  height: 100%;
  position: relative;
}

.rain-summary-panel {
  border-radius: vh(12);
  padding: 0 vw(20) 0 vw(20);
  color: #fff;
  display: flex;
  flex-direction: column;
  gap: vh(18);
}

.rain-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: vh(18);
  font-weight: bold;
  margin-bottom: vh(8);

  .rain-unit {
    font-size: vh(14);
    opacity: 0.7;
    font-weight: normal;
  }
}

.rain-main-data {
  display: flex;
  justify-items: flex-start;
  gap: vw(53);

  .rain-data-block {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: vh(10) 0 vh(8) 0;

    .rain-icon {
      width: vw(105);
      height: vh(90);
      margin-bottom: 0;
      margin-right: vw(10);
    }

    .rain-info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .rain-value {
        font-size: vh(36);
        font-family: DIN-Bold;
        margin-bottom: vh(2);
      }

      .rain-label {
        font-family: Alibaba-PuHuiTi;
        font-weight: normal;
        font-size: 22px;
      }
    }
  }
}

.rain-forecast-list {
  display: flex;
  justify-content: space-between;
  gap: vw(12);

  .rain-forecast-item {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: vh(8) 0 vh(6) 0;

    .forecast-time {
      width: vh(98);
      height: vh(98);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: vh(40);
      font-family: DIN-Bold;
      font-weight: bold;
      margin-bottom: 0;
      margin-right: vw(10);
      border-radius: 50%;

      &.blue {
        background-image: url('@/assets/images/alarmplan/qipao-b.png');
        background-size: 100% 100%;
      }

      &.yellow {
        background-image: url('@/assets/images/alarmplan/qipao-y.png');
        background-size: 100% 100%;
      }

      &.dblue {
        background-image: url('@/assets/images/alarmplan/qipao-b.png');
        background-size: 100% 100%;
      }

      &.green {
        background-image: url('@/assets/images/alarmplan/qipao-g.png');
        background-size: 100% 100%;
      }
    }

    .forecast-info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;

      .forecast-value {
        font-size: vh(36);
        font-family: DIN-Bold;
        font-weight: bold;
        margin-bottom: vh(2);
      }

      .forecast-label {
        font-family: Alibaba-PuHuiTi;
        font-weight: normal;
        font-size: 22px;
      }
    }
  }
}

.bottom-bar-chart {
  width: 100%;
  height: 100%;
  min-height: vh(110);
  position: relative;
}

/* 实时告警模块样式 */
.alarm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: vh(45);
  margin-bottom: vh(5);
  background: url('@/assets/images/home/<USER>') no-repeat;
  background-size: 100% 100%;
  padding: 0;
}

.alarm-title {
  font-weight: normal;
  font-size: vh(26);
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 vw(50);
  font-family: JiangChengXieHei;
  color: #d8f1ff;
}

.alarm-tabs {
  display: flex;
  height: 100%;
}

.alarm-tab {
  padding: 0 vw(20);
  height: 100%;
  display: flex;
  align-items: center;
  color: #fff;
  font-family: JiangChengXieHei, JiangChengXieHei;
  font-weight: normal;
  font-size: vh(20);
  cursor: pointer;
  margin-left: vw(5);
  background: url('@/assets/images/home/<USER>') no-repeat;
  background-size: 100% 100%;

  &.active {
    background: url('@/assets/images/home/<USER>') no-repeat;
    background-size: 100% 100%;
  }
}

.water-stats-container {
  display: flex;
  flex-direction: row;
  height: 100%;
  padding: vh(0) vw(10);
  gap: vw(10);
}

.total-points-card {
  position: relative;
  width: 30%;
  height: 80%;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.total-points-card::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: url('@/assets/images/alarmplan/l-bg3.png') no-repeat bottom;
  z-index: 0;
}

.card-title {
  font-family: Alibaba-PuHuiTi;
  font-weight: normal;
  font-size: vh(24);
  color: #fff;
  margin-bottom: vh(10);
  z-index: 1;
}

.total-value {
  font-size: vh(42);
  margin-bottom: vh(30);
  font-family: DIN-Bold;
  color: #fff;
  font-weight: bold;
  z-index: 1;
}

.level-stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: vh(0) vw(15);
  width: 70%;
  height: 100%;
}

.level-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: vh(149);
  height: vh(105);
  background-size: 100% 100%;
  position: relative;
  padding: 0;
}

.level-title {
  font-family: Alibaba-PuHuiTi;
  font-weight: normal;
  font-size: vh(22);
  color: rgba(255, 255, 255, 1);
  position: absolute;
  top: vh(8);
}

.level-value {
  font-size: vh(36);
  font-family: DIN-Bold;
  font-weight: bold;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.alarm-table-container {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(6, 72, 146, 0.24) 0%, rgba(6, 79, 156, 0.06) 100%);
  border-radius: vh(8);
  display: flex;
  flex-direction: column;
  padding: 0 vw(10) vh(10) vw(10);
  box-sizing: border-box;
}
.alarm-table-tabs {
  display: flex;
  .alarm-table-tab {
    flex: 1;
    text-align: center;
  }
}
.alarm-table-tab {
  width: 100%;
  height: vh(40);
  line-height: vh(40);
  margin-bottom: vh(10);
  font-size: vh(22);
  margin-top: vh(10);
  color: #fff;
  cursor: pointer;
  border: vh(1) solid #20689b;
  transition: all 0.3s;

  &.active {
    background: #2594e4;
    color: #fff;
    font-weight: bold;
  }

  &:hover:not(.active) {
    background: rgba(30, 90, 155, 0.3);
  }
}
.alarm-table-header,
.alarm-tr {
  display: flex;
  align-items: center;
  min-height: vh(50);
  font-family: Alibaba-PuHuiTi;
  font-weight: normal;
  font-size: vh(22);
  width: 100%;
}
.alarm-table-header {
  background: #115a8f;
  color: #fff;
  font-weight: bold;
  margin-bottom: vh(4);
}
.alarm-th,
.alarm-td {
  text-align: center;
  padding: 0 vw(2);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  box-sizing: border-box;
}
.th-index {
  width: 5.5%;
}
.th-risk {
  width: 15%;
}
.th-time {
  width: 15%;
}
.th-water {
  width: 10%;
}
.th-level {
  width: 10%;
}
.th-status {
  width: 15%;
}
.th-respond {
  width: 15%;
}
.th-area {
  width: 15%;
}
.alarm-table-body {
  height: vh(500);
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
  padding-bottom: vh(10); /* 添加底部填充确保完全显示 */

  &::-webkit-scrollbar {
    display: none;
  }
}
.alarm-tr {
  // background: rgba(255, 255, 255, 0.03);
  border-bottom: vh(2) solid rgba(0, 198, 255, 0.15);
  color: #fff;
  transition: background 0.2s;

  &:hover {
    background: rgba(0, 198, 255, 0.12);
  }
}
.level-badge {
  display: inline-block;
  min-width: vw(28);
  padding: vh(2) vw(8);
  border-radius: vh(12);
  font-size: vh(22);
}
.level-yellow {
  color: #edc96e;
}
.level-blue {
  color: #1a86fc;
}
.level-red {
  color: #ff3b3b;
}
.level-orange {
  color: #fc7f22;
}

/* 小时降雨监测表样式 */
.rain-monitor-table-container {
  width: 100%;
  height: 100%;

  border-radius: vh(8);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
}
.rain-monitor-table-header {
  display: flex;
  align-items: center;
  min-height: vh(48);
  background: #115a8f;
  color: #fff;
  font-weight: bold;
  font-size: vh(22);
  border-bottom: vh(2) solid #205080;
  font-family: Alibaba-PuHuiTi, sans-serif;
}
.rain-th,
.rain-td {
  text-align: center;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.th-index1 {
  width: 4%;
}
.th-station1 {
  width: 11%;
}
.th-hour1 {
  width: 10%;
}
.th-warning1 {
  width: 8%;
}
.th-over1 {
  width: 10%;
}
.th-level1 {
  width: 8%;
}
.th-time1 {
  width: 10%;
}
.th-f11 {
  width: 13%;
}
.th-f21 {
  width: 13%;
}
.th-f41 {
  width: 13%;
}

.rain-monitor-table-body {
  height: vh(210);
  overflow-y: auto;
  -ms-overflow-style: none; /* IE 和 Edge */
  scrollbar-width: none; /* Firefox */
  padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
}
.rain-monitor-table-body::-webkit-scrollbar {
  display: none; /* Chrome, Safari 和 Opera */
}
.rain-tr {
  display: flex;
  align-items: center;
  min-height: vh(44);
  font-size: vh(22);
  color: #fff;
  border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
  transition: background 0.2s;
}
.rain-tr:hover {
  background: rgba(0, 198, 255, 0.1);
}
.over-red {
  color: #ff3b3b;
  font-weight: bold;
}
.over-blue {
  color: #1a86fc;
  font-weight: bold;
}
.level-badge {
  display: inline-block;
  min-width: vw(28);
  padding: vh(2) vw(8);
  border-radius: vh(12);
  font-weight: bold;
  font-size: vh(18);
}
.level-red {
  color: #ff3b3b;
}
.level-blue {
  color: #1a86fc;
}
.level-light {
  color: #7ed6fc;
}

/* 右侧区域底部样式 */
.right-section-bottom {
  display: flex;
  width: 100%;
  height: calc(100% - vh(60));
  margin-top: vh(20);
}

// .right-bottom-module {
//   width: 50%;
//   height: 100%;
// }
.right-module {
  width: 47%;
  height: 100%;
  background: linear-gradient(90deg, rgba(6, 72, 146, 0.24) 0%, rgba(6, 79, 156, 0.06) 100%);

  /* 添加全局样式，确保右侧模块中所有表格行都有底部边框 */
  table tbody tr {
    border-bottom: vh(2) solid rgba(58, 96, 124, 1) !important;
  }
}

.left-module {
  width: 53%;
  height: 100%;
  margin-right: vh(10);
  display: flex;
  flex-direction: column;
  gap: 1%;
}

.left-module-top {
  flex: 3;
  display: flex;
  flex-direction: column;
  background: linear-gradient(90deg, rgba(6, 72, 146, 0.24) 0%, rgba(6, 79, 156, 0.06) 100%);
}

.left-module-bottom {
  flex: 7;
  display: flex;
  flex-direction: column;
  background: linear-gradient(90deg, rgba(6, 72, 146, 0.24) 0%, rgba(6, 79, 156, 0.06) 100%);
}

.right-module {
  margin-left: vh(10);
}

.module-content {
  width: 100%;
  height: 100%;
  padding: vh(10);
}

/* 新增样式 */
.plan-selection-area {
  display: flex;
  align-items: center;
  padding: vh(5) vw(15);
  width: 100%;
}

.plan-selection-label {
  font-family: Alibaba-PuHuiTi;
  font-weight: normal;
  font-size: vh(22);
  color: rgba(255, 255, 255, 1);
  white-space: nowrap;
}

.plan-selection-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-family: Alibaba-PuHuiTi;
  font-weight: normal;
  height: vh(53);
  font-size: vh(22);
  color: #fff;
  padding: vh(4) vw(15);
  border-radius: vh(3);
  background: rgba(18, 78, 119, 0.45);
  border: vh(2) solid #2594e4;
  margin-left: vw(15);
  flex: 1;
}

.selected-plan {
  font-weight: normal;
  margin-right: vw(10);
}

.dropdown-arrow {
  font-size: vh(12);
  color: rgba(0, 198, 255, 0.8);
}

.plan-description-area {
  margin-bottom: vh(15);
  padding: vh(5) vw(15);
}

.description-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: vh(5);
}

.description-label {
  font-family: Alibaba-PuHuiTi;
  font-weight: normal;
  font-size: vh(22);
  font-family: Alibaba-PuHuiTi;
  font-weight: normal;
  color: rgba(255, 255, 255, 1);
  min-width: vw(140);
  &.descript {
    color: #5dbbff;
  }
}

.description-value {
  font-family: Alibaba-PuHuiTi;
  font-weight: normal;
  font-size: vh(22);
  color: #fff;
}

.water-data-cards {
  display: flex;
  justify-content: space-between;
  gap: vw(10);
}

.water-data-card {
  flex: 1;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: vh(109);
  width: vw(232);
  padding: vh(5) vw(5);
  background-size: 100% 100%;
}

.card-label {
  font-family: Alibaba-PuHuiTi;
  font-weight: normal;
  font-size: vh(22);
  color: rgba(255, 255, 255, 1);
  margin-bottom: vh(10);
}

.card-value {
  font-family: DIN-Bold;
  font-size: vh(40);
  color: #fff;
  font-weight: normal;
  margin-bottom: vh(50);
}

.water-level-chart {
  width: 100%;
  height: vh(350);
  margin-top: vh(10);
  position: relative;
}

.chart-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 vw(10);
}

.chart-label {
  font-family: Alibaba-PuHuiTi;
  font-weight: normal;
  font-size: vh(22);
  color: #fff;
}

.chart-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.time-axis {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 vw(10);
}

.time-axis span {
  font-family: Alibaba-PuHuiTi;
  font-weight: normal;
  font-size: vh(14);
  color: #fff;
}

/* 水位监测表格样式 */
.water-monitoring-table {
  width: 100%;
  height: vh(250);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: visible;
  margin-bottom: vh(10); /* 确保表格有足够的底部间距 */

  table {
    width: 100%;
    border-collapse: collapse;
    color: #fff;
    font-family: DIN-Bold, Alibaba-PuHuiTi;
    display: flex;
    flex-direction: column;

    thead {
      height: vh(50);
      background: #115a8f;
      display: table;
      width: 100%;
      table-layout: fixed;

      th {
        padding: vh(10) vw(5);
        text-align: center;
        font-size: vh(22);
        font-weight: normal;
      }
    }

    tbody {
      display: block;
      height: vh(195);
      overflow-y: auto;
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */

      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari and Opera */
      }

      tr {
        display: table;
        width: 100%;
        table-layout: fixed;
        height: vh(44);
        border-bottom: vh(2) solid rgba(58, 96, 124, 1);

        &:hover {
          background: rgba(0, 198, 255, 0.1);
        }

        td {
          padding: vh(8) vw(5);
          text-align: center;
          font-size: vh(22);
          white-space: nowrap;
          overflow: visible;
          text-overflow: clip;
        }
      }
    }
  }
}

.level-badge {
  display: inline-block;
  min-width: vw(28);
  padding: vh(2) vw(8);
  border-radius: vh(12);
  font-weight: bold;
  font-size: vh(18);
}

.level-red {
  color: #ff3b3b;
}

.level-orange {
  color: #ff9500;
}

.level-yellow {
  color: #ffca0a;
}

.level-blue {
  color: #1a86fc;
}

/* 统计信息卡片样式 */
.stats-cards {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: vh(15);
}

.stats-card {
  display: flex;
  align-items: center;
  width: 25%;
  height: vh(100);
  // padding: vh(10) vw(0);
}

.stats-icon {
  width: vh(100);
  height: vh(100);
  display: flex;
  justify-content: center;
  align-items: center;
  // margin-right: vw(10);

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.stats-info {
  flex: 1;
}

.stats-title {
  font-family: Alibaba-PuHuiTi;
  font-size: vh(18);
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: vh(5);
}

.stats-value {
  font-family: DIN-Bold;
  font-size: vh(36);
  color: #fff;
  font-weight: normal;

  .stats-unit {
    font-size: vh(18);
    color: rgba(255, 255, 255, 0.8);
    // margin-left: vw(5);
    font-weight: normal;
  }
}

.district-stats-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: #fff;
  gap: vh(15);
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.district-stats-header {
  display: flex;
  justify-content: space-between;
  gap: vw(10);
  margin-bottom: vh(5);
}

.district-stats-card {
  flex: 1;
  height: vh(120);
  border-radius: vh(4);
  transition: all 0.3s ease;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative; /* 添加相对定位以便放置标签 */

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  }
}

.stats-label {
  position: absolute;
  width: vw(110);
  height: vh(28);
  font-size: vh(18);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: vh(2);
  z-index: 1;
  top: vh(5);
  left: vw(5);
}

.stats-label-1 {
  background-color: rgba(38, 123, 200, 0.42);
  color: rgba(57, 195, 241, 1);
}

.stats-label-2 {
  background-color: rgba(255, 255, 2, 0.23);
  color: rgba(255, 235, 111, 1);
}

.stats-label-3 {
  background-color: rgba(255, 2, 2, 0.1);
  color: #ff6f6f;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: vh(10);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: vh(10);
}

.header-text {
  font-size: vh(22);
  color: rgba(255, 255, 255, 0.8);
}

.header-value {
  font-size: vh(32);
  font-family: DIN-Bold;
  color: #fff;
}

.card-info {
  width: 100%;
  margin-top: vh(35);
}

.info-row {
  display: flex;
  justify-content: space-around;
  width: 100%;
}

.info-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.info-label {
  font-size: vh(22);
  color: rgba(255, 255, 255, 0.8);
  margin-top: vh(5);
}

.info-value {
  font-size: vh(32);
  font-family: DIN-Bold;
  color: #fff;
}

.main-table-wrapper {
  width: 100%;
  overflow: hidden;
  table {
    width: 100%;
    border-collapse: collapse;
    color: #fff;
    font-family: DIN-Bold, Alibaba-PuHuiTi;
    display: flex;
    flex-direction: column;

    thead {
      height: vh(42);
      background: #115a8f;
      display: table;
      width: 100%;
      table-layout: fixed;

      th {
        padding: vh(10) vw(5);
        text-align: center;
        font-size: vh(22);
        font-weight: normal;
      }
    }

    tbody {
      display: block;
      height: vh(190);
      overflow-y: auto;
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */

      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari and Opera */
      }

      tr {
        display: table;
        width: 100%;
        table-layout: fixed;
        height: vh(44);
        border-bottom: vh(2) solid rgba(58, 96, 124, 1);

        &:hover {
          background: rgba(0, 198, 255, 0.1);
        }

        td {
          padding: vh(8) vw(5);
          text-align: center;
          font-size: vh(22);
          white-space: nowrap;
          overflow: visible;
          text-overflow: clip;
        }
      }

      /* 确保最后一行有足够的底部间距 */
      tr:last-child {
        margin-bottom: vh(10);
      }
    }
  }

  /* 增加表格容器高度，确保滚动时内容完全显示 */
  .district-data-table {
    tbody {
      padding-bottom: vh(10);
      height: vh(195); /* 增加高度以确保完全显示 */
    }
  }
}

.equipment-stats {
  display: flex;
  justify-content: space-between;
  gap: vw(15);
  margin: vh(15) 0;
}

.equipment-item {
  flex: 1;
  display: flex;
  align-items: center;

  padding: vh(10) vw(15);
}

.equipment-icon {
  width: vh(78);
  height: vh(78);
  margin-right: vw(15);

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.equipment-info {
  display: flex;
  flex-direction: column;
}

.equipment-value {
  font-size: vh(36);
  font-family: DIN-Bold;
  color: #fff;
  margin-bottom: vh(5);
}

.equipment-label {
  font-size: vh(22);
  color: rgba(255, 255, 255, 1);
}

.pump-table-wrapper {
  width: 100%;
  overflow: hidden;
  margin-bottom: vh(15);

  table {
    width: 100%;
    border-collapse: collapse;
    color: #fff;
    font-family: DIN-Bold, Alibaba-PuHuiTi;
    display: flex;
    flex-direction: column;

    thead {
      height: vh(42);
      background: #115a8f;
      display: table;
      width: 100%;
      table-layout: fixed;

      th {
        padding: vh(10) vw(5);
        text-align: center;
        font-size: vh(22);
        font-weight: normal;

        &:nth-child(1) {
          /* 序号 */
          width: 10%;
        }
        &:nth-child(2) {
          /* 名称 */
          width: 15%;
        }
        &:nth-child(3) {
          /* 数量 */
          width: 15%;
        }
        &:nth-child(4) {
          /* 流量 */
          width: 15%;
        }
        &:nth-child(5) {
          /* 联系人/电话 */
          width: 45%;
        }
      }
    }

    tbody {
      display: block;
      height: vh(195);
      overflow-y: auto;
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */

      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari and Opera */
      }

      tr {
        display: table;
        width: 100%;
        table-layout: fixed;
        height: vh(44);
        border-bottom: vh(2) solid rgba(58, 96, 124, 1);

        &:hover {
          background: rgba(0, 198, 255, 0.1);
        }

        td {
          padding: vh(8) vw(5);
          text-align: center;
          font-size: vh(22);
          white-space: normal; /* 允许文本换行 */
          overflow: visible;
          word-break: break-word; /* 允许在单词内换行 */

          &:nth-child(1) {
            /* 序号 */
            width: 10%;
          }
          &:nth-child(2) {
            /* 名称 */
            width: 15%;
          }
          &:nth-child(3) {
            /* 数量 */
            width: 15%;
          }
          &:nth-child(4) {
            /* 流量 */
            width: 15%;
          }
          &:nth-child(5) {
            /* 联系人/电话 */
            width: 45%;
          }
        }
      }

      /* 确保最后一行有足够的底部间距 */
      tr:last-child {
        margin-bottom: vh(10);
      }
    }
  }
}

.personnel-stats {
  display: flex;
  justify-content: space-between;
  gap: vw(15);
}

.personnel-item {
  flex: 1;
  display: flex;
  height: vh(100);
  padding: vh(0) vw(20);
  background-repeat: no-repeat;
  background-position: left center;
  background-size: 100% 100%;
  background-origin: content-box;
  position: relative;
}

.personnel-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-left: vh(100); /* 为背景图留出空间 */
}

/* 已删除personnel-icon，改为背景图 */

.personnel-title {
  font-size: vh(24);
  color: rgba(255, 255, 255, 1);
  margin-top: vh(5);
  padding-left: vw(40);
}

.personnel-data {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: vw(10);
  padding-left: vw(40);
  margin-bottom: vh(0);
  padding-top: vh(15);
}

.personnel-count {
  font-size: vh(32);
  font-family: DIN-Bold;
  color: #fff;

  span {
    font-size: vh(22);
    color: rgba(255, 255, 255, 1);
    margin-left: vw(5);
  }
}

.firefighter-table-wrapper {
  width: 100%;
  overflow: hidden;

  table {
    width: 100%;
    border-collapse: collapse;
    color: #fff;
    font-family: DIN-Bold, Alibaba-PuHuiTi;
    display: flex;
    flex-direction: column;

    thead {
      height: vh(42);
      background: #115a8f;
      display: table;
      width: 100%;
      table-layout: fixed;

      th {
        padding: vh(10) vw(5);
        text-align: center;
        font-size: vh(22);
        font-weight: normal;
      }
    }

    tbody {
      display: block;
      height: vh(195);
      overflow-y: auto;
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */

      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari and Opera */
      }

      tr {
        display: table;
        width: 100%;
        table-layout: fixed;
        height: vh(44);
        border-bottom: vh(2) solid rgba(58, 96, 124, 1);

        &:hover {
          background: rgba(0, 198, 255, 0.1);
        }

        td {
          padding: vh(8) vw(5);
          text-align: center;
          font-size: vh(22);
          white-space: nowrap;
          overflow: visible;
          text-overflow: clip;
        }
      }

      /* 确保最后一行有足够的底部间距 */
      tr:last-child {
        margin-bottom: vh(10);
      }
    }
  }
}
</style>
