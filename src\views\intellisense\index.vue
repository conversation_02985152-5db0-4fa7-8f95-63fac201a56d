<template>
  <div class="screen-container">
    <div class="bg-layer bg-layer-1"></div>
    <div class="bg-layer bg-layer-2"></div>
    <div class="bg-layer bg-layer-3"></div>
    <!-- 地图容器，修改ID避免重复 -->
    <div id="player" class="map-container"></div>

    <!-- 左侧区域容器 -->
    <div class="left-container">
      <div class="left-section">
        <!-- <div class="time-weather">
          <div class="time-date">
            <div class="time">{{ currentTime }}</div>
            <div class="date">{{ currentDate }}</div>
          </div>
          <div class="divider"></div>
          <div class="weather">
            <img class="weather-icon" :src="getAssetsFile('sunny.png')" alt="天气" />
            <img class="weather-icon1" :src="getAssetsFile('wendu.png')" alt="温度" />
            <span class="temperature">17℃</span>
          </div>
        </div> -->

        <!-- 上下布局容器 -->
        <div class="left-content">
          <!-- 第一部分 -->

          <div class="content-layout-first">
            <div class="content-layout-header">
              <div class="weather-info-bar">
                <div class="current-weather">
                  <span class="weather-label">当前天气</span>
                  <div class="city-info">
                    <img class="location-icon" src="@/assets/images/home/<USER>" alt="" />
                    <span class="city-name">太原市</span>
                  </div>
                </div>
                <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />
                <div class="weather-detail">
                  <div class="weather-icon-large">
                    <img src="@/assets/images/home/<USER>" alt="大雨" />
                  </div>
                  <div class="weather-data">
                    <div class="weather-type">
                      <span class="weather-name">大雨</span>
                      <span class="weather-temp">19优</span>
                    </div>
                    <div class="wind-info">
                      <span>东风 </span>
                      <span> 2级</span>
                    </div>
                  </div>
                </div>
                <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />
                <div class="weather-forecast">
                  <div class="forecast-item">
                    <div class="forecast-title">24h累计降雨</div>
                    <div class="forecast-value">
                      <span class="forecast-value-num">50</span>
                      <span class="forecast-value-unit">mm</span>
                    </div>
                  </div>
                  <div class="forecast-item">
                    <div class="forecast-title">未来2h降雨</div>
                    <div class="forecast-value1">
                      <span class="forecast-value-num">20</span>
                      <span class="forecast-value-unit">mm</span>
                    </div>
                  </div>
                </div>
                <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />

                <div class="publish-info">
                  <span class="publish-label">预报发布</span>
                  <div class="publish-times">
                    <div class="time-item">开始时间: 20:50:00</div>
                    <div class="time-item">结束时间: 18:00:00</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="content-left-wrap">
              <div class="wrap-left">
                <div class="chart-headers">
                  <div class="chart-title">短临气象</div>
                </div>
                <div ref="wrapLeftCharts" class="wrap-left-charts"></div>
                <div class="jishui">
                  <div class="jishui-left">
                    <div class="chart-headers">
                      <div class="chart-title">积水点信息</div>
                      <div class="alarm-tabs">
                        <div
                          v-for="(tab, index) in alarmTabs"
                          :key="index"
                          class="alarm-tab"
                          :class="{ active: currentAlarmTab === tab.value }"
                          @click="changejishuipaihang(tab.value)"
                        >
                          {{ tab.name }}
                        </div>
                      </div>
                    </div>
                    <div class="rain-monitor-table-container">
                      <div class="rain-monitor-table-header">
                        <div class="rain-th th-index1">积水点</div>
                        <div class="rain-th th-station1">场景类型</div>
                        <div class="rain-th th-hour1">数据</div>
                        <div class="rain-th th-warning1">通讯时间</div>
                      </div>
                      <div class="rain-monitor-table-body">
                        <div class="rain-tr" v-for="(row, idx) in rainMonitorTableData" :key="row.index">
                          <div class="rain-td th-index1">{{ row.point }}</div>
                          <div class="rain-td th-station1">{{ row.type }}</div>
                          <div class="rain-td th-hour1">{{ row.data }}</div>
                          <div class="rain-td th-warning1">{{ row.time }}</div>
                        </div>
                      </div>
                    </div>
                    <div class="chart-headers">
                      <div class="chart-title">泵站水位</div>
                    </div>
                    <div class="rain-monitor-table-container1">
                      <div class="rain-monitor-table-header">
                        <div class="rain-th th-index1">站名</div>
                        <div class="rain-th th-station1">实时水位(m)</div>
                        <div class="rain-th th-hour1">水位变化速率(cm/h)</div>
                        <div class="rain-th th-warning1">警戒水位差值(m)</div>
                        <div class="rain-th th-warning2">实时流量(m²/s)</div>
                        <div class="rain-th th-warning3">水流速度(m/s)</div>
                      </div>
                      <div class="rain-monitor-table-body">
                        <div class="rain-tr" v-for="(row, idx) in bengzhanshuiwei" :key="row.index">
                          <div class="rain-td th-index1">{{ row.station_name }}</div>
                          <div class="rain-td th-station1">{{ row.real_time_water_level }}</div>
                          <div class="rain-td th-hour1">{{ row.water_level_change_rate }}</div>
                          <div class="rain-td th-warning1">{{ row.alert_water_level_difference }}</div>
                          <div class="rain-td th-warning2">{{ row.real_time_flow }}</div>
                          <div class="rain-td th-warning3">{{ row.water_flow_speed }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="jishui-right">
                    <div class="chart-headers">
                      <div class="chart-title">管网预警</div>
                    </div>
                    <div class="guanwang">
                      <div class="guanwang-wrap" v-for="gItem in gwyjDATA">
                        <div class="guanwang-item">
                          <div class="guanwang-item-value">{{ gItem.warning }}</div>
                          <div class="guanwang-item-val">{{ gItem.online }}/{{ gItem.total }}</div>
                        </div>
                        <div class="guanwang-text">{{ gItem.type }}</div>
                      </div>
                    </div>
                    <div style="height: 40px"></div>
                    <div class="chart-headers">
                      <div class="chart-title">物资调配统计</div>
                      <div class="alarm-tabs">
                        <div class="alarm-tab">更多</div>
                      </div>
                    </div>
                    <div class="rain-monitor-table-container">
                      <div class="rain-monitor-table-header">
                        <div class="rain-th th-index1">序号</div>
                        <div class="rain-th th-station1">名称</div>
                        <div class="rain-th th-hour1">数量</div>
                        <div class="rain-th th-warning1">部门</div>
                        <div class="rain-th th-warning1">地址</div>
                      </div>
                      <div class="rain-monitor-table-body">
                        <div class="rain-tr" v-for="(row, idx) in wzdptjDATA" :key="row.index">
                          <div class="rain-td th-index1">{{ row.seq }}</div>
                          <div class="rain-td th-station1">{{ row.name }}</div>
                          <div class="rain-td th-hour1">{{ row.quantity }}</div>
                          <div class="rain-td th-warning1">{{ row.department }}</div>
                          <div class="rain-td th-warning1">{{ row.address }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="wrap-right">
                <div class="chart-headers">
                  <div class="chart-title">报警信息</div>
                  <div class="alarm-tabs">
                    <div class="alarm-tab">区/县/街道</div>
                  </div>
                </div>
                <div class="guanwang">
                  <div class="guanwang-wrap">
                    <div class="guanwang-item">
                      <div class="guanwang-item-value">{{ bjxxDATA.total_alert }}</div>
                    </div>
                    <div class="guanwang-text">总预警</div>
                  </div>
                  <div class="guanwang-wrap">
                    <div class="guanwang-item">
                      <div class="guanwang-item-value">{{ bjxxDATA.device_alert }}</div>
                    </div>
                    <div class="guanwang-text">设备预警</div>
                  </div>
                  <div class="guanwang-wrap">
                    <div class="guanwang-item">
                      <div class="guanwang-item-value">{{ bjxxDATA.pipeline_alert }}</div>
                    </div>
                    <div class="guanwang-text">管网预警</div>
                  </div>
                </div>
                <div style="height: 70px"></div>
                <div class="chart-headers">
                  <div class="chart-title">报警设备信息列表</div>
                </div>
                <div class="rain-monitor-table-container">
                  <div class="rain-monitor-table-header">
                    <div class="rain-th th-index1">序号</div>
                    <div class="rain-th th-station1">点位信息</div>
                    <div class="rain-th th-hour1">报警时间</div>
                    <div class="rain-th th-warning1">位置</div>
                    <div class="rain-th th-warning1">阈值</div>
                  </div>
                  <div class="rain-monitor-table-body">
                    <div class="rain-tr" v-for="(row, idx) in bjsbxxlbDATA" :key="row.index">
                      <div class="rain-td th-index1">{{ row.sequence }}</div>
                      <div class="rain-td th-station1">{{ row.point_info }}</div>
                      <div class="rain-td th-hour1">{{ row.alarm_time }}</div>
                      <div class="rain-td th-warning1">{{ row.location }}</div>
                      <div class="rain-td th-warning1">{{ row.threshold }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间区域容器 -->
    <div class="middle-container">
      <div class="middle-section">
        <!-- 标题 -->
        <div class="section-title">内涝安全预警监测综合驾驶舱系统</div>
      </div>
    </div>

    <!-- 右侧区域容器 -->
    <div class="right-container">
      <div class="right-section">
        <!-- 上部分：用户信息和返回门户 -->
        <!-- <div class="user-portal-container">
          <div class="user-info">
            <img class="user-icon" src="https://picsum.photos/200/300" alt="用户" />
            <span class="username">管理员</span>
          </div>
          <div class="portal-back">
            <img class="portal-icon" src="https://picsum.photos/200/300" alt="门户" />
            <span class="portal-text">返回门户</span>
          </div>
        </div> -->

        <!-- 下部分：新增的内容区域 -->
        <div class="right-content">
          <div class="right-video">
            <div class="chart-headers">
              <div class="chart-title">
                视频在线
                <div class="title-value">78/88</div>
              </div>
              <div class="alarm-tabs">
                <div
                  v-for="(tab, index) in viewTabs"
                  :key="index"
                  class="alarm-tab"
                  :class="{ active: vidoTabFlag === tab.value }"
                  @click="changeAlarmTab(tab.value)"
                >
                  {{ tab.name }}
                </div>
              </div>
            </div>
          </div>
          <div class="video-besides">
            <div class="chart-headers">
              <div class="chart-title">调度执行情况</div>
            </div>
            <div class="charts-diaodu" ref="diaoduRef"></div>
            <div style="height: 20px"></div>
            <div class="chart-headers">
              <div class="chart-title">中台指令</div>
              <div class="alarm-tabs">
                <div class="alarm-tab">更多</div>
              </div>
            </div>
            <div class="rain-monitor-table-container-right">
              <div class="rain-monitor-table-header">
                <div class="rain-th th-index1">序号</div>
                <div class="rain-th th-station1">事件内容</div>
                <div class="rain-th th-hour1">时间</div>
                <div class="rain-th th-warning1">地点</div>
                <div class="rain-th th-warning1">事件等级</div>
              </div>
              <div class="rain-monitor-table-body">
                <div class="rain-tr" v-for="(row, idx) in ztzlDATA" :key="row.index">
                  <div class="rain-td th-index1">{{ row.seq }}</div>
                  <div class="rain-td th-station1" :title="row.event">{{ row.event }}</div>
                  <div class="rain-td th-hour1" :title="row.time">{{ row.time }}</div>
                  <div class="rain-td th-warning1">{{ row.location }}</div>
                  <div class="rain-td th-warning1">{{ row.alert_level }}</div>
                </div>
              </div>
            </div>
            <div style="height: 40px"></div>
            <div class="chart-headers">
              <div class="chart-title">协同处置事件</div>
              <div class="alarm-tabs">
                <div class="alarm-tab">雨中快报</div>
              </div>
            </div>
            <div class="rain-monitor-table-container-right-bottom">
              <div class="rain-monitor-table-header">
                <div class="rain-th th-index1">事件处置部门</div>
                <div class="rain-th th-station1">上报人</div>
                <div class="rain-th th-hour1">事件发生时间</div>
                <div class="rain-th th-warning1">事件名称</div>
                <div class="rain-th th-warning2">事件状态</div>
                <div class="rain-th th-warning3">事件位置</div>
              </div>
              <div class="rain-monitor-table-body">
                <div class="rain-tr" v-for="(row, idx) in caseInformationDATA" :key="row.index">
                  <div class="rain-td th-index1">{{ row.eventRepDepartName }}</div>
                  <div class="rain-td th-station1">{{ row.reportUserName }}</div>
                  <div class="rain-td th-hour1">{{ row.rptTime }}</div>
                  <div class="rain-td th-warning1" :title="row.eventName">{{ row.eventName }}</div>
                  <div class="rain-td th-warning2" :title="row.eventStatusName">{{ row.eventStatusName }}</div>
                  <div class="rain-td th-warning3" :title="row.eventGridName">{{ row.eventGridName }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as acapi from '../../assets/ac.min.js'
import * as echarts from 'echarts'
import {
  shortGet,
  jsdxxOnline,
  jsdxxOffline,
  jsdxxRanking,
  bzswGET,
  gwyjGET,
  wzdptjGET,
  bjxxGET,
  bjsbxxlbGET,
  ddzxgkGET,
  ztzlGET,
  caseInfoGET,
  dlqxGET
} from '@/api/intelligence'
// 获取assets静态资源
const getAssetsFile = url => {
  return new URL(`../../assets/images/home/<USER>
}
// 时间和日期
const currentTime = ref('')
const currentDate = ref('')

let timer = null

// 更新时间函数
const updateTime = () => {
  const now = new Date()

  // 格式化时分秒
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  const seconds = now.getSeconds().toString().padStart(2, '0')
  currentTime.value = `${hours}:${minutes}:${seconds}`

  // 格式化年月日
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  currentDate.value = `${year} ${month} ${day}`
}

// 连接数字孪生配置
const options = ref({
  domId: 'player', // 修改为新的ID
  apiOptions: {
    onReady: function () {
      console.info('此时可以调API了')
    }
  }
})

const api = ref(null)
// 太原数字孪生内网地址
const host =
  ref(import.meta.env.VITE_DTS_URL) /
  // 组件挂载时启动定时器和初始化屏幕适配
  onMounted(async () => {
    updateTime() // 立即执行一次
    timer = setInterval(updateTime, 1000) // 每秒更新一次
    // 修改这里，接入地图配置
    try {
      // 确保先引入了ac.min.js
      if (typeof acapi !== 'undefined') {
        // 创建数字孪生平台实例
        console.log('加载飞渡')
        api.value = new DigitalTwinPlayer(host.value, options.value)
        console.log('数字孪生平台初始化成功')
      } else {
        console.error('ac.min.js未正确加载，请检查引入路径')
      }
    } catch (error) {
      console.error('数字孪生平台初始化失败:', error)
    }
  })

// 组件卸载时清除定时器和屏幕适配事件监听
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }

  // 销毁DTS实例
  if (api.value) {
    api.value = null
  }
})

const vidoTabFlag = ref('rain')
const viewTabs = [
  { name: '综治', value: 'rain' },
  { name: '地铁', value: 'water' },
  { name: '易涝点', value: 'flood' },
  { name: '泵站', value: 'beng' }
]
const alarmTabs = [
  { name: '全部', value: 'rain' },
  { name: '在线', value: 'water' },
  { name: '离线', value: 'flood' },
  { name: '积水排行', value: 'rank' }
]
const currentAlarmTab = ref('rain')
const wrapLeftCharts = ref(null)
const wrapLeftChartsInstance = ref(null)
const getLeftFirstCharts = data => {
  if (!wrapLeftCharts.value) return

  // 销毁已存在的图表实例
  if (wrapLeftChartsInstance.value) {
    wrapLeftChartsInstance.value.dispose()
  }

  // 创建新的图表实例
  wrapLeftChartsInstance.value = echarts.init(wrapLeftCharts.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },

    xAxis: {
      type: 'category',
      axisLabel: {
        color: '#fff',
        fontSize: 20
      },
      axisLine: {
        show: false,
        lineStyle: {
          width: 0,
          color: '#D3D3D3'
        }
      },
      data: data.map(item => item.time)
    },
    grid: {
      left: '2%',
      right: '4%',
      bottom: '6%',
      top: '8%',
      containLabel: true
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#fff',
        fontSize: 20
      },
      axisLine: {
        lineStyle: {
          width: 0,
          color: '#D3D3D3'
        }
      }
    },
    series: [
      {
        name: '降雨量',
        data: data.map(item => item.rainfall),
        type: 'line'
      },
      {
        name: '气温',
        data: data.map(item => item.temperature),
        type: 'line'
      }
    ]
  }

  // 设置图表配置并渲染
  wrapLeftChartsInstance.value.setOption(option)

  // 确保图表铺满容器
  //   wrapLeftChartsInstance.value.resize()

  // 添加窗口大小变化监听器
  window.addEventListener('resize', () => {
    if (wrapLeftChartsInstance.value) {
      wrapLeftChartsInstance.value.resize()
    }
  })
}

const rainMonitorTableData = ref()

const displayedVideos = ref([])
// 获取监控视频
const getCameraListData = async () => {
  const result = await getCameraList({ pageSize: 1 }) // 先获取足够多的数据，再前端分页
  // if (result.code === 200) {
  videoData.value = result.rows
  totalVideos.value = videoData.value ? videoData.value.length : 0
  updateDisplayedVideos()
  console.log('getCameraListData监控视频接口', videoData.value)
  // }
}
// 初始化FLV播放器
const initFlvPlayers = () => {
  if (!displayedVideos.value || !displayedVideos.value.length) return

  if (flvjs.isSupported()) {
    // 为每个视频元素创建一个flv播放器
    displayedVideos.value.forEach((item, index) => {
      try {
        // 获取视频元素
        const videoElement = document.getElementById(`video-player-${index}`)
        if (!videoElement) {
          console.error(`无法找到视频元素 #video-player-${index}`)
          return
        }

        // 解析urlData字符串
        let videoUrl = ''
        if (item.urlData) {
          try {
            // 先解析外层JSON
            const urlDataObj = JSON.parse(item.urlData)
            // 再解析内层url字符串
            if (urlDataObj && urlDataObj.url) {
              const urlObj = JSON.parse(urlDataObj.url)
              // 获取flv格式的视频URL
              videoUrl = urlObj.flv
            }
          } catch (error) {
            console.error(`解析视频URL数据失败:`, error)
          }
        }

        // 检查视频URL是否存在
        if (!videoUrl) {
          console.error(`视频 ${index} 没有有效的URL`)
          return
        }

        // 创建flv播放器
        const player = flvjs.createPlayer({
          type: 'flv',
          url: videoUrl
        })

        // 将播放器附加到视频元素
        player.attachMediaElement(videoElement)
        // 加载视频
        player.load()
        // 存储播放器实例以便销毁
        flvPlayers.value[index] = player

        // 添加错误处理
        player.on(flvjs.Events.ERROR, (errorType, errorDetail) => {
          console.error(`视频 ${index} 播放错误:`, errorType, errorDetail)
        })
      } catch (error) {
        console.error(`初始化视频播放器 ${index} 时出错:`, error)
      }
    })
  } else {
    console.error('您的浏览器不支持flv.js')
  }
}

const diaoduRef = ref(null)
const diaoduGetEcharts = data => {
  const chartDiaodu = echarts.init(diaoduRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '12%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: data.map(item => item.name),
        axisTick: {
          alignWithLabel: true
        },
        axisLabel: {
          color: '#fff',
          fontSize: 20
        },
        axisLine: {
          lineStyle: {
            color: '#fff'
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: '#fff',
          fontSize: 20
        }
      }
    ],
    series: [
      {
        name: '调度执行',
        type: 'bar',
        barWidth: '60%',
        data: data.map(item => item.value)
      }
    ]
  }
  chartDiaodu.setOption(option)
}

// 积水点切换
const changejishuipaihang = val => {
  console.log(val, 'val')
  currentAlarmTab.value = val
  if (val === 'rain') {
    rainMonitorTableData.value = JSON.parse(JSON.stringify(jishuiquanbu.value))
  } else if (val === 'water') {
    rainMonitorTableData.value = jishuizaixian.value
  } else if (val === 'flood') {
    rainMonitorTableData.value = jishuilixian.value
  } else if (val === 'rank') {
    rainMonitorTableData.value = jishuipaihang.value
  }
}

const jishuiquanbu = ref([])
const jishuizaixian = ref([])
const jishuilixian = ref([])
const jishuipaihang = ref([])

const bengzhanshuiwei = ref([])
const gwyjDATA = ref([])
const wzdptjDATA = ref([])
const bjxxDATA = ref({})
const bjsbxxlbDATA = ref([])
const ddzxgkDATA = ref([])
const ztzlDATA = ref([])
const caseInformationDATA = ref([])
const dlqxDATA = ref()
onMounted(async () => {
  // 积水点--全部
  shortGet().then(res => {
    jishuiquanbu.value = JSON.parse(res.data[0].value).jsdxx
    rainMonitorTableData.value = JSON.parse(JSON.stringify(jishuiquanbu.value))
  })
  jsdxxOnline().then(res => {
    jishuizaixian.value = JSON.parse(res.data[0].value).jsdxx
  })
  jsdxxOffline().then(res => {
    jishuilixian.value = JSON.parse(res.data[0].value).jsdxx
  })
  jsdxxRanking().then(res => {
    jishuipaihang.value = JSON.parse(res.data[0].value).jsdxx
  })

  bzswGET().then(res => {
    bengzhanshuiwei.value = JSON.parse(res.data[0].value).pump_station_water_level
  })

  gwyjGET().then(res => {
    gwyjDATA.value = JSON.parse(res.data[0].value).data
  })

  wzdptjGET().then(res => {
    wzdptjDATA.value = JSON.parse(res.data[0].value).data
  })

  bjxxGET().then(res => {
    bjxxDATA.value = JSON.parse(res.data[0].value).alarm_information
  })
  bjsbxxlbGET().then(res => {
    bjsbxxlbDATA.value = JSON.parse(res.data[0].value).alarm_device_information
  })

  ddzxgkGET().then(res => {
    ddzxgkDATA.value = JSON.parse(res.data[0].value).scheduling_execution
    setTimeout(() => {
      diaoduGetEcharts(ddzxgkDATA.value)
    }, 10)
  })

  ztzlGET().then(res => {
    ztzlDATA.value = JSON.parse(res.data[0].value).data
  })

  caseInfoGET().then(res => {
    caseInformationDATA.value = JSON.parse(res.data[0].value).data
  })

  dlqxGET().then(res => {
    dlqxDATA.value = JSON.parse(res.data[0].value).data
    setTimeout(() => {
      getLeftFirstCharts(dlqxDATA.value)
    }, 0)
  })

  // 左侧上，第一个图表
  //

  //   await getCameraListData()
  //   initFlvPlayers()
})
</script>

<style lang="scss" scoped>
.chart-headers {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: vh(45);
  margin-bottom: vh(5);
  background: url('@/assets/images/home/<USER>') no-repeat;
  background-size: 100% 100%;
  padding: 0;
  .chart-title {
    font-weight: normal;
    font-size: vh(26);
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 vw(50);
    font-family: JiangChengXieHei;
    color: #d8f1ff;
    .title-value {
      color: #fff;
      font-family: Alibaba-PuHuiTi, sans-serif;
      font-size: 18px;
      font-weight: bold;
      padding-left: 30px;
      padding-top: 12px;
    }
    // @include respond-to("ultra-wide") {
    //   font-size: 18px;
    // }
  }

  .alarm-tabs {
    display: flex;
    height: 100%;
  }

  .alarm-tab {
    padding: 0 vw(20);
    height: 100%;
    display: flex;
    align-items: center;
    color: #fff;
    font-family: JiangChengXieHei, JiangChengXieHei;
    font-weight: normal;
    font-size: vh(20);
    cursor: pointer;
    margin-left: vw(5);
    background: url('@/assets/images/home/<USER>') no-repeat;
    background-size: 100% 100%;

    &.active {
      background: url('@/assets/images/home/<USER>') no-repeat;
      background-size: 100% 100%;
    }
  }
}

.content-left-wrap {
  display: flex;
  height: 100%;
  //   border: 1px solid red;
  height: calc(100% - vh(140));
  .wrap-left {
    width: 66.6%;
    // height: vh(1000);
    // border: 1px solid red;
    .wrap-left-charts {
      height: vh(415);
      //   border: 1px solid yellow;
    }
    .jishui {
      display: flex;
      justify-content: space-between;
      .jishui-left {
        width: 59%;

        .rain-monitor-table-container {
          width: 100%;
          //   height: calc(100% - vh(45));
          //   border: 1px solid red;

          border-radius: vh(8);
          display: flex;
          flex-direction: column;
          box-sizing: border-box;
          overflow: hidden;
          .rain-monitor-table-header {
            display: flex;
            align-items: center;
            min-height: vh(48);
            background: #115a8f;
            color: #fff;
            font-weight: bold;
            font-size: vh(22);
            border-bottom: vh(2) solid #205080;
            font-family: Alibaba-PuHuiTi, sans-serif;
          }
          .rain-th,
          .rain-td {
            text-align: center;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .th-index1 {
            width: 20%;
          }
          .th-station1 {
            width: 20%;
          }
          .th-hour1 {
            width: 20%;
          }
          .th-warning1 {
            width: 40%;
          }

          .rain-monitor-table-body {
            // height: calc(100% - vh(48));
            height: 250px;
            // border: 1px solid red;
            overflow-y: auto;
            -ms-overflow-style: none; /* IE 和 Edge */
            scrollbar-width: none; /* Firefox */
            padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
          }
          .rain-monitor-table-body::-webkit-scrollbar {
            display: none; /* Chrome, Safari 和 Opera */
          }
          .rain-tr {
            display: flex;
            align-items: center;
            min-height: vh(44);
            font-size: vh(22);
            color: #fff;
            border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
            transition: background 0.2s;
          }
          .rain-tr:hover {
            background: rgba(0, 198, 255, 0.1);
          }
          .over-red {
            color: #ff3b3b;
            font-weight: bold;
          }
          .over-blue {
            color: #1a86fc;
            font-weight: bold;
          }
          .level-badge {
            display: inline-block;
            min-width: vw(28);
            padding: vh(2) vw(8);
            border-radius: vh(12);
            font-weight: bold;
            font-size: vh(18);
          }
          .level-red {
            color: #ff3b3b;
          }
          .level-blue {
            color: #1a86fc;
          }
          .level-light {
            color: #7ed6fc;
          }
        }
        .rain-monitor-table-container1 {
          width: 100%;
          //   height: calc(100% - vh(45));
          //   border: 1px solid red;

          border-radius: vh(8);
          display: flex;
          flex-direction: column;
          box-sizing: border-box;
          overflow: hidden;
          .rain-monitor-table-header {
            display: flex;
            align-items: center;
            min-height: vh(48);
            background: #115a8f;
            color: #fff;
            font-weight: bold;
            font-size: vh(22);
            border-bottom: vh(2) solid #205080;
            font-family: Alibaba-PuHuiTi, sans-serif;
          }
          .rain-th,
          .rain-td {
            text-align: center;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
            // overflow: hidden;
            // white-space: nowrap;
            text-overflow: ellipsis;
          }
          .th-index1 {
            width: 33%;
          }
          .th-station1 {
            width: 12%;
          }
          .th-hour1 {
            width: 15%;
          }
          .th-warning1 {
            width: 13%;
          }
          .th-warning2 {
            width: 12%;
          }
          .th-warning3 {
            width: 12%;
          }

          .rain-monitor-table-body {
            height: 230px;
            overflow-y: auto;
            -ms-overflow-style: none; /* IE 和 Edge */
            scrollbar-width: none; /* Firefox */
            padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
          }
          .rain-monitor-table-body::-webkit-scrollbar {
            display: none; /* Chrome, Safari 和 Opera */
          }
          .rain-tr {
            display: flex;
            align-items: center;
            min-height: vh(44);
            font-size: vh(22);
            color: #fff;
            border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
            transition: background 0.2s;
          }
          .rain-tr:hover {
            background: rgba(0, 198, 255, 0.1);
          }
          .over-red {
            color: #ff3b3b;
            font-weight: bold;
          }
          .over-blue {
            color: #1a86fc;
            font-weight: bold;
          }
          .level-badge {
            display: inline-block;
            min-width: vw(28);
            padding: vh(2) vw(8);
            border-radius: vh(12);
            font-weight: bold;
            font-size: vh(18);
          }
          .level-red {
            color: #ff3b3b;
          }
          .level-blue {
            color: #1a86fc;
          }
          .level-light {
            color: #7ed6fc;
          }
        }
      }
      .jishui-right {
        width: 39%;
        .guanwang {
          display: flex;
          .guanwang-wrap {
            flex: 1;
            color: #fff;
            font-size: 22px;
            .guanwang-item {
              height: 100px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
            }
            .guanwang-text {
              text-align: center;
            }
          }
        }
        .rain-monitor-table-container {
          width: 100%;
          //   height: calc(100% - vh(45));
          //   border: 1px solid red;

          border-radius: vh(8);
          display: flex;
          flex-direction: column;
          box-sizing: border-box;
          overflow: hidden;
          .rain-monitor-table-header {
            display: flex;
            align-items: center;
            min-height: vh(48);
            background: #115a8f;
            color: #fff;
            font-weight: bold;
            font-size: vh(22);
            border-bottom: vh(2) solid #205080;
            font-family: Alibaba-PuHuiTi, sans-serif;
          }
          .rain-th,
          .rain-td {
            text-align: center;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .th-index1 {
            width: 20%;
          }
          .th-station1 {
            width: 20%;
          }
          .th-hour1 {
            width: 20%;
          }
          .th-warning1 {
            width: 40%;
          }

          .rain-monitor-table-body {
            height: calc(100% - vh(48));
            overflow-y: auto;
            -ms-overflow-style: none; /* IE 和 Edge */
            scrollbar-width: none; /* Firefox */
            padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
          }
          .rain-monitor-table-body::-webkit-scrollbar {
            display: none; /* Chrome, Safari 和 Opera */
          }
          .rain-tr {
            display: flex;
            align-items: center;
            min-height: vh(44);
            font-size: vh(22);
            color: #fff;
            border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
            transition: background 0.2s;
          }
          .rain-tr:hover {
            background: rgba(0, 198, 255, 0.1);
          }
          .over-red {
            color: #ff3b3b;
            font-weight: bold;
          }
          .over-blue {
            color: #1a86fc;
            font-weight: bold;
          }
          .level-badge {
            display: inline-block;
            min-width: vw(28);
            padding: vh(2) vw(8);
            border-radius: vh(12);
            font-weight: bold;
            font-size: vh(18);
          }
          .level-red {
            color: #ff3b3b;
          }
          .level-blue {
            color: #1a86fc;
          }
          .level-light {
            color: #7ed6fc;
          }
        }
      }
    }
  }
  .wrap-right {
    width: 33.3%;
    // height: vh(400);
    // border: 1px solid red;
    .guanwang {
      display: flex;
      .guanwang-wrap {
        flex: 1;
        color: #fff;
        font-size: 22px;
        .guanwang-item {
          height: 100px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
        .guanwang-text {
          text-align: center;
        }
      }
    }

    .rain-monitor-table-container {
      width: 100%;
      //   height: calc(100% - vh(45));
      //   border: 1px solid red;

      border-radius: vh(8);
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      overflow: hidden;
      .rain-monitor-table-header {
        display: flex;
        align-items: center;
        min-height: vh(48);
        background: #115a8f;
        color: #fff;
        font-weight: bold;
        font-size: vh(22);
        border-bottom: vh(2) solid #205080;
        font-family: Alibaba-PuHuiTi, sans-serif;
      }
      .rain-th,
      .rain-td {
        text-align: center;
        padding: 0;
        margin: 0;
        box-sizing: border-box;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .th-index1 {
        width: 20%;
      }
      .th-station1 {
        width: 20%;
      }
      .th-hour1 {
        width: 20%;
      }
      .th-warning1 {
        width: 20%;
      }

      .rain-monitor-table-body {
        height: calc(100% - vh(48));
        overflow-y: auto;
        -ms-overflow-style: none; /* IE 和 Edge */
        scrollbar-width: none; /* Firefox */
        padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
      }
      .rain-monitor-table-body::-webkit-scrollbar {
        display: none; /* Chrome, Safari 和 Opera */
      }
      .rain-tr {
        display: flex;
        align-items: center;
        min-height: vh(44);
        font-size: vh(22);
        color: #fff;
        border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
        transition: background 0.2s;
      }
      .rain-tr:hover {
        background: rgba(0, 198, 255, 0.1);
      }
      .over-red {
        color: #ff3b3b;
        font-weight: bold;
      }
      .over-blue {
        color: #1a86fc;
        font-weight: bold;
      }
      .level-badge {
        display: inline-block;
        min-width: vw(28);
        padding: vh(2) vw(8);
        border-radius: vh(12);
        font-weight: bold;
        font-size: vh(18);
      }
      .level-red {
        color: #ff3b3b;
      }
      .level-blue {
        color: #1a86fc;
      }
      .level-light {
        color: #7ed6fc;
      }
    }
  }
}

// 屏幕容器
.screen-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

// 背景层
.bg-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  pointer-events: none;
  z-index: 1;
}

.bg-layer-1 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 1;
}

.bg-layer-2 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 2;
}

.bg-layer-3 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 3;
}

// 地图容器样式
.map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: auto;
}

// 拆分为三个独立的容器
.left-container,
.middle-container,
.right-container {
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.left-container {
  left: 0;
  width: 35%;
  // background: blue;
  .left-section {
    width: 100%;
    height: 100%;
    padding: vh(40) vw(0) vh(20) vw(100);
    pointer-events: auto;

    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .time-weather {
    display: flex;
    align-items: center;
    color: #fff;
    padding-left: vw(40);
    background: blue;
    .time-date {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }

    .time {
      font-size: vh(24);
    }

    .date {
      font-size: vh(14);
      margin-right: vw(2);
    }

    .divider {
      margin: 0 vw(20);
      width: vw(1);
      height: vh(30);
      background-color: #3a607c;
    }

    .weather {
      display: flex;
      align-items: center;
    }

    .weather-icon {
      width: vw(24);
      height: vh(24);
      margin-right: vw(10);
    }

    .weather-icon1 {
      width: vw(24);
      height: vh(22);
    }

    .temperature {
      font-size: vh(24);
    }
  }

  .left-content {
    // background: red;
    // background: rgba(255, 255, 255, 0.1);
    width: 100%;
    height: calc(100% - 50px);
    display: flex;
    flex-direction: column;
    margin-top: vh(10);
    gap: vh(20);

    .content-layout-first {
      flex: 1;
      width: 100%;
      // background: rgba(0, 0, 0, 0.2);
      .content-layout-header {
        width: 100%;
        height: vh(140);
        background-image: url('@/assets/images/home/<USER>');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 vw(95);

        .weather-info-bar {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 100%;
          color: #fff;
          padding: 0 vw(15);
        }

        .current-weather {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 100%;
          gap: vw(48);
        }

        .weather-label {
          font-weight: normal;
          font-size: vh(24);
          color: #ffffff;
          line-height: vh(33);
        }

        .city-info {
          display: flex;
          align-items: center;
        }

        .location-icon {
          display: inline-block;
          width: vw(22);
          height: vh(24);
          margin-right: vw(20);
        }

        .city-name {
          font-weight: normal;
          font-size: vh(32);
          color: #ffffff;
          margin-top: vh(-8);
        }

        .weather-detail {
          display: flex;
          align-items: center;
          height: 100%;
          gap: vw(15);
        }

        .weather-icon-large {
          width: vw(58);
          height: vh(61);
          margin-right: vw(10);
        }

        .weather-icon-large img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }

        .weather-data {
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .weather-type {
          display: flex;
          align-items: center;
          gap: vw(10);
        }

        .weather-name {
          font-weight: normal;
          font-size: vh(30);
          color: #ffffff;
          line-height: vh(42);
          text-align: center;
          font-style: normal;
        }

        .weather-temp {
          font-weight: normal;
          font-size: vh(22);
          color: #ffffff;
          line-height: vh(30);
          font-style: normal;
          text-align: center;
          width: vw(72);
          height: vh(31);
          background: #1ecfa5;
          border-radius: vw(16);
        }

        .wind-info {
          display: flex;
          align-items: center;
          gap: vw(20);
          font-weight: normal;
          font-size: vh(27);
          color: #ffffff;
          text-align: left;
          font-style: normal;
          margin-top: vh(3);
        }

        .weather-forecast {
          display: flex;
          height: 100%;
          align-items: center;
        }

        .forecast-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 0 vw(30);
        }

        .forecast-title {
          font-weight: normal;
          font-size: vh(28);
          color: #ffffff;
          text-align: left;
          font-style: normal;
        }

        .forecast-value {
          .forecast-value-num {
            font-weight: bold;
            font-size: vh(33);
            color: #24ceb8;
            font-style: normal;
          }

          .forecast-value-unit {
            font-weight: normal;
            font-size: vh(24);
            color: #ffffff;
            font-style: normal;
          }
        }

        .forecast-value1 {
          .forecast-value-num {
            font-weight: bold;
            font-size: vh(33);
            color: #92d3ec;
            font-style: normal;
          }

          .forecast-value-unit {
            font-weight: normal;
            font-size: vh(24);
            color: #ffffff;
            font-style: normal;
          }
        }

        .publish-info {
          width: vw(395);
          height: vh(77);
          background: rgba(110, 204, 255, 0.14);
          border-radius: vw(1);
          display: flex;
          justify-content: center;
          align-items: center;
          gap: vw(20);
        }

        .publish-label {
          font-weight: normal;
          font-size: vh(25);
          color: #ffffff;
          font-style: normal;
        }

        .publish-times {
          font-weight: normal;
          font-size: vh(22);
          color: #ffffff;
          font-style: normal;
        }

        .time-item {
          font-weight: normal;
          font-size: vh(22);
          color: #ffffff;
          font-style: normal;
        }

        .divider-line {
          width: vw(1);
          height: vh(70);
        }
      }
    }
  }
}

.middle-container {
  left: 46%;
  height: auto;
  .middle-section {
    .section-title {
      font-family: JiangChengXieHei;
      font-size: vh(50);
      margin-top: vh(40);
      color: #d8f1ff;
      text-align: center;
    }
  }
}

.right-container {
  right: 0;
  width: 30%;

  .right-section {
    width: 100%;
    height: 100%;
    padding: vh(40) vw(100) vh(20) vw(0);
    pointer-events: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .user-portal-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: vh(10) vw(20);
    gap: vw(30);
    border-radius: vh(4);
    background: rgba(6, 72, 146, 0.24); // 添加背景色

    .user-info,
    .portal-back {
      display: flex;
      align-items: center;
      gap: vw(10);

      img {
        width: vh(40);
        height: vh(40);
        border-radius: 50%;
        object-fit: cover;
      }

      span {
        color: #fff;
        font-size: vh(16);
      }
    }
  }

  .right-content {
    // padding-top: vh(140);
    background: rgba(255, 255, 255, 0.1);
    border-radius: vh(4);
    width: 100%;
    height: calc(100% - 15px);
    display: flex;
    gap: vw(20);
    .right-video {
      width: 65%;
    }
    .video-besides {
      width: 35%;
      .charts-diaodu {
        height: 300px;
        // border-bottom: 1px solid #aaa;
      }
    }
    .rain-monitor-table-container-right {
      width: 100%;
      //   height: calc(100% - vh(45));
      //   border: 1px solid red;

      border-radius: vh(8);
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      overflow: hidden;
      .rain-monitor-table-header {
        display: flex;
        align-items: center;
        min-height: vh(48);
        background: #115a8f;
        color: #fff;
        font-weight: bold;
        font-size: vh(22);
        border-bottom: vh(2) solid #205080;
        font-family: Alibaba-PuHuiTi, sans-serif;
      }
      .rain-th,
      .rain-td {
        text-align: center;
        padding: 0;
        margin: 0;
        box-sizing: border-box;
        // overflow: hidden;
        white-space: wrap;
        text-overflow: ellipsis;
      }
      .th-index1 {
        width: 14%;
      }
      .th-station1 {
        // padding-right: 20px;
        width: 43%;
      }
      .th-hour1 {
        width: 23%;
      }
      .th-warning1 {
        width: 20%;
      }

      .rain-monitor-table-body {
        height: 314px;
        overflow-y: auto;
        -ms-overflow-style: none; /* IE 和 Edge */
        scrollbar-width: none; /* Firefox */
        padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
      }
      .rain-monitor-table-body::-webkit-scrollbar {
        display: none; /* Chrome, Safari 和 Opera */
      }
      .rain-tr {
        display: flex;
        align-items: center;
        min-height: vh(44);
        font-size: vh(22);
        color: #fff;
        border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
        transition: background 0.2s;
      }
      .rain-tr:hover {
        background: rgba(0, 198, 255, 0.1);
      }
      .over-red {
        color: #ff3b3b;
        font-weight: bold;
      }
      .over-blue {
        color: #1a86fc;
        font-weight: bold;
      }
      .level-badge {
        display: inline-block;
        min-width: vw(28);
        padding: vh(2) vw(8);
        border-radius: vh(12);
        font-weight: bold;
        font-size: vh(18);
      }
      .level-red {
        color: #ff3b3b;
      }
      .level-blue {
        color: #1a86fc;
      }
      .level-light {
        color: #7ed6fc;
      }
    }
    .rain-monitor-table-container-right-bottom {
      width: 100%;
      //   height: calc(100% - vh(45));
      //   border: 1px solid red;

      border-radius: vh(8);
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      overflow: hidden;
      .rain-monitor-table-header {
        display: flex;
        align-items: center;
        min-height: vh(48);
        background: #115a8f;
        color: #fff;
        font-weight: bold;
        font-size: vh(22);
        border-bottom: vh(2) solid #205080;
        font-family: Alibaba-PuHuiTi, sans-serif;
      }
      .rain-th,
      .rain-td {
        text-align: center;
        padding: 0;
        margin: 0;
        box-sizing: border-box;
        // overflow: hidden;
        white-space: wrap;
        text-overflow: ellipsis;
      }
      .th-index1 {
        width: 16.66%;
      }
      .th-station1 {
        width: 16.66%;
      }
      .th-hour1 {
        width: 16.66%;
      }
      .th-warning1 {
        width: 16.66%;
      }
      .th-warning2 {
        width: 16.66%;
      }
      .th-warning3 {
        width: 16.66%;
      }

      .rain-monitor-table-body {
        height: 374px;
        overflow-y: auto;
        -ms-overflow-style: none; /* IE 和 Edge */
        scrollbar-width: none; /* Firefox */
        padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
      }
      .rain-monitor-table-body::-webkit-scrollbar {
        display: none; /* Chrome, Safari 和 Opera */
      }
      .rain-tr {
        display: flex;
        align-items: center;
        min-height: vh(44);
        font-size: vh(22);
        color: #fff;
        border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
        transition: background 0.2s;
      }
      .rain-tr:hover {
        background: rgba(0, 198, 255, 0.1);
      }
      .over-red {
        color: #ff3b3b;
        font-weight: bold;
      }
      .over-blue {
        color: #1a86fc;
        font-weight: bold;
      }
      .level-badge {
        display: inline-block;
        min-width: vw(28);
        padding: vh(2) vw(8);
        border-radius: vh(12);
        font-weight: bold;
        font-size: vh(18);
      }
      .level-red {
        color: #ff3b3b;
      }
      .level-blue {
        color: #1a86fc;
      }
      .level-light {
        color: #7ed6fc;
      }
    }
  }
}
</style>
