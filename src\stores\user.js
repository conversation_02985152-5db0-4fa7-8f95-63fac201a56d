import { defineStore } from "pinia";
import { login, getUserInfo, register } from "../api/user";

export const useUserStore = defineStore("user", {
  state: () => ({
    token: "",
    userInfo: {},
  }),
  getters: {
    isLoggedIn: (state) => !!state.token,
    username: (state) => state.userInfo.username || "",
  },
  actions: {
    async login(credentials) {
      try {
        const response = await login(credentials);
        const { token, user } = response;

        // 保存token和用户信息
        this.token = token;
        this.userInfo = user;

        return Promise.resolve(response);
      } catch (error) {
        return Promise.reject(error);
      }
    },

    async getUserInfo(userId) {
      try {
        const response = await getUserInfo(userId);
        this.userInfo = response;
        return Promise.resolve(response);
      } catch (error) {
        return Promise.reject(error);
      }
    },

    async register(userData) {
      try {
        const response = await register(userData);
        return Promise.resolve(response);
      } catch (error) {
        return Promise.reject(error);
      }
    },

    logout() {
      this.token = "";
      this.userInfo = {};
    },
  },
  persist: {
    key: "user-store",
    storage: localStorage,
    paths: ["token", "userInfo"],
  },
});
