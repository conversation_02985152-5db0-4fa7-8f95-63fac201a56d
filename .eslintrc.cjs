// 在rules里面简单的一些配置：
// "off" 或 0 - 关闭规则
// "warn" 或 1 - 开启规则，使用警告级别的错误
// "error" 或 2 - 开启规则，使用错误级别的错误
module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: [
    'eslint:recommended', // 使用推荐的eslint
    'plugin:vue/vue3-recommended', // 使用插件支持vue3
    // 接入 prettier 的规则
    'plugin:prettier/recommended',
    'eslint-config-prettier'
  ],
  parserOptions: {
    ecmaVersion: 13,
    sourceType: 'module',
    ecmaFeatures: {
      modules: true,
      jsx: true
    },
    requireConfigFile: false,
    parser: '@babel/eslint-parser'
  },
  // eslint-plugin-vue
  plugins: [
    'vue', // 引入vue的插件 vue <==> eslint-plugin-vue
    'prettier' // 引入规范插件  prettier <==>  eslint-plugin-prettier
  ],
  globals: {
    defineProps: 'readonly',
    defineEmits: 'readonly',
    defineExpose: 'readonly',
    withDefaults: 'readonly'
  },
  // 这里时配置规则的,自己看情况配置
  // 这里可以进行自定义规则配置
  // key：规则代号
  // value：具体的限定方式
  // "off" or 0 - 关闭规则
  // "warn" or 1 - 将规则视为一个警告（不会影响退出码）,只警告，不会退出程序
  // "error" or 2 - 将规则视为一个错误 (退出码为1)，报错并退出程序
  rules: {
    // 自定义规则 - 其实上面集成后有很多内置的规则, 这里可以进行规则的一些修改
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off', // 上线环境用打印就报警告, 开发环境关闭此规则
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off', // debugger可以终止代码执行
    'vue/multi-word-component-names': 'off', // 自定义组件名称应该由多单词大驼峰命名组成，防止和html标签冲突 --- 关闭
    'vue/max-attributes-per-line': [
      1,
      // 多个特性的元素应该分多行撰写，每个特性一行
      {
        singleline: 10,
        multiline: {
          max: 1
        }
      }
    ],
    'vue/prop-name-casing': [1, 'camelCase'], // 在声明prop的时候，其命名应该始终使用驼峰命名
    'vue/require-v-for-key': 1, // 给v-for设置键值，与key结合使用，可以高效的更新虚拟DOM
    'vue/no-use-v-if-with-v-for': [
      1,
      {
        allowUsingIterationVar: false
      }
    ],
    // 不要把 v-if 和 v-for 用在同一个元素上——因为v-for 比 v-if 具有更高的优先级
    'vue/order-in-components': [
      1,
      {
        // 组件/实例的选项的顺序
        order: [
          'el',
          'name',
          'parent',
          'functional',
          ['delimiters', 'comments'],
          ['components', 'directives', 'filters'],
          'extends',
          'mixins',
          'inheritAttrs',
          'model',
          ['props', 'propsData'],
          'data',
          'computed',
          'watch',
          'LIFECYCLE_HOOKS',
          'methods',
          ['template', 'render'],
          'renderError'
        ]
      }
    ],
    // //
    // /// js.规范 /
    // /
    'arrow-spacing': [
      1,
      {
        // 在箭头函数之前/之后需要空格
        before: true,
        after: true
      }
    ],
    camelcase: [
      0,
      {
        // 需要驼峰命名
        properties: 'always'
      }
    ],
    'comma-dangle': [0, 'never'], // 要求或禁止使用尾随逗号；最后一个属性是不需要逗号
    'comma-spacing': [
      1,
      {
        // 强制逗号旁边的间距： 左右一个空格
        before: false,
        after: true
      }
    ],
    'comma-style': [1, 'last'], // 逗号风格
    'constructor-super': 1, // 构建方法中使用super方法
    curly: [1, 'multi-line'],
    'dot-location': [1, 'property'], // 在dot之前和之后强制换行
    'eol-last': 1, // 在文件末尾要求或禁止换行
    eqeqeq: [1, 'always', { null: 'ignore' }], // 是否使用全等
    indent: [
      'off',
      2,
      {
        // 强制执行一致的缩进
        SwitchCase: 1
      }
    ],
    'jsx-quotes': [1, 'prefer-single'], // 强制在JSX文件中一致使用单引号
    'keyword-spacing': [
      1,
      {
        // 关键字前后强制执行一致的间距
        before: true,
        after: true
      }
    ],
    'new-cap': [
      1,
      {
        // 要求构造函数名称以大写字母开头
        newIsCap: true,
        capIsNew: false
      }
    ],
    'new-parens': 1, // 调用不带参数的函数时需要括号
    'no-array-constructor': 1, // 禁止阵列构建器
    'no-class-assign': 1, // 禁止修改类声明的变量
    'no-cond-assign': 1, // 在条件语句中禁止赋值运算符
    'no-const-assign': 1, // 禁止修改使用const声明的变量
    'no-control-regex': 0, // 禁止正则表达式中的控制字符
    'no-delete-var': 1, // 禁止删除变量
    'no-dupe-args': 1, // 在函数定义中禁止重复参数
    'no-dupe-class-members': 1, // 禁止在类成员中重复名称
    'no-dupe-keys': 1, // 禁止对象重复声明属性
    'no-duplicate-case': 1, // 规则禁止重复案例标签
    'no-empty-character-class': 1, // 禁止在正则表达式中使用空字符类
    'no-empty-pattern': 1, // 不允许空的解构模式
    'no-eval': 1, // 禁止使用eval（）
    'no-ex-assign': 1, // 禁止在catch子句中重新分配异常
    'no-extend-native': 1, // 禁止扩展原生对象
    'no-extra-bind': 1, // 禁止不必要的功能绑定
    'no-extra-boolean-cast': 1, // 禁止不必要的布尔类型转换
    'no-extra-parens': [1, 'functions'], // 禁止不必要的括号
    'no-func-assign': 1, // 禁止重新分配函数声明
    'no-implied-eval': 1,
    'no-inner-declarations': [1, 'functions'], // 禁止嵌套块中的变量或函数声明
    'no-invalid-regexp': 1, // 禁止在RegExp中使用无效的正则表达式字符串
    'no-irregular-whitespace': 1, // 不允许不规则的空白
    'no-iterator': 1, // 禁止迭代器
    'no-label-var': 1, // 禁止变量名称的标签
    'no-labels': [
      1,
      {
        allowLoop: false,
        allowSwitch: false
      }
    ],
    'no-lone-blocks': 1, // 禁止不必要的嵌套块
    'no-mixed-spaces-and-tabs': 1, // 禁止使用混合空格和制表符进行缩进
    'no-multi-spaces': 1, // 禁止多个空格
    'no-multi-str': 1, // 禁止多行字符串
    'no-multiple-empty-lines': [
      1,
      {
        // 禁止多个空行
        max: 1
      }
    ],
    'no-native-reassign': 1,
    'no-negated-in-lhs': 1,
    'no-new-object': 1,
    'no-new-require': 1,
    'no-new-symbol': 1,
    'no-new-wrappers': 1,
    'no-obj-calls': 1,
    'no-octal': 1,
    'no-octal-escape': 1,
    'no-path-concat': 1,
    'no-proto': 1,
    'no-redeclare': 1,
    'no-regex-spaces': 1,
    'no-return-assign': [1, 'except-parens'],
    'no-self-assign': 1,
    'no-self-compare': 1,
    'no-sequences': 1,
    'no-shadow-restricted-names': 1,
    'no-spaced-func': 1,
    'no-sparse-arrays': 1,
    'no-this-before-super': 1,
    'no-throw-literal': 1,
    'no-trailing-spaces': 1,
    'no-undef': 0,
    'no-undef-init': 1,
    'no-unexpected-multiline': 1,
    'no-unmodified-loop-condition': 1, // 禁止未修改的循环条件
    'no-unneeded-ternary': [
      1,
      {
        // 当存在更简单的替代方案时，不允许三元运算符
        defaultAssignment: false
      }
    ],
    'no-unreachable': 1, // 返回，抛出，继续和中断语句后禁止无法访问的代码
    'no-unsafe-finally': 1, // 禁止finally块中的控制流语句
    'no-unused-vars': [
      1,
      {
        // 禁止使用未声明的变量
        vars: 'all',
        args: 'none'
      }
    ],
    'no-useless-call': 1, // 禁止不必要的call()和apply()方法
    'no-useless-computed-key': 1, // 禁止在对象上使用不必要的计算属性键
    'no-useless-constructor': 1, // 禁止不必要的构造方法
    'no-useless-escape': 0, // 禁止不必要的转义用法
    'no-whitespace-before-property': 1, // 在属性之前禁止空格
    'no-with': 1,
    'linebreak-style': [0, 'error', 'windows'],
    'one-var': [
      1,
      {
        initialized: 'never'
      }
    ],
    'operator-linebreak': [
      1,
      'after',
      {
        // 为维护强制执行一致的换行方式
        overrides: {
          '?': 'before',
          ':': 'before'
        }
      }
    ],
    'padded-blocks': [1, 'never'], // 在块内要求或禁止填充
    quotes: [
      1,
      'single',
      {
        avoidEscape: true,
        allowTemplateLiterals: true
      }
    ],
    semi: [1, 'never'],
    'semi-spacing': [
      1,
      {
        before: false,
        after: true
      }
    ],
    'space-before-blocks': [1, 'always'], // 不要存在多余的块空间
    'space-in-parens': [1, 'never'],
    'space-infix-ops': 1,
    'space-unary-ops': [
      1,
      {
        words: true,
        nonwords: false
      }
    ],
    'spaced-comment': [
      1,
      'always',
      {
        markers: ['global', 'globals', 'eslint', 'eslint-disable', '*package', '!', ',']
      }
    ],
    'template-curly-spacing': [1, 'never'],
    'use-isnan': 1,
    'valid-typeof': 1,
    'wrap-iife': [1, 'any'],
    'yield-star-spacing': [1, 'both'],
    yoda: [1, 'never'],
    'prefer-const': 1,
    'object-curly-spacing': [
      1,
      'always',
      {
        objectsInObjects: false
      }
    ],
    'array-bracket-spacing': [1, 'never'],
    'prettier/prettier': ['warn', { endOfLine: 'auto' }] // 忽略换行格式的检查
  }
}
